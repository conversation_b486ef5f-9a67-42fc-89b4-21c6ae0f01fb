"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notifySecurityAlert = exports.notifyEventReminder = exports.notifyCampaignDeadline = exports.notifyCampaignMilestone = exports.notifyDonationSuccess = exports.notifyAdminMessage = exports.notifyNewEvent = exports.notifyNewCampaign = exports.notifyCommentDeleted = exports.notifyPostDeleted = exports.notifyEventDeleted = exports.notifyCampaignDeleted = exports.notifyNewComment = exports.deleteNotification = exports.createCommentReportNotificationForAdmins = exports.markAllNotificationsAsRead = exports.createReportNotificationForAdmins = exports.markNotificationAsRead = exports.getUnreadNotifications = exports.createNotification = void 0;
const Notification_1 = require("../models/Notification");
const user_model_1 = require("../models/user.model");
const emailService_1 = require("./emailService");
const index_1 = require("../index");
const createNotification = (params) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const notification = new Notification_1.Notification(Object.assign(Object.assign({}, params), { isRead: false, emailSent: false, priority: params.priority || 'medium' }));
        yield notification.save();
        // Emit real-time notification
        index_1.io.to(`user-${params.userId}`).emit('new_notification', notification);
        // Send email if required and user has email notifications enabled
        if (params.sendEmail !== false) {
            yield sendNotificationEmail(notification);
        }
        return notification;
    }
    catch (error) {
        console.error('Error creating notification:', error);
        throw error;
    }
});
exports.createNotification = createNotification;
// Send notification email
const sendNotificationEmail = (notification) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const user = yield user_model_1.User.findById(notification.userId);
        if (!user || !((_a = user.preferences) === null || _a === void 0 ? void 0 : _a.emailNotifications)) {
            return;
        }
        // Determine if this notification type should send email
        const emailTypes = ['campaign', 'event', 'system', 'admin'];
        if (!emailTypes.includes(notification.type)) {
            return;
        }
        yield (0, emailService_1.sendEmail)({
            to: user.email,
            subject: notification.title,
            template: 'notification',
            data: {
                userName: user.name,
                title: notification.title,
                message: notification.message,
                type: notification.type,
                priority: notification.priority,
                createdAt: notification.createdAt
            }
        });
        // Mark email as sent
        yield Notification_1.Notification.findByIdAndUpdate(notification._id, { emailSent: true });
    }
    catch (error) {
        console.error('Error sending notification email:', error);
    }
});
const getUnreadNotifications = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield Notification_1.Notification.find({ userId, isRead: false })
            .sort({ createdAt: -1 })
            .limit(50);
    }
    catch (error) {
        console.error('Error getting unread notifications:', error);
        throw error;
    }
});
exports.getUnreadNotifications = getUnreadNotifications;
const markNotificationAsRead = (notificationId, userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield Notification_1.Notification.findOneAndUpdate({ _id: notificationId, userId }, { isRead: true }, { new: true });
    }
    catch (error) {
        console.error('Error marking notification as read:', error);
        throw error;
    }
});
exports.markNotificationAsRead = markNotificationAsRead;
const createReportNotificationForAdmins = (reportId, postId, reporterName, reason) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Import User model to find admins
        const { User } = yield Promise.resolve().then(() => __importStar(require('../models/user.model')));
        // Find all admin users
        const admins = yield User.find({ role: 'admin' }).select('_id');
        // Create notification for each admin
        const notifications = yield Promise.all(admins.map(admin => (0, exports.createNotification)({
            userId: admin._id,
            type: 'report',
            title: 'Báo cáo bài viết mới',
            message: `${reporterName} đã báo cáo một bài viết với lý do: ${reason}`,
            data: {
                reportId,
                postId
            }
        })));
        return notifications;
    }
    catch (error) {
        console.error('Error creating report notifications for admins:', error);
        throw error;
    }
});
exports.createReportNotificationForAdmins = createReportNotificationForAdmins;
const markAllNotificationsAsRead = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield Notification_1.Notification.updateMany({ userId, isRead: false }, { isRead: true });
    }
    catch (error) {
        console.error('Error marking all notifications as read:', error);
        throw error;
    }
});
exports.markAllNotificationsAsRead = markAllNotificationsAsRead;
const createCommentReportNotificationForAdmins = (reportId, commentId, postId, reporterName, reason) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Import User model to find admins
        const { User } = yield Promise.resolve().then(() => __importStar(require('../models/user.model')));
        // Find all admin users
        const admins = yield User.find({ role: 'admin' }).select('_id');
        // Create notification for each admin
        const notifications = yield Promise.all(admins.map(admin => (0, exports.createNotification)({
            userId: admin._id,
            type: 'report',
            title: 'Báo cáo comment mới',
            message: `${reporterName} đã báo cáo một comment với lý do: ${reason}`,
            data: {
                reportId,
                commentId,
                postId
            }
        })));
        return notifications;
    }
    catch (error) {
        console.error('Error creating comment report notifications for admins:', error);
        throw error;
    }
});
exports.createCommentReportNotificationForAdmins = createCommentReportNotificationForAdmins;
const deleteNotification = (notificationId, userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield Notification_1.Notification.findOneAndDelete({ _id: notificationId, userId });
    }
    catch (error) {
        console.error('Error deleting notification:', error);
        throw error;
    }
});
exports.deleteNotification = deleteNotification;
// Notification helpers for specific events
const notifyNewComment = (postOwnerId, commenterName, postId) => __awaiter(void 0, void 0, void 0, function* () {
    return (0, exports.createNotification)({
        userId: postOwnerId,
        type: 'comment',
        title: 'Bình luận mới',
        message: `${commenterName} đã bình luận vào bài viết của bạn`,
        data: {
            postId,
            actionType: 'created',
            relatedUserName: commenterName
        },
        priority: 'low',
        sendEmail: false // Don't send email for comments
    });
});
exports.notifyNewComment = notifyNewComment;
const notifyCampaignDeleted = (donorIds, campaignTitle, campaignId, reason) => __awaiter(void 0, void 0, void 0, function* () {
    const notifications = donorIds.map(donorId => (0, exports.createNotification)({
        userId: donorId,
        type: 'campaign',
        title: 'Chiến dịch đã bị hủy',
        message: `Chiến dịch "${campaignTitle}" đã bị hủy. Vui lòng liên hệ để được hoàn tiền.${reason ? ` Lý do: ${reason}` : ''}`,
        data: {
            campaignId,
            actionType: 'cancelled',
            reason
        },
        priority: 'high',
        sendEmail: true
    }));
    return Promise.all(notifications);
});
exports.notifyCampaignDeleted = notifyCampaignDeleted;
const notifyEventDeleted = (participantIds, eventTitle, eventId, reason) => __awaiter(void 0, void 0, void 0, function* () {
    const notifications = participantIds.map(participantId => (0, exports.createNotification)({
        userId: participantId,
        type: 'event',
        title: 'Sự kiện đã bị hủy',
        message: `Sự kiện "${eventTitle}" đã bị hủy. Bạn không cần tham gia sự kiện này.${reason ? ` Lý do: ${reason}` : ''}`,
        data: {
            eventId,
            actionType: 'cancelled',
            reason
        },
        priority: 'high',
        sendEmail: true
    }));
    return Promise.all(notifications);
});
exports.notifyEventDeleted = notifyEventDeleted;
const notifyPostDeleted = (userId, postTitle, reason) => __awaiter(void 0, void 0, void 0, function* () {
    return (0, exports.createNotification)({
        userId,
        type: 'post',
        title: 'Bài viết đã bị xóa',
        message: `Bài viết "${postTitle}" của bạn đã bị xóa bởi quản trị viên. Lý do: ${reason}`,
        data: {
            actionType: 'deleted',
            reason
        },
        priority: 'medium',
        sendEmail: false
    });
});
exports.notifyPostDeleted = notifyPostDeleted;
const notifyCommentDeleted = (userId, commentContent, reason) => __awaiter(void 0, void 0, void 0, function* () {
    return (0, exports.createNotification)({
        userId,
        type: 'comment',
        title: 'Bình luận đã bị xóa',
        message: `Bình luận "${commentContent.substring(0, 50)}..." của bạn đã bị xóa bởi quản trị viên. Lý do: ${reason}`,
        data: {
            actionType: 'deleted',
            reason
        },
        priority: 'medium',
        sendEmail: false
    });
});
exports.notifyCommentDeleted = notifyCommentDeleted;
const notifyNewCampaign = (userIds, campaignTitle, campaignId) => __awaiter(void 0, void 0, void 0, function* () {
    const notifications = userIds.map(userId => (0, exports.createNotification)({
        userId,
        type: 'campaign',
        title: 'Chiến dịch mới',
        message: `Chiến dịch mới "${campaignTitle}" đã được tạo. Hãy tham gia ủng hộ!`,
        data: {
            campaignId,
            actionType: 'created'
        },
        priority: 'medium',
        sendEmail: true
    }));
    return Promise.all(notifications);
});
exports.notifyNewCampaign = notifyNewCampaign;
const notifyNewEvent = (userIds, eventTitle, eventId) => __awaiter(void 0, void 0, void 0, function* () {
    const notifications = userIds.map(userId => (0, exports.createNotification)({
        userId,
        type: 'event',
        title: 'Sự kiện mới',
        message: `Sự kiện mới "${eventTitle}" đã được tạo. Hãy đăng ký tham gia!`,
        data: {
            eventId,
            actionType: 'created'
        },
        priority: 'medium',
        sendEmail: true
    }));
    return Promise.all(notifications);
});
exports.notifyNewEvent = notifyNewEvent;
const notifyAdminMessage = (userIds, title, message) => __awaiter(void 0, void 0, void 0, function* () {
    const notifications = userIds.map(userId => (0, exports.createNotification)({
        userId,
        type: 'admin',
        title,
        message,
        data: {
            actionType: 'created'
        },
        priority: 'high',
        sendEmail: true
    }));
    return Promise.all(notifications);
});
exports.notifyAdminMessage = notifyAdminMessage;
const notifyDonationSuccess = (donorEmail_1, donorName_1, amount_1, campaignTitle_1, campaignId_1, transactionId_1, ...args_1) => __awaiter(void 0, [donorEmail_1, donorName_1, amount_1, campaignTitle_1, campaignId_1, transactionId_1, ...args_1], void 0, function* (donorEmail, donorName, amount, campaignTitle, campaignId, transactionId, isAnonymous = false, userId) {
    try {
        // Send email notification
        yield (0, emailService_1.sendEmail)({
            to: donorEmail,
            subject: 'Cảm ơn bạn đã quyên góp - KeyDyWeb',
            template: 'donation-thank-you',
            data: {
                donorName: isAnonymous ? 'Người ủng hộ ẩn danh' : donorName,
                amount: new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                }).format(amount),
                campaignTitle,
                transactionId,
                donationDate: new Date().toLocaleDateString('vi-VN'),
                isAnonymous
            }
        });
        console.log('📧 [Donation] Thank you email sent to:', donorEmail);
        // Send in-app notification if user is logged in
        if (userId) {
            yield (0, exports.createNotification)({
                userId,
                type: 'donation',
                title: 'Cảm ơn bạn đã quyên góp!',
                message: `Cảm ơn bạn đã quyên góp ${new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                }).format(amount)} cho chiến dịch "${campaignTitle}". Sự đóng góp của bạn sẽ tạo nên những thay đổi tích cực!`,
                data: {
                    campaignId,
                    amount,
                    actionType: 'completed',
                    status: 'success'
                },
                priority: 'medium',
                sendEmail: false // Already sent email above
            });
            console.log('📱 [Donation] In-app notification sent to user:', userId);
        }
    }
    catch (error) {
        console.error('❌ [Donation] Error sending thank you notifications:', error);
        throw error;
    }
});
exports.notifyDonationSuccess = notifyDonationSuccess;
// Campaign milestone notifications
const notifyCampaignMilestone = (donorIds, campaignTitle, campaignId, milestone, currentAmount, targetAmount) => __awaiter(void 0, void 0, void 0, function* () {
    const notifications = donorIds.map(donorId => (0, exports.createNotification)({
        userId: donorId,
        type: 'campaign',
        title: 'Chiến dịch đạt mốc quan trọng! 🎉',
        message: `Chiến dịch "${campaignTitle}" đã đạt ${milestone}% mục tiêu (${new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(currentAmount)}/${new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(targetAmount)}). Cảm ơn sự đóng góp của bạn!`,
        data: {
            campaignId,
            actionType: 'updated',
            amount: currentAmount
        },
        priority: 'medium',
        sendEmail: true
    }));
    return Promise.all(notifications);
});
exports.notifyCampaignMilestone = notifyCampaignMilestone;
// Campaign deadline reminder
const notifyCampaignDeadline = (userIds, campaignTitle, campaignId, daysLeft) => __awaiter(void 0, void 0, void 0, function* () {
    const notifications = userIds.map(userId => (0, exports.createNotification)({
        userId,
        type: 'campaign',
        title: 'Chiến dịch sắp kết thúc! ⏰',
        message: `Chiến dịch "${campaignTitle}" chỉ còn ${daysLeft} ngày để kết thúc. Hãy nhanh tay ủng hộ!`,
        data: {
            campaignId,
            actionType: 'reminder'
        },
        priority: 'high',
        sendEmail: true
    }));
    return Promise.all(notifications);
});
exports.notifyCampaignDeadline = notifyCampaignDeadline;
// Event reminder notifications
const notifyEventReminder = (participantIds, eventTitle, eventId, eventDate, reminderType) => __awaiter(void 0, void 0, void 0, function* () {
    const reminderMessages = {
        '7days': 'Sự kiện sẽ diễn ra trong 7 ngày tới',
        '1day': 'Sự kiện sẽ diễn ra vào ngày mai',
        '1hour': 'Sự kiện sẽ bắt đầu trong 1 giờ nữa'
    };
    const priorities = {
        '7days': 'medium',
        '1day': 'high',
        '1hour': 'urgent'
    };
    const notifications = participantIds.map(participantId => (0, exports.createNotification)({
        userId: participantId,
        type: 'event',
        title: 'Nhắc nhở sự kiện 📅',
        message: `${reminderMessages[reminderType]}. Sự kiện "${eventTitle}" - ${eventDate.toLocaleDateString('vi-VN')}. Đừng quên tham gia nhé!`,
        data: {
            eventId,
            actionType: 'reminder'
        },
        priority: priorities[reminderType],
        sendEmail: reminderType !== '7days' // Send email for 1day and 1hour reminders
    }));
    return Promise.all(notifications);
});
exports.notifyEventReminder = notifyEventReminder;
// User account security notifications
const notifySecurityAlert = (userId, alertType, details) => __awaiter(void 0, void 0, void 0, function* () {
    const titles = {
        'login': 'Đăng nhập từ thiết bị mới',
        'password_change': 'Mật khẩu đã được thay đổi',
        'email_change': 'Email đã được thay đổi'
    };
    const messages = {
        'login': `Tài khoản của bạn vừa được đăng nhập từ: ${details}. Nếu không phải bạn, hãy thay đổi mật khẩu ngay.`,
        'password_change': `Mật khẩu tài khoản của bạn đã được thay đổi lúc ${details}. Nếu không phải bạn, hãy liên hệ hỗ trợ ngay.`,
        'email_change': `Email tài khoản của bạn đã được thay đổi thành ${details}. Nếu không phải bạn, hãy liên hệ hỗ trợ ngay.`
    };
    return (0, exports.createNotification)({
        userId,
        type: 'system',
        title: titles[alertType],
        message: messages[alertType],
        data: {
            actionType: 'updated'
        },
        priority: 'urgent',
        sendEmail: true
    });
});
exports.notifySecurityAlert = notifySecurityAlert;
