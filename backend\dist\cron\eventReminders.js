"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startEventReminderJob = exports.eventReminderJob = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const mongoose_1 = __importDefault(require("mongoose"));
const Event_1 = __importDefault(require("../models/Event"));
const EventRegistration_1 = __importDefault(require("../models/EventRegistration"));
const notification_service_1 = require("../services/notification.service");
// Run every hour to check for event reminders
exports.eventReminderJob = node_cron_1.default.createTask('0 * * * *', () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🔔 [Cron] Running event reminder job...');
        const now = new Date();
        const oneHourFromNow = new Date(now.getTime() + (60 * 60 * 1000));
        const oneDayFromNow = new Date(now.getTime() + (24 * 60 * 60 * 1000));
        const sevenDaysFromNow = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
        // Find events starting in 1 hour
        const eventsIn1Hour = yield Event_1.default.find({
            status: 'upcoming',
            eventDate: {
                $gte: now,
                $lte: oneHourFromNow
            }
        });
        // Find events starting in 1 day (check within 1-hour window around 24 hours)
        const eventsIn1Day = yield Event_1.default.find({
            status: 'upcoming',
            eventDate: {
                $gte: oneDayFromNow,
                $lte: new Date(oneDayFromNow.getTime() + (60 * 60 * 1000))
            }
        });
        // Find events starting in 7 days (check within 1-hour window around 7 days)
        const eventsIn7Days = yield Event_1.default.find({
            status: 'upcoming',
            eventDate: {
                $gte: sevenDaysFromNow,
                $lte: new Date(sevenDaysFromNow.getTime() + (60 * 60 * 1000))
            }
        });
        // Process 1-hour reminders
        for (const event of eventsIn1Hour) {
            const registrations = yield EventRegistration_1.default.find({
                eventId: event._id,
                status: 'registered'
            }).distinct('userId');
            if (registrations.length > 0) {
                yield (0, notification_service_1.notifyEventReminder)(registrations, event.title, new mongoose_1.default.Types.ObjectId(event._id), event.eventDate, '1hour');
                console.log(`📧 [Cron] 1-hour reminder sent for event: ${event.title} (${registrations.length} participants)`);
            }
        }
        // Process 1-day reminders
        for (const event of eventsIn1Day) {
            const registrations = yield EventRegistration_1.default.find({
                eventId: event._id,
                status: 'registered'
            }).distinct('userId');
            if (registrations.length > 0) {
                yield (0, notification_service_1.notifyEventReminder)(registrations, event.title, new mongoose_1.default.Types.ObjectId(event._id), event.eventDate, '1day');
                console.log(`📧 [Cron] 1-day reminder sent for event: ${event.title} (${registrations.length} participants)`);
            }
        }
        // Process 7-day reminders
        for (const event of eventsIn7Days) {
            const registrations = yield EventRegistration_1.default.find({
                eventId: event._id,
                status: 'registered'
            }).distinct('userId');
            if (registrations.length > 0) {
                yield (0, notification_service_1.notifyEventReminder)(registrations, event.title, new mongoose_1.default.Types.ObjectId(event._id), event.eventDate, '7days');
                console.log(`📧 [Cron] 7-day reminder sent for event: ${event.title} (${registrations.length} participants)`);
            }
        }
        const totalEvents = eventsIn1Hour.length + eventsIn1Day.length + eventsIn7Days.length;
        console.log(`✅ [Cron] Event reminders completed. Processed ${totalEvents} events`);
    }
    catch (error) {
        console.error('❌ [Cron] Error in event reminder job:', error);
    }
}));
// Start the cron job
const startEventReminderJob = () => {
    exports.eventReminderJob.start();
    console.log('⏰ [Cron] Event reminder job started (hourly)');
};
exports.startEventReminderJob = startEventReminderJob;
