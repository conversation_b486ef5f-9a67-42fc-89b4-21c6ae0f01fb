import React, { useState, useEffect } from 'react';
import { X, <PERSON>, Send, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface EventReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  eventId: string;
  eventTitle: string;
  onReviewSubmitted?: () => void;
}

interface ReviewData {
  rating: number;
  comment: string;
  aspects: {
    organization: number;
    content: number;
    venue: number;
    communication: number;
  };
  isAnonymous: boolean;
}

const EventReviewModal: React.FC<EventReviewModalProps> = ({
  isOpen,
  onClose,
  eventId,
  eventTitle,
  onReviewSubmitted
}) => {
  const [reviewData, setReviewData] = useState<ReviewData>({
    rating: 0,
    comment: '',
    aspects: {
      organization: 0,
      content: 0,
      venue: 0,
      communication: 0
    },
    isAnonymous: false
  });
  const [loading, setLoading] = useState(false);
  const [existingReview, setExistingReview] = useState<any>(null);

  useEffect(() => {
    if (isOpen) {
      checkExistingReview();
    }
  }, [isOpen, eventId]);

  const checkExistingReview = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/events/${eventId}/reviews/my`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success && response.data.review) {
        const review = response.data.review;
        setExistingReview(review);
        setReviewData({
          rating: review.rating,
          comment: review.comment,
          aspects: review.aspects,
          isAnonymous: review.isAnonymous
        });
      }
    } catch (error: any) {
      // User hasn't reviewed yet, which is fine
      console.log('No existing review found');
    }
  };

  const handleStarClick = (rating: number, aspect?: keyof ReviewData['aspects']) => {
    if (aspect) {
      setReviewData(prev => ({
        ...prev,
        aspects: {
          ...prev.aspects,
          [aspect]: rating
        }
      }));
    } else {
      setReviewData(prev => ({ ...prev, rating }));
    }
  };

  const renderStars = (currentRating: number, onStarClick: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onStarClick(star)}
            className={`transition-colors ${
              star <= currentRating
                ? 'text-yellow-400 hover:text-yellow-500'
                : 'text-gray-300 hover:text-yellow-300'
            }`}
          >
            <Star className="h-6 w-6 fill-current" />
          </button>
        ))}
      </div>
    );
  };

  const validateForm = () => {
    if (reviewData.rating === 0) {
      toast.error('Vui lòng chọn đánh giá tổng thể');
      return false;
    }
    if (reviewData.comment.trim().length < 10) {
      toast.error('Bình luận phải có ít nhất 10 ký tự');
      return false;
    }
    if (Object.values(reviewData.aspects).some(rating => rating === 0)) {
      toast.error('Vui lòng đánh giá tất cả các khía cạnh');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      if (existingReview) {
        // Update existing review
        await axios.put(`${API_URL}/api/events/reviews/${existingReview._id}`, reviewData, {
          headers: { Authorization: `Bearer ${token}` }
        });
        toast.success('Cập nhật đánh giá thành công');
      } else {
        // Create new review
        await axios.post(`${API_URL}/api/events/${eventId}/reviews`, reviewData, {
          headers: { Authorization: `Bearer ${token}` }
        });
        toast.success('Gửi đánh giá thành công');
      }
      
      onReviewSubmitted?.();
      onClose();
    } catch (error: any) {
      console.error('Error submitting review:', error);
      toast.error(error.response?.data?.message || 'Có lỗi xảy ra khi gửi đánh giá');
    } finally {
      setLoading(false);
    }
  };

  const aspectLabels = {
    organization: 'Tổ chức sự kiện',
    content: 'Nội dung chương trình',
    venue: 'Địa điểm & cơ sở vật chất',
    communication: 'Giao tiếp & hỗ trợ'
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {existingReview ? 'Chỉnh sửa đánh giá' : 'Đánh giá sự kiện'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Event Info */}
          <div className="text-center">
            <h4 className="font-medium text-gray-900 mb-2">{eventTitle}</h4>
            <p className="text-sm text-gray-600">
              Chia sẻ trải nghiệm của bạn để giúp cải thiện các sự kiện tương lai
            </p>
          </div>

          {/* Overall Rating */}
          <div className="text-center">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Đánh giá tổng thể *
            </label>
            {renderStars(reviewData.rating, (rating) => handleStarClick(rating))}
            <p className="text-xs text-gray-500 mt-2">
              {reviewData.rating > 0 && (
                <>
                  {reviewData.rating === 1 && 'Rất không hài lòng'}
                  {reviewData.rating === 2 && 'Không hài lòng'}
                  {reviewData.rating === 3 && 'Bình thường'}
                  {reviewData.rating === 4 && 'Hài lòng'}
                  {reviewData.rating === 5 && 'Rất hài lòng'}
                </>
              )}
            </p>
          </div>

          {/* Aspect Ratings */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">
              Đánh giá chi tiết *
            </label>
            <div className="space-y-4">
              {Object.entries(aspectLabels).map(([key, label]) => (
                <div key={key} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 flex-1">{label}</span>
                  <div className="flex items-center space-x-2">
                    {renderStars(
                      reviewData.aspects[key as keyof ReviewData['aspects']], 
                      (rating) => handleStarClick(rating, key as keyof ReviewData['aspects'])
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Comment */}
          <div>
            <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-2">
              Bình luận chi tiết *
            </label>
            <textarea
              id="comment"
              name="comment"
              value={reviewData.comment}
              onChange={(e) => setReviewData(prev => ({ ...prev, comment: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Chia sẻ trải nghiệm của bạn về sự kiện này..."
              minLength={10}
              maxLength={1000}
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              {reviewData.comment.length}/1000 ký tự (tối thiểu 10 ký tự)
            </p>
          </div>

          {/* Anonymous Option */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="anonymous"
              checked={reviewData.isAnonymous}
              onChange={(e) => setReviewData(prev => ({ ...prev, isAnonymous: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="anonymous" className="ml-2 flex items-center text-sm text-gray-700">
              {reviewData.isAnonymous ? <EyeOff className="h-4 w-4 mr-1" /> : <Eye className="h-4 w-4 mr-1" />}
              Đánh giá ẩn danh
            </label>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              {existingReview ? 'Cập nhật đánh giá' : 'Gửi đánh giá'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EventReviewModal;
