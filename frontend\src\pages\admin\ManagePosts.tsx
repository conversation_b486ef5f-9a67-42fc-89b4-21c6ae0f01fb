import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { usePostsContext } from '../../contexts/PostsContext';
import {
  Search,
  Eye,
  Trash2,
  UserX,
  AlertTriangle,
  Calendar,
  MessageSquare,
  Heart,
  Share,
  ChevronDown,
  CheckCircle,
  XCircle
} from 'lucide-react';
import ReportNotifications from '../../components/admin/ReportNotifications';

const ManagePosts: React.FC = () => {
  const { posts: communityPosts, deletePost: deleteCommunityPost } = usePostsContext();
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all'); // all, reported, recent
  const [selectedPost, setSelectedPost] = useState<any>(null);
  const [showPostDetail, setShowPostDetail] = useState(false);
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);
  const [postToDelete, setPostToDelete] = useState<any>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Load posts with real report data from API
  const fetchPostsWithReports = async () => {
    console.log('🔧 [ManagePosts] Loading posts with reports from API');
    setLoading(true);

    try {
      // Try authenticated endpoint first, fallback to test endpoint
      let response = await fetch('http://localhost:5001/api/admin/posts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      // If auth fails, try test endpoint
      if (!response.ok && response.status === 401) {
        console.log('🔄 Auth failed, trying test endpoint...');
        response = await fetch('http://localhost:5001/api/admin/posts-test');
      }

      const data = await response.json();

      if (data.success) {
        const posts = data.data?.posts || data.posts || [];
        console.log('✅ [ManagePosts] Loaded posts with reports:', posts.length);
        console.log('✅ [ManagePosts] Sample post with reports:', posts[0]);
        console.log('✅ [ManagePosts] Posts with reports:', posts.filter((p: any) => p.reportCount > 0).length);
        setPosts(posts);
      } else {
        console.error('❌ [ManagePosts] Failed to load posts:', data.message);
        // Fallback to community posts without reports
        const postsWithoutReports = communityPosts.map(post => ({
          ...post,
          reportCount: 0,
          reports: [],
          status: 'active'
        }));
        setPosts(postsWithoutReports);
      }
    } catch (error) {
      console.error('❌ [ManagePosts] Error loading posts:', error);
      // Fallback to community posts without reports
      const postsWithoutReports = communityPosts.map(post => ({
        ...post,
        reportCount: 0,
        reports: [],
        status: 'active'
      }));
      setPosts(postsWithoutReports);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPostsWithReports();
  }, []); // Chỉ load 1 lần khi component mount

  // Auto-refresh every 2 minutes (thay vì 30 giây) và chỉ khi không có modal nào mở
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // Chỉ auto-refresh khi không có modal nào đang mở và autoRefresh được bật
      if (!showPostDetail && !showConfirmDelete && autoRefresh) {
        console.log('🔄 Auto-refreshing posts...');
        fetchPostsWithReports();
      }
    }, 120000); // 2 minutes thay vì 30 seconds

    return () => clearInterval(interval);
  }, [showPostDetail, showConfirmDelete, autoRefresh]);

  // Filter posts based on search and filter
  const filteredPosts = posts.filter(post => {
    const matchesSearch = searchTerm === '' ||
                         post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.user?.name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterStatus === 'all' ||
                         (filterStatus === 'reported' && post.hasPendingReports) ||
                         (filterStatus === 'recent' && new Date(post.createdAt) > new Date(Date.now() - 24 * 60 * 60 * 1000));

    return matchesSearch && matchesFilter;
  });

  // Delete post
  const handleDeletePost = (post: any) => {
    setPostToDelete(post);
    setShowConfirmDelete(true);
  };

  const confirmDeletePost = async () => {
    if (!postToDelete) return;

    try {
      console.log('🗑️ Attempting to delete post:', postToDelete._id);

      // Get token from localStorage
      const token = localStorage.getItem('token');
      console.log('🔑 Token available:', !!token);
      console.log('🔑 Token preview:', token ? token.substring(0, 20) + '...' : 'null');

      // Check if user is admin
      const userStr = localStorage.getItem('user');
      const user = userStr ? JSON.parse(userStr) : null;
      console.log('👤 User data:', user);
      console.log('👤 User role:', user?.role);
      console.log('👤 User admin status:', user?.isAdmin);
      console.log('👤 User admin check:', user?.role === 'admin' || user?.isAdmin === true);

      if (!token) {
        throw new Error('Không có token xác thực. Vui lòng đăng nhập lại.');
      }

      // More flexible admin check - if user is on admin page, they should have access
      const isAdmin = user?.role === 'admin' ||
                     user?.isAdmin === true ||
                     user?.role === 'Admin' ||
                     window.location.pathname.includes('/admin');

      console.log('👤 Final admin check:', isAdmin);

      if (!isAdmin) {
        console.log('⚠️ Admin check failed, but proceeding with API call to let server decide...');
        // Don't throw error here, let the server handle authorization
      }

      let response = await fetch(`http://localhost:5001/api/admin/posts/${postToDelete._id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Delete response status:', response.status);

      // If auth fails, try simple test endpoint for development
      if (response.status === 401 || response.status === 403) {
        console.log('🔄 Auth failed, trying simple test endpoint...');
        response = await fetch(`http://localhost:5001/api/admin/posts-simple/${postToDelete._id}`, {
          method: 'DELETE'
        });
        console.log('📡 Simple test response status:', response.status);
      }

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Delete successful:', result);

        // Remove post from local state
        setPosts(prev => prev.filter(p => p._id !== postToDelete._id));

        // Also remove from community posts context if needed
        deleteCommunityPost(postToDelete._id);

        toast.success('Đã xóa bài viết thành công');
        setShowConfirmDelete(false);
        setPostToDelete(null);

        // Refresh the posts list to get updated data
        fetchPostsWithReports();
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        console.error('❌ Delete failed:', errorData);

        if (response.status === 401) {
          throw new Error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
        } else if (response.status === 403) {
          throw new Error('Bạn không có quyền thực hiện hành động này.');
        } else if (response.status === 404) {
          throw new Error('Không tìm thấy bài viết hoặc bài viết đã bị xóa.');
        } else {
          throw new Error(errorData.message || `Lỗi ${response.status}: Không thể xóa bài viết`);
        }
      }
    } catch (error) {
      console.error('❌ Error deleting post:', error);
      const errorMessage = error instanceof Error ? error.message : 'Lỗi không xác định';
      toast.error(`Có lỗi xảy ra khi xóa bài viết: ${errorMessage}`);
      setShowConfirmDelete(false);
      setPostToDelete(null);
    }
  };

  // View post details
  const handleViewPost = (post: any) => {
    setSelectedPost(post);
    setShowPostDetail(true);
  };

  // Lock user
  const handleLockUser = (userName: string) => {
    if (window.confirm(`Bạn có chắc chắn muốn khóa tài khoản "${userName}"?`)) {
      toast.success(`Đã khóa tài khoản ${userName}`);
      // In production: API call to lock user
    }
  };

  // Delete comment
  const handleDeleteComment = async (postId: string, commentId: string) => {
    if (!window.confirm('Bạn có chắc chắn muốn xóa bình luận này?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5001/api/admin/posts/${postId}/comments/${commentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        // Update local state
        setPosts(prev => prev.map(post => {
          if (post._id === postId) {
            return {
              ...post,
              comments: post.comments?.filter((comment: any) => comment._id !== commentId) || []
            };
          }
          return post;
        }));

        // Update selected post if it's the same
        if (selectedPost && selectedPost._id === postId) {
          setSelectedPost((prev: any) => ({
            ...prev,
            comments: prev.comments?.filter((comment: any) => comment._id !== commentId) || []
          }));
        }

        toast.success('Đã xóa bình luận thành công');
      } else {
        throw new Error('Failed to delete comment');
      }
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast.error('Có lỗi xảy ra khi xóa bình luận');
    }
  };

  // Load post comments with reports
  const loadPostComments = async (postId: string) => {
    try {
      const response = await fetch(`http://localhost:5001/api/admin/posts/${postId}/comments`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Update selected post with detailed comments
          setSelectedPost((prev: any) => ({
            ...prev,
            comments: data.data.comments
          }));
          toast.success('Đã tải chi tiết comment');
        }
      } else {
        throw new Error('Failed to load comments');
      }
    } catch (error) {
      console.error('Error loading comments:', error);
      toast.error('Có lỗi xảy ra khi tải comment');
    }
  };

  // Resolve comment report
  const handleResolveCommentReport = async (commentId: string, action: 'dismiss' | 'remove_comment') => {
    try {
      // Find the report ID for this comment (simplified - in real app you'd track this better)
      const response = await fetch(`http://localhost:5001/api/admin/comment-reports`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const report = data.data.reports.find((r: any) => r.comment._id === commentId);

        if (report) {
          const resolveResponse = await fetch(`http://localhost:5001/api/admin/comment-reports/${report._id}/resolve`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ action })
          });

          if (resolveResponse.ok) {
            if (action === 'remove_comment') {
              // Remove comment from UI
              setSelectedPost((prev: any) => ({
                ...prev,
                comments: prev.comments?.filter((comment: any) => comment._id !== commentId) || []
              }));
              toast.success('Đã xóa comment và xử lý báo cáo');
            } else {
              // Clear reports for this comment
              setSelectedPost((prev: any) => ({
                ...prev,
                comments: prev.comments?.map((comment: any) =>
                  comment._id === commentId
                    ? { ...comment, reportCount: 0, reports: [] }
                    : comment
                ) || []
              }));
              toast.success('Đã bỏ qua báo cáo comment');
            }
          } else {
            throw new Error('Failed to resolve comment report');
          }
        }
      }
    } catch (error) {
      console.error('Error resolving comment report:', error);
      toast.error('Có lỗi xảy ra khi xử lý báo cáo comment');
    }
  };

  // Resolve report
  const handleResolveReport = async (postId: string, action: 'dismiss' | 'remove') => {
    try {
      const response = await fetch(`http://localhost:5001/api/admin/posts/${postId}/reports/resolve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ action })
      });

      if (response.ok) {
        if (action === 'remove') {
          // Remove post from list
          setPosts(prev => prev.filter(p => p._id !== postId));
          setShowPostDetail(false);
          toast.success('Đã xóa bài viết và xử lý báo cáo');
        } else {
          // Clear reports
          setPosts(prev => prev.map(post => {
            if (post._id === postId) {
              return { ...post, reports: [], reportCount: 0 };
            }
            return post;
          }));

          if (selectedPost && selectedPost._id === postId) {
            setSelectedPost((prev: any) => ({ ...prev, reports: [], reportCount: 0 }));
          }
          toast.success('Đã bỏ qua báo cáo');
        }
      } else {
        throw new Error('Failed to resolve report');
      }
    } catch (error) {
      console.error('Error resolving report:', error);
      toast.error('Có lỗi xảy ra khi xử lý báo cáo');
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Quản lý bài viết</h1>
              <p className="mt-2 text-gray-600">
                Quản lý và kiểm duyệt các bài viết trong cộng đồng
                {autoRefresh && (
                  <span className="ml-2 text-sm text-green-600">
                    • Tự động làm mới mỗi 2 phút
                  </span>
                )}
              </p>
            </div>

            {/* Report Notifications */}
            <ReportNotifications
              onViewPost={(postId) => {
                const post = posts.find(p => p._id === postId);
                if (post) {
                  handleViewPost(post);
                }
              }}
              onResolveReport={handleResolveReport}
            />
          </div>

          {/* Report Alert */}
          {posts.filter(p => p.hasPendingReports || p.reportCount > 0).length > 0 && (
            <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-600 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-red-800">
                    Có {posts.filter(p => p.hasPendingReports).length} bài viết có báo cáo chưa xử lý
                  </h3>
                  <p className="text-sm text-red-700 mt-1">
                    Tổng cộng {posts.filter(p => p.reportCount > 0).length} bài viết đã từng bị báo cáo.
                    Hãy kiểm tra và xử lý để duy trì chất lượng cộng đồng.
                  </p>
                  {posts.filter(p => p.hasPendingReports).length > 0 && (
                    <div className="mt-2">
                      <button
                        onClick={() => setFilterStatus('reported')}
                        className="text-sm bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 transition-colors"
                      >
                        Xem bài viết cần xử lý
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MessageSquare className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Tổng bài viết</p>
                <p className="text-2xl font-semibold text-gray-900">{posts.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Bài viết bị báo cáo</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {posts.filter(p => p.reportCount > 0).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Hôm nay</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {posts.filter(p => new Date(p.createdAt) > new Date(Date.now() - 24 * 60 * 60 * 1000)).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Heart className="h-8 w-8 text-pink-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Tổng reactions</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {posts.reduce((total, post) => total + (post.reactions?.length || 0), 0)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    id="posts-search"
                    name="search"
                    type="text"
                    placeholder="Tìm kiếm bài viết hoặc tác giả..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    autoComplete="off"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={fetchPostsWithReports}
                  disabled={loading}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  {loading ? 'Đang tải...' : 'Làm mới'}
                </button>

                <button
                  onClick={() => setAutoRefresh(!autoRefresh)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                    autoRefresh
                      ? 'bg-green-500 text-white hover:bg-green-600'
                      : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
                  }`}
                  title={autoRefresh ? 'Tắt tự động làm mới' : 'Bật tự động làm mới'}
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {autoRefresh ? 'Auto ON' : 'Auto OFF'}
                </button>

                <div className="relative">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">Tất cả bài viết ({posts.length})</option>
                    <option value="reported">
                      Cần xử lý báo cáo ({posts.filter(p => p.hasPendingReports).length})
                    </option>
                    <option value="recent">Bài viết gần đây (24h)</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Posts Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tác giả
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nội dung
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thống kê
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Báo cáo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ngày đăng
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-12 text-center">
                      <div className="flex justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      </div>
                      <p className="mt-2 text-gray-500">Đang tải...</p>
                    </td>
                  </tr>
                ) : filteredPosts.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-12 text-center">
                      <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-gray-500">Không có bài viết nào</p>
                    </td>
                  </tr>
                ) : (
                  filteredPosts.map((post) => (
                    <tr key={post._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {post.user?.name?.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {post.user?.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              ID: {post.user?._id}
                            </div>
                          </div>
                        </div>
                      </td>

                      <td className="px-6 py-4">
                        <div className="max-w-xs">
                          <p className="text-sm text-gray-900 line-clamp-3">
                            {post.content}
                          </p>
                          {post.media && post.media.length > 0 && (
                            <div className="mt-2 flex items-center text-xs text-gray-500">
                              <span>📷 {post.media.length} ảnh</span>
                            </div>
                          )}
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          <div className="flex items-center space-x-4">
                            <span className="flex items-center">
                              <Heart className="h-4 w-4 text-red-500 mr-1" />
                              {post.reactions?.length || 0}
                            </span>
                            <span className="flex items-center">
                              <MessageSquare className="h-4 w-4 text-blue-500 mr-1" />
                              {post.comments?.length || 0}
                            </span>
                            <span className="flex items-center">
                              <Share className="h-4 w-4 text-green-500 mr-1" />
                              {post.shares || 0}
                            </span>
                          </div>
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        {post.reportCount > 0 ? (
                          <div className="flex flex-col space-y-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              post.hasPendingReports
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              {post.reportCount} báo cáo
                            </span>
                            {post.hasPendingReports ? (
                              <span className="text-xs text-red-600 font-medium">
                                {post.pendingReportCount} chưa xử lý
                              </span>
                            ) : (
                              <span className="text-xs text-gray-600">
                                Đã xử lý
                              </span>
                            )}
                            {post.latestReport && (
                              <span className="text-xs text-gray-500">
                                Gần nhất: {post.latestReport.reason}
                              </span>
                            )}
                          </div>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Bình thường
                          </span>
                        )}
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(post.createdAt)}
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleViewPost(post)}
                            className="text-blue-600 hover:text-blue-900 p-1 rounded"
                            title="Xem chi tiết"
                          >
                            <Eye className="h-4 w-4" />
                          </button>

                          {/* Quick Report Actions */}
                          {post.reportCount > 0 && (
                            <>
                              <button
                                onClick={() => handleResolveReport(post._id, 'dismiss')}
                                className="text-green-600 hover:text-green-900 p-1 rounded"
                                title="Bỏ qua báo cáo"
                              >
                                <CheckCircle className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleResolveReport(post._id, 'remove')}
                                className="text-red-600 hover:text-red-900 p-1 rounded"
                                title="Xóa bài viết do báo cáo"
                              >
                                <XCircle className="h-4 w-4" />
                              </button>
                            </>
                          )}

                          <button
                            onClick={() => handleLockUser(post.user?.name || 'Unknown User')}
                            className="text-yellow-600 hover:text-yellow-900 p-1 rounded"
                            title="Khóa tài khoản"
                          >
                            <UserX className="h-4 w-4" />
                          </button>

                          <button
                            onClick={() => handleDeletePost(post)}
                            className="text-red-600 hover:text-red-900 p-1 rounded"
                            title="Xóa bài viết"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Post Detail Modal */}
        {showPostDetail && selectedPost && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Chi tiết bài viết</h3>
                  <button
                    onClick={() => setShowPostDetail(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tác giả</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedPost.user?.name}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Nội dung</label>
                    <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{selectedPost.content}</p>
                  </div>

                  {selectedPost.media && selectedPost.media.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Media</label>
                      <div className="mt-1 grid grid-cols-2 gap-2">
                        {selectedPost.media.map((url: string, index: number) => (
                          <img
                            key={index}
                            src={url}
                            alt={`Media ${index + 1}`}
                            className="w-full h-32 object-cover rounded"
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Reports Section */}
                  {selectedPost.reports && selectedPost.reports.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Báo cáo ({selectedPost.reports.length})
                      </label>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {selectedPost.reports.map((report: any, index: number) => (
                          <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-3">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="text-sm font-medium text-red-800">{report.reason}</p>
                                <p className="text-xs text-red-600">
                                  Bởi: {report.user?.name || 'Ẩn danh'} • {formatDate(report.createdAt)}
                                </p>
                              </div>
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                {report.reason}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Comments Management Section */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Bình luận ({selectedPost.comments?.length || 0})
                      </label>
                      <button
                        onClick={() => loadPostComments(selectedPost._id)}
                        className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                      >
                        Tải chi tiết comment
                      </button>
                    </div>

                    {selectedPost.comments && selectedPost.comments.length > 0 ? (
                      <div className="space-y-3 max-h-80 overflow-y-auto border border-gray-200 rounded-lg p-3">
                        {selectedPost.comments.map((comment: any, index: number) => (
                          <div key={comment._id || index} className="bg-white border border-gray-100 rounded-lg p-3 shadow-sm">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-2">
                                  <img
                                    src={comment.user?.avatar || '/default-avatar.png'}
                                    alt="avatar"
                                    className="w-6 h-6 rounded-full"
                                  />
                                  <span className="text-sm font-medium text-gray-900">
                                    {comment.user?.name || 'Ẩn danh'}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {formatDate(comment.createdAt)}
                                  </span>
                                  {comment.reportCount > 0 && (
                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                      {comment.reportCount} báo cáo
                                    </span>
                                  )}
                                </div>
                                <p className="text-sm text-gray-700 mb-2">{comment.content}</p>

                                {/* Comment stats */}
                                <div className="flex items-center space-x-4 text-xs text-gray-500">
                                  {comment.reactions && comment.reactions.length > 0 && (
                                    <span>{comment.reactions.length} reactions</span>
                                  )}
                                  {comment.replies && comment.replies.length > 0 && (
                                    <span>{comment.replies.length} replies</span>
                                  )}
                                </div>

                                {/* Comment reports */}
                                {comment.reports && comment.reports.length > 0 && (
                                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                                    <div className="text-xs font-medium text-red-800 mb-1">Báo cáo:</div>
                                    {comment.reports.slice(0, 2).map((report: any, idx: number) => (
                                      <div key={idx} className="text-xs text-red-700">
                                        • {report.reason} - {report.user?.name}
                                      </div>
                                    ))}
                                    {comment.reports.length > 2 && (
                                      <div className="text-xs text-red-600 font-medium">
                                        +{comment.reports.length - 2} báo cáo khác
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>

                              <div className="flex items-center space-x-2 ml-3">
                                {comment.reportCount > 0 && (
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={() => handleResolveCommentReport(comment._id, 'dismiss')}
                                      className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200"
                                      title="Bỏ qua báo cáo"
                                    >
                                      Bỏ qua
                                    </button>
                                    <button
                                      onClick={() => handleResolveCommentReport(comment._id, 'remove_comment')}
                                      className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                                      title="Xóa comment"
                                    >
                                      Xóa
                                    </button>
                                  </div>
                                )}
                                <button
                                  onClick={() => handleDeleteComment(selectedPost._id, comment._id)}
                                  className="text-red-600 hover:text-red-800 p-1 rounded"
                                  title="Xóa bình luận"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500 italic">Chưa có bình luận nào</div>
                    )}
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Reactions</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedPost.reactions?.length || 0}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Comments</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedPost.comments?.length || 0}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Shares</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedPost.shares || 0}</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Ngày đăng</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(selectedPost.createdAt)}</p>
                  </div>

                  {/* Action Buttons for Reports */}
                  {selectedPost.reports && selectedPost.reports.length > 0 && (
                    <div className="border-t pt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-3">Xử lý báo cáo</label>
                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleResolveReport(selectedPost._id, 'dismiss')}
                          className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                          Bỏ qua báo cáo
                        </button>
                        <button
                          onClick={() => handleResolveReport(selectedPost._id, 'remove')}
                          className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                        >
                          Xóa bài viết
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Confirm Delete Modal */}
        {showConfirmDelete && postToDelete && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
                  <h3 className="text-lg font-semibold">Xác nhận xóa bài viết</h3>
                </div>

                <p className="text-gray-600 mb-6">
                  Bạn có chắc chắn muốn xóa bài viết này? Hành động này không thể hoàn tác.
                </p>

                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setShowConfirmDelete(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
                  >
                    Hủy
                  </button>
                  <button
                    onClick={confirmDeletePost}
                    className="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700"
                  >
                    Xóa bài viết
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManagePosts;