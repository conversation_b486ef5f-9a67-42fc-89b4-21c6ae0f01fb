import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit3,
  Heart,
  MessageCircle,
  Share2,
  Settings,
  Award,
  TrendingUp,
  Users,
  Gift
} from 'lucide-react';
import UserAvatar from '../components/common/UserAvatar';
import BadgeShowcase from '../components/badges/BadgeShowcase';
import PrimaryBadge from '../components/badges/PrimaryBadge';

interface UserPost {
  _id: string;
  content: string;
  media?: string[];
  createdAt: string;
  reactions: any[];
  comments: any[];
  user: {
    _id: string;
    name: string;
    avatar?: string;
  };
}

interface UserStats {
  totalPosts: number;
  totalReactions: number;
  totalComments: number;
  totalDonations: number;
  totalEvents: number;
  totalDonationAmount: number;
}

const Profile: React.FC = () => {
  const { user, authLoading } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'posts' | 'about' | 'badges' | 'activity'>('posts');
  const [userPosts, setUserPosts] = useState<UserPost[]>([]);
  const [userStats, setUserStats] = useState<UserStats>({
    totalPosts: 0,
    totalReactions: 0,
    totalComments: 0,
    totalDonations: 0,
    totalEvents: 0,
    totalDonationAmount: 0
  });
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/login');
      return;
    }

    if (user) {
      fetchUserData();
    }
  }, [user, authLoading, navigate]);

  const fetchUserData = async () => {
    try {
      setLoading(true);

      // Fetch user posts
      const postsResponse = await fetch(`http://localhost:5001/api/posts/user/${user?.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (postsResponse.ok) {
        const postsData = await postsResponse.json();
        setUserPosts(postsData.data || []);
      }

      // Fetch user stats
      const statsResponse = await fetch(`http://localhost:5001/api/users/${user?.id}/stats`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setUserStats(statsData.data || userStats);
      }

    } catch (error) {
      console.error('Error fetching user data:', error);
      toast.error('Không thể tải thông tin người dùng');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePost = async (postId: string) => {
    if (!confirm('Bạn có chắc chắn muốn xóa bài viết này?')) return;

    try {
      const response = await fetch(`http://localhost:5001/api/posts/${postId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setUserPosts(prev => prev.filter(post => post._id !== postId));
        toast.success('Đã xóa bài viết');
      } else {
        toast.error('Không thể xóa bài viết');
      }
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error('Có lỗi xảy ra khi xóa bài viết');
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải thông tin...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-6xl mx-auto py-8 px-4">
        {/* Profile Header */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-8">
          {/* Cover Photo */}
          <div className="h-48 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 relative">
            <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            <div className="absolute bottom-4 right-4">
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="bg-white bg-opacity-20 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition-all duration-200 flex items-center gap-2"
              >
                <Edit3 className="w-4 h-4" />
                Chỉnh sửa
              </button>
            </div>
          </div>

          {/* Profile Info */}
          <div className="px-8 py-6">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
              {/* Avatar */}
              <div className="relative -mt-16 md:-mt-20">
                <div className="w-32 h-32 rounded-full border-4 border-white shadow-lg overflow-hidden bg-white">
                  <UserAvatar user={user} size="xl" showTooltip={false} />
                </div>
                <div className="absolute -bottom-2 -right-2">
                  <PrimaryBadge userId={user.id} size="md" showTooltip={true} />
                </div>
              </div>

              {/* User Info */}
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-3xl font-bold text-gray-900">{user.name}</h1>
                  <PrimaryBadge userId={user.id} size="sm" showTooltip={true} />
                </div>
                <p className="text-gray-600 mb-4">{user.email}</p>

                {/* Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{userStats.totalPosts}</div>
                    <div className="text-sm text-blue-500">Bài viết</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{userStats.totalDonations}</div>
                    <div className="text-sm text-green-500">Lần ủng hộ</div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{userStats.totalEvents}</div>
                    <div className="text-sm text-purple-500">Sự kiện</div>
                  </div>
                  <div className="text-center p-3 bg-yellow-50 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">
                      {formatCurrency(userStats.totalDonationAmount).replace('₫', '')}₫
                    </div>
                    <div className="text-sm text-yellow-500">Đã ủng hộ</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="border-t border-gray-100">
            <nav className="flex">
              {[
                { key: 'posts', label: 'Bài viết', icon: MessageCircle },
                { key: 'about', label: 'Giới thiệu', icon: User },
                { key: 'badges', label: 'Huy hiệu', icon: Award },
                { key: 'activity', label: 'Hoạt động', icon: TrendingUp }
              ].map(({ key, label, icon: Icon }) => (
                <button
                  key={key}
                  onClick={() => setActiveTab(key as any)}
                  className={`flex-1 flex items-center justify-center gap-2 py-4 px-6 font-medium transition-colors ${
                    activeTab === key
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  {label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'posts' && (
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Bài viết của tôi ({userPosts.length})</h2>
              {userPosts.length > 0 ? (
                <div className="grid gap-6">
                  {userPosts.map(post => (
                    <div key={post._id} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center gap-3">
                          <UserAvatar user={post.user} size="sm" />
                          <div>
                            <p className="font-medium text-gray-900">{post.user.name}</p>
                            <p className="text-sm text-gray-500">{formatDate(post.createdAt)}</p>
                          </div>
                        </div>
                        <button
                          onClick={() => handleDeletePost(post._id)}
                          className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                        >
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>

                      <p className="text-gray-800 mb-4 leading-relaxed">{post.content}</p>

                      {post.media && post.media.length > 0 && (
                        <div className="mb-4">
                          <img
                            src={post.media[0]}
                            alt="Post media"
                            className="w-full h-64 object-cover rounded-lg"
                          />
                        </div>
                      )}

                      <div className="flex items-center gap-6 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <Heart className="w-4 h-4" />
                          {post.reactions?.length || 0} lượt thích
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageCircle className="w-4 h-4" />
                          {post.comments?.length || 0} bình luận
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-white rounded-xl shadow-lg">
                  <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">Bạn chưa có bài viết nào</p>
                  <p className="text-gray-400 mb-6">Hãy chia sẻ câu chuyện đầu tiên của bạn!</p>
                  <button
                    onClick={() => navigate('/posts')}
                    className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Tạo bài viết
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'about' && (
            <div className="bg-white rounded-xl shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Thông tin cá nhân</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{user.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Calendar className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Tham gia từ</p>
                      <p className="font-medium">{formatDate(user.createdAt || new Date().toISOString())}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Users className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Vai trò</p>
                      <p className="font-medium capitalize">{user.role === 'admin' ? 'Quản trị viên' : 'Thành viên'}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Gift className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Đóng góp</p>
                      <p className="font-medium">{formatCurrency(userStats.totalDonationAmount)}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'badges' && (
            <BadgeShowcase userId={user.id} />
          )}

          {activeTab === 'activity' && (
            <div className="bg-white rounded-xl shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Hoạt động gần đây</h2>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <MessageCircle className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium">Đã đăng {userStats.totalPosts} bài viết</p>
                    <p className="text-sm text-gray-500">Chia sẻ câu chuyện với cộng đồng</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 bg-green-50 rounded-lg">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <Heart className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium">Đã ủng hộ {userStats.totalDonations} lần</p>
                    <p className="text-sm text-gray-500">Tổng cộng {formatCurrency(userStats.totalDonationAmount)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-4 bg-purple-50 rounded-lg">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium">Tham gia {userStats.totalEvents} sự kiện</p>
                    <p className="text-sm text-gray-500">Hoạt động tình nguyện tích cực</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile;