import React, { useState, useEffect } from 'react';
import { X, Bell, Clock, CheckCircle, AlertCircle, Info, MessageSquare, Calendar, DollarSign, Settings, User } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';

interface Notification {
  _id: string;
  type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
  title: string;
  message: string;
  data?: {
    campaignId?: string;
    donationId?: string;
    eventId?: string;
    postId?: string;
    commentId?: string;
    reportId?: string;
    amount?: number;
    status?: string;
    reason?: string;
    actionType?: 'created' | 'updated' | 'deleted' | 'cancelled' | 'completed' | 'reminder';
    relatedUserId?: string;
    relatedUserName?: string;
  };
  isRead: boolean;
  emailSent: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
}

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  notifications: Notification[];
  onMarkAsRead: (notificationId: string) => void;
  onMarkAllAsRead: () => void;
  loading: boolean;
}

const NotificationModal: React.FC<NotificationModalProps> = ({
  isOpen,
  onClose,
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  loading
}) => {
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'donation':
        return <DollarSign className="w-5 h-5 text-green-500" />;
      case 'campaign':
        return <Bell className="w-5 h-5 text-blue-500" />;
      case 'event':
        return <Calendar className="w-5 h-5 text-purple-500" />;
      case 'post':
        return <MessageSquare className="w-5 h-5 text-indigo-500" />;
      case 'comment':
        return <MessageSquare className="w-5 h-5 text-teal-500" />;
      case 'system':
        return <Settings className="w-5 h-5 text-gray-500" />;
      case 'admin':
        return <User className="w-5 h-5 text-red-500" />;
      case 'report':
        return <AlertCircle className="w-5 h-5 text-orange-500" />;
      default:
        return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-50';
      case 'high':
        return 'border-l-orange-500 bg-orange-50';
      case 'medium':
        return 'border-l-blue-500 bg-blue-50';
      case 'low':
        return 'border-l-gray-500 bg-gray-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      onMarkAsRead(notification._id);
    }
    setSelectedNotification(notification);
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-5xl max-h-[90vh] mx-4 bg-white rounded-xl shadow-2xl overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <Bell className="w-6 h-6" />
              </div>
              <div>
                <h2 className="text-xl font-bold">Thông báo</h2>
                <p className="text-blue-100 text-sm">Quản lý thông báo của bạn</p>
              </div>
              {unreadCount > 0 && (
                <span className="px-3 py-1 text-xs bg-red-500 text-white rounded-full font-medium">
                  {unreadCount} mới
                </span>
              )}
            </div>
            <div className="flex items-center space-x-3">
              {unreadCount > 0 && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={onMarkAllAsRead}
                  className="px-4 py-2 text-sm bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-colors font-medium flex items-center space-x-2"
                >
                  <CheckCircle className="w-4 h-4" />
                  <span>Đánh dấu đã đọc</span>
                </motion.button>
              )}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onClose}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </motion.button>
            </div>
          </div>

          <div className="flex h-[calc(90vh-120px)]">
            {/* Notifications List */}
            <div className="w-1/2 border-r border-gray-200 overflow-y-auto bg-gray-50">
              {loading ? (
                <div className="flex flex-col items-center justify-center h-32 p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
                  <p className="text-gray-500">Đang tải thông báo...</p>
                </div>
              ) : notifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-32 text-gray-500 p-8">
                  <Bell className="w-16 h-16 mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Không có thông báo</h3>
                  <p className="text-sm text-center">Bạn sẽ nhận được thông báo khi có hoạt động mới</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {notifications.map((notification) => (
                    <motion.div
                      key={notification._id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`p-4 cursor-pointer border-l-4 transition-all duration-200 ${getPriorityColor(notification.priority)} ${
                        !notification.isRead ? 'bg-blue-50 shadow-sm' : 'bg-white'
                      } ${selectedNotification?._id === notification._id ? 'bg-blue-100 shadow-md ring-2 ring-blue-500' : ''}`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                              {notification.title}
                            </p>
                            {!notification.isRead && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-400">
                              {formatDistanceToNow(new Date(notification.createdAt), { 
                                addSuffix: true, 
                                locale: vi 
                              })}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              notification.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                              notification.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                              notification.priority === 'medium' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {notification.priority}
                            </span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Notification Detail */}
            <div className="w-1/2 p-6 overflow-y-auto">
              {selectedNotification ? (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-4"
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {getNotificationIcon(selectedNotification.type)}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {selectedNotification.title}
                      </h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          selectedNotification.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                          selectedNotification.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                          selectedNotification.priority === 'medium' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {selectedNotification.priority}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatDistanceToNow(new Date(selectedNotification.createdAt), { 
                            addSuffix: true, 
                            locale: vi 
                          })}
                        </span>
                        {selectedNotification.isRead && (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700 leading-relaxed">
                      {selectedNotification.message}
                    </p>
                  </div>

                  {selectedNotification.data?.reason && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h4 className="font-medium text-yellow-800 mb-2">Lý do:</h4>
                      <p className="text-yellow-700">{selectedNotification.data.reason}</p>
                    </div>
                  )}

                  {selectedNotification.data?.relatedUserName && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-medium text-blue-800 mb-2">Liên quan đến:</h4>
                      <p className="text-blue-700">{selectedNotification.data.relatedUserName}</p>
                    </div>
                  )}

                  <div className="text-xs text-gray-500 pt-4 border-t border-gray-200">
                    <p>Loại: {selectedNotification.type}</p>
                    <p>Thời gian tạo: {new Date(selectedNotification.createdAt).toLocaleString('vi-VN')}</p>
                    {selectedNotification.emailSent && (
                      <p className="text-green-600">✓ Đã gửi email thông báo</p>
                    )}
                  </div>
                </motion.div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <MessageSquare className="w-16 h-16 mb-4 opacity-50" />
                  <p className="text-lg font-medium">Chọn thông báo để xem chi tiết</p>
                  <p className="text-sm">Click vào thông báo bên trái để xem nội dung đầy đủ</p>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default NotificationModal;
