import React, { useState } from 'react';
import api from '../services/api';
import { Mail, Lock, User as UserIcon, CheckCircle, XCircle, Heart, ArrowLeft, RotateCcw } from 'lucide-react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

const passwordRules = [
  { label: 'Ít nhất 8 ký tự', test: (v: string) => v.length >= 8 },
  { label: 'Chữ hoa', test: (v: string) => /[A-Z]/.test(v) },
  { label: 'Chữ thường', test: (v: string) => /[a-z]/.test(v) },
  { label: 'Ký tự đặc biệt', test: (v: string) => /[^A-Za-z0-9]/.test(v) },
];

const RegisterPage: React.FC = () => {
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState('');
  const [otpError, setOtpError] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [otpDigits, setOtpDigits] = useState(['', '', '', '', '', '']);
  const otpInputs = [0, 1, 2, 3, 4, 5];
  const otpRefs = otpInputs.map(() => React.createRef<HTMLInputElement>());
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Step 1: Nhập email
  const handleCheckEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      setError('Vui lòng nhập email');
      return;
    }
    try {
      setLoading(true);
      setError(null);
      const response = await api.post('/api/auth/check-email', { email });
      if (response.data.available) {
        await api.post('/api/auth/send-otp', { email });
        setStep(2);
      } else {
        setError('Email đã được sử dụng');
      }
    } catch (error: any) {
      setError(error.message || 'Có lỗi xảy ra khi kiểm tra email');
    } finally {
      setLoading(false);
    }
  };

  // Xử lý nhập OTP từng số
  const handleOtpChange = (idx: number, value: string) => {
    if (!/^[0-9]?$/.test(value)) return;
    const newOtp = [...otpDigits];
    newOtp[idx] = value;
    setOtpDigits(newOtp);
    if (value && idx < 5) {
      otpRefs[idx + 1].current?.focus();
    }
    if (newOtp.every(d => d.length === 1)) {
      handleVerifyOtp(newOtp.join(''));
    }
  };

  const handleOtpKeyDown = (idx: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace') {
      if (otpDigits[idx]) {
        const newOtp = [...otpDigits];
        newOtp[idx] = '';
        setOtpDigits(newOtp);
      } else if (idx > 0) {
        otpRefs[idx - 1].current?.focus();
      }
    }
  };

  // Step 2: Nhập OTP
  const handleVerifyOtp = async (otpValue: string) => {
    if (!otpValue || otpValue.length !== 6) {
      setOtpError('Vui lòng nhập đầy đủ mã OTP');
      return;
    }
    setOtpError('');
    setLoading(true);
    try {
      await api.post('/api/auth/verify-otp', { email, otp: otpValue });
      setStep(3);
    } catch (err: any) {
      setOtpError(err.message || 'Lỗi xác thực OTP');
      setOtpDigits(['', '', '', '', '', '']);
      otpRefs[0].current?.focus();
    } finally {
      setLoading(false);
    }
  };

  // Step 3: Nhập thông tin cá nhân
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!name.trim()) {
      setError('Vui lòng nhập họ và tên');
      return;
    }
    if (!password) {
      setError('Vui lòng nhập mật khẩu');
      return;
    }
    if (password !== confirmPassword) {
      setError('Mật khẩu không khớp');
      return;
    }
    
    // Validate password strength
    const passwordErrors = passwordRules
      .filter(rule => !rule.test(password))
      .map(rule => rule.label);
    
    if (passwordErrors.length > 0) {
      setError(`Mật khẩu không đủ mạnh: ${passwordErrors.join(', ')}`);
      return;
    }

    setLoading(true);
    try {
      const response = await api.post('/api/auth/register', {
        email,
        name: name.trim(),
        password
      });
      localStorage.setItem('token', response.data.token);
      setStep(4);
    } catch (err: any) {
      setError(err.message || 'Lỗi đăng ký');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-pink-50 to-purple-100 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-300 to-pink-300 rounded-full opacity-10 blur-3xl"></div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md relative z-10"
      >
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-center mb-8"
        >
          <motion.div
            whileHover={{ scale: 1.05, rotate: 5 }}
            className="mx-auto w-16 h-16 bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 rounded-2xl flex items-center justify-center shadow-xl mb-6"
          >
            <Heart className="w-8 h-8 text-white" />
          </motion.div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 via-purple-700 to-pink-600 bg-clip-text text-transparent">
            Tham gia KeyDyWeb
          </h2>
          <p className="mt-2 text-gray-600">Bắt đầu hành trình thiện nguyện của bạn</p>

          {/* Step indicator */}
          <div className="flex justify-center mt-6 space-x-2">
            {[1, 2, 3].map((stepNum) => (
              <div
                key={stepNum}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  step >= stepNum
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </motion.div>

        {/* Form Container */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white/80 backdrop-blur-md rounded-2xl shadow-2xl p-8 border border-white/20"
        >
          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.form
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                onSubmit={handleCheckEmail}
                className="space-y-6"
              >
                <div>
                  <label htmlFor="register-email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="register-email"
                      name="email"
                      type="email"
                      className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 bg-white/50 backdrop-blur-sm"
                      placeholder="Nhập email của bạn"
                      value={email}
                      onChange={e => setEmail(e.target.value)}
                      required
                      autoComplete="email"
                    />
                  </div>
                  <AnimatePresence>
                    {error && (
                      <motion.p
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        className="text-red-600 mt-2 text-sm"
                      >
                        {error}
                      </motion.p>
                    )}
                  </AnimatePresence>
                </div>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  className="w-full py-3 rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Đang kiểm tra...
                    </div>
                  ) : (
                    'Tiếp tục'
                  )}
                </motion.button>
                <div className="text-center text-sm">
                  <span className="text-gray-600">Đã có tài khoản? </span>
                  <Link to="/login" className="font-medium text-purple-600 hover:text-purple-500 transition-colors duration-200">
                    Đăng nhập
                  </Link>
                </div>
              </motion.form>
            )}
            {step === 2 && (
              <motion.form
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
                onSubmit={e => e.preventDefault()}
              >
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 text-center">
                    Nhập mã OTP đã gửi tới email
                  </label>
                  <p className="text-sm text-gray-500 text-center mb-4">{email}</p>
                  <div className="flex space-x-3 justify-center">
                    {otpInputs.map((_, idx) => (
                      <motion.input
                        key={idx}
                        id={`otp-input-${idx}`}
                        name={`otp-${idx}`}
                        ref={otpRefs[idx]}
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ duration: 0.3, delay: idx * 0.1 }}
                        type="text"
                        inputMode="numeric"
                        maxLength={1}
                        className="w-12 h-12 text-center text-xl border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 bg-white/50 backdrop-blur-sm"
                        value={otpDigits[idx]}
                        onChange={e => handleOtpChange(idx, e.target.value)}
                        onKeyDown={e => handleOtpKeyDown(idx, e)}
                        autoFocus={idx === 0}
                        aria-label={`OTP digit ${idx + 1}`}
                        autoComplete="one-time-code"
                      />
                    ))}
                  </div>
                  <AnimatePresence>
                    {otpError && (
                      <motion.p
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        className="text-red-600 mt-2 text-sm text-center"
                      >
                        {otpError}
                      </motion.p>
                    )}
                  </AnimatePresence>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    type="button"
                    className="flex items-center text-purple-600 hover:text-purple-500 transition-colors duration-200"
                    onClick={() => setStep(1)}
                    aria-label="Quay lại bước trước"
                  >
                    <ArrowLeft className="w-4 h-4 mr-1" />
                    Quay lại
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    type="button"
                    className="flex items-center text-purple-600 hover:text-purple-500 transition-colors duration-200"
                    onClick={async () => {
                      await api.post('/api/auth/send-otp', { email });
                      setOtpDigits(['','','','','','']);
                      otpRefs[0].current?.focus();
                    }}
                    aria-label="Gửi lại mã OTP"
                  >
                    <RotateCcw className="w-4 h-4 mr-1" />
                    Gửi lại mã
                  </motion.button>
                </div>
              </motion.form>
            )}
            {step === 3 && (
              <motion.form
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                onSubmit={handleRegister}
                className="space-y-6"
              >
                <div>
                  <label htmlFor="register-name" className="block text-sm font-medium text-gray-700 mb-2">
                    Họ và tên
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <UserIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="register-name"
                      name="name"
                      type="text"
                      className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 bg-white/50 backdrop-blur-sm"
                      placeholder="Nhập họ và tên"
                      value={name}
                      onChange={e => setName(e.target.value)}
                      required
                      autoComplete="name"
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="register-password" className="block text-sm font-medium text-gray-700 mb-2">
                    Mật khẩu
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="register-password"
                      name="password"
                      type="password"
                      className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 bg-white/50 backdrop-blur-sm"
                      placeholder="Nhập mật khẩu"
                      value={password}
                      onChange={e => setPassword(e.target.value)}
                      required
                      autoComplete="new-password"
                    />
                  </div>
                  <ul className="mt-3 space-y-2 text-xs">
                    {passwordRules.map(rule => {
                      const ok = rule.test(password);
                      return (
                        <motion.li
                          key={rule.label}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          className={`flex items-center gap-2 transition-colors duration-200 ${
                            ok ? 'text-green-600' : 'text-gray-500'
                          }`}
                        >
                          {ok ? (
                            <CheckCircle size={14} className="text-green-500" />
                          ) : (
                            <XCircle size={14} className="text-gray-400" />
                          )}
                          {rule.label}
                        </motion.li>
                      )
                    })}
                  </ul>
                </div>
                <div>
                  <label htmlFor="register-confirm-password" className="block text-sm font-medium text-gray-700 mb-2">
                    Xác nhận mật khẩu
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="register-confirm-password"
                      name="confirmPassword"
                      type="password"
                      className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 bg-white/50 backdrop-blur-sm"
                      placeholder="Nhập lại mật khẩu"
                      value={confirmPassword}
                      onChange={e => setConfirmPassword(e.target.value)}
                      required
                      autoComplete="new-password"
                    />
                  </div>
                  <AnimatePresence>
                    {confirmPassword && (
                      <motion.p
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        className={`text-xs mt-2 flex items-center gap-1 ${
                          password === confirmPassword ? 'text-green-600' : 'text-red-600'
                        }`}
                      >
                        {password === confirmPassword ? (
                          <CheckCircle size={14} />
                        ) : (
                          <XCircle size={14} />
                        )}
                        {password === confirmPassword ? 'Mật khẩu khớp' : 'Mật khẩu không khớp'}
                      </motion.p>
                    )}
                  </AnimatePresence>
                </div>

                <AnimatePresence>
                  {error && (
                    <motion.p
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.95 }}
                      className="text-red-600 text-sm text-center"
                    >
                      {error}
                    </motion.p>
                  )}
                </AnimatePresence>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  className="w-full py-3 rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Đang đăng ký...
                    </div>
                  ) : (
                    'Hoàn tất đăng ký'
                  )}
                </motion.button>

                <div className="flex justify-between items-center text-sm">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    type="button"
                    className="flex items-center text-purple-600 hover:text-purple-500 transition-colors duration-200"
                    onClick={() => setStep(1)}
                  >
                    <ArrowLeft className="w-4 h-4 mr-1" />
                    Quay lại
                  </motion.button>
                  <Link to="/login" className="text-purple-600 hover:text-purple-500 transition-colors duration-200">
                    Đăng nhập
                  </Link>
                </div>
              </motion.form>
            )}
            {step === 4 && (
              <motion.div
                key="step4"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="text-center space-y-6"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.2, type: "spring", stiffness: 200 }}
                >
                  <CheckCircle size={64} className="mx-auto text-green-500" aria-hidden="true" />
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <h3 className="text-2xl font-bold text-green-700">Đăng ký thành công!</h3>
                  <p className="text-gray-600 mt-2">Chào mừng bạn đến với KeyDyWeb. Hãy bắt đầu hành trình thiện nguyện của bạn!</p>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <Link
                    to="/login"
                    className="inline-block px-8 py-3 rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                    aria-label="Chuyển đến trang đăng nhập"
                  >
                    Đăng nhập ngay
                  </Link>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default RegisterPage; 