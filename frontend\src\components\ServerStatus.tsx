import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Wifi, WifiOff } from 'lucide-react';
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

const ServerStatus: React.FC = () => {
  const [isOnline, setIsOnline] = useState<boolean | null>(null);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);
  const [checking, setChecking] = useState(false);

  const checkServerStatus = async () => {
    setChecking(true);
    try {
      const response = await axios.get(`${API_URL}/api/campaigns`, {
        timeout: 5000
      });
      setIsOnline(response.status === 200);
      setLastCheck(new Date());
    } catch (error) {
      console.error('Server check failed:', error);
      setIsOnline(false);
      setLastCheck(new Date());
    } finally {
      setChecking(false);
    }
  };

  useEffect(() => {
    checkServerStatus();
    
    // Check every 30 seconds
    const interval = setInterval(checkServerStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  if (isOnline === null) {
    return (
      <div className="fixed top-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 z-50">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
        <span className="text-sm">Đang kiểm tra server...</span>
      </div>
    );
  }

  return (
    <div className={`fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 z-50 ${
      isOnline 
        ? 'bg-green-100 border border-green-400 text-green-700' 
        : 'bg-red-100 border border-red-400 text-red-700'
    }`}>
      {isOnline ? (
        <>
          <CheckCircle className="w-4 h-4" />
          <span className="text-sm">Server hoạt động</span>
        </>
      ) : (
        <>
          <AlertCircle className="w-4 h-4" />
          <span className="text-sm">Server không phản hồi</span>
        </>
      )}
      
      {lastCheck && (
        <span className="text-xs opacity-75">
          {lastCheck.toLocaleTimeString()}
        </span>
      )}
      
      <button
        onClick={checkServerStatus}
        disabled={checking}
        className="ml-2 p-1 hover:bg-black/10 rounded"
        title="Kiểm tra lại"
      >
        {checking ? (
          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current"></div>
        ) : (
          <Wifi className="w-3 h-3" />
        )}
      </button>
    </div>
  );
};

export default ServerStatus;
