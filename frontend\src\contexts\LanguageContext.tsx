import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'vi' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string) => string;
}

// Translation dictionary
const translations = {
  vi: {
    // Navigation
    'nav.home': 'Trang chủ',
    'nav.events': 'Sự kiện',
    'nav.campaigns': 'Chiến dịch',
    'nav.posts': 'Bài viết',
    'nav.profile': 'Tài khoản',
    'nav.settings': 'Cài đặt',
    'nav.dashboard': 'Bảng điều khiển',
    'nav.admin': 'Quản trị viên',
    'nav.login': 'Đăng nhập',
    'nav.register': 'Đăng ký',
    'nav.logout': 'Đăng xuất',

    // Settings
    'settings.title': 'Cài đặt',
    'settings.subtitle': '<PERSON>uản lý thông tin tài khoản và tùy chọn cá nhân của bạn',
    'settings.general': 'Thông tin chung',
    'settings.account': '<PERSON><PERSON><PERSON> kho<PERSON>n',
    'settings.security': 'Bảo mật',
    'settings.notifications': 'Thông báo',
    'settings.privacy': 'Quyền riêng tư',
    'settings.theme': 'Giao diện',
    'settings.language': 'Ngôn ngữ',
    'settings.theme.light': '🌞 Sáng',
    'settings.theme.dark': '🌙 Tối',
    'settings.language.vi': '🇻🇳 Tiếng Việt',
    'settings.language.en': '🇺🇸 English',
    'settings.save': 'Lưu thay đổi',
    'settings.saving': 'Đang lưu...',
    'settings.displayOptions': 'Tùy chọn hiển thị',

    // Profile
    'profile.name': 'Họ và tên',
    'profile.email': 'Email',
    'profile.phone': 'Số điện thoại',
    'profile.address': 'Địa chỉ',
    'profile.bio': 'Giới thiệu bản thân',
    'profile.avatar': 'Ảnh đại diện',

    // Security
    'security.changePassword': 'Đổi mật khẩu',
    'security.currentPassword': 'Mật khẩu hiện tại',
    'security.newPassword': 'Mật khẩu mới',
    'security.confirmPassword': 'Xác nhận mật khẩu mới',
    'security.loginSessions': 'Phiên đăng nhập',
    'security.currentDevice': 'Thiết bị hiện tại',
    'security.active': 'Đang hoạt động',

    // Notifications
    'notifications.emailNotifications': 'Thông báo email',
    'notifications.pushNotifications': 'Thông báo đẩy',
    'notifications.generalNotifications': 'Thông báo chung',
    'notifications.emailDesc': 'Nhận email về các hoạt động quan trọng',
    'notifications.pushDesc': 'Nhận thông báo đẩy trên trình duyệt',

    // Privacy
    'privacy.profileVisibility': 'Hiển thị hồ sơ',
    'privacy.showEmail': 'Hiển thị email',
    'privacy.showPhone': 'Hiển thị số điện thoại',
    'privacy.allowMessages': 'Cho phép nhắn tin',
    'privacy.allowFriendRequests': 'Cho phép kết bạn',
    'privacy.public': '🌍 Công khai - Mọi người',
    'privacy.friends': '👥 Bạn bè',
    'privacy.private': '🔒 Riêng tư - Chỉ mình tôi',

    // Account
    'account.loginEmail': 'Email đăng nhập',
    'account.emailDesc': 'Email này được sử dụng để đăng nhập và nhận thông báo',
    'account.exportData': 'Xuất dữ liệu cá nhân',
    'account.exportDesc': 'Tải xuống bản sao dữ liệu cá nhân của bạn',
    'account.deleteAccount': 'Xóa tài khoản',
    'account.deleteDesc': 'Xóa vĩnh viễn tài khoản và tất cả dữ liệu liên quan. Hành động này không thể hoàn tác.',

    // Common
    'common.save': 'Lưu',
    'common.cancel': 'Hủy',
    'common.delete': 'Xóa',
    'common.edit': 'Chỉnh sửa',
    'common.loading': 'Đang tải...',
    'common.success': 'Thành công',
    'common.error': 'Lỗi',
    'common.change': 'Thay đổi',

    // Messages
    'message.updateSuccess': 'Cập nhật thông tin thành công!',
    'message.updateError': 'Có lỗi xảy ra khi cập nhật thông tin',
    'message.passwordMismatch': 'Mật khẩu xác nhận không khớp',
    'message.passwordTooShort': 'Mật khẩu mới phải có ít nhất 6 ký tự',
    'message.passwordChangeSuccess': 'Đổi mật khẩu thành công!',
    'message.loginRequired': 'Vui lòng đăng nhập để xem bài viết',
    'message.checkingAuth': 'Đang kiểm tra đăng nhập...',
  },
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.events': 'Events',
    'nav.campaigns': 'Campaigns',
    'nav.posts': 'Posts',
    'nav.profile': 'Profile',
    'nav.settings': 'Settings',
    'nav.dashboard': 'Dashboard',
    'nav.admin': 'Admin',
    'nav.login': 'Login',
    'nav.register': 'Register',
    'nav.logout': 'Logout',

    // Settings
    'settings.title': 'Settings',
    'settings.subtitle': 'Manage your account information and personal preferences',
    'settings.general': 'General',
    'settings.account': 'Account',
    'settings.security': 'Security',
    'settings.notifications': 'Notifications',
    'settings.privacy': 'Privacy',
    'settings.theme': 'Theme',
    'settings.language': 'Language',
    'settings.theme.light': '🌞 Light',
    'settings.theme.dark': '🌙 Dark',
    'settings.language.vi': '🇻🇳 Vietnamese',
    'settings.language.en': '🇺🇸 English',
    'settings.save': 'Save Changes',
    'settings.saving': 'Saving...',
    'settings.displayOptions': 'Display Options',

    // Profile
    'profile.name': 'Full Name',
    'profile.email': 'Email',
    'profile.phone': 'Phone Number',
    'profile.address': 'Address',
    'profile.bio': 'Bio',
    'profile.avatar': 'Avatar',

    // Security
    'security.changePassword': 'Change Password',
    'security.currentPassword': 'Current Password',
    'security.newPassword': 'New Password',
    'security.confirmPassword': 'Confirm New Password',
    'security.loginSessions': 'Login Sessions',
    'security.currentDevice': 'Current Device',
    'security.active': 'Active',

    // Notifications
    'notifications.emailNotifications': 'Email Notifications',
    'notifications.pushNotifications': 'Push Notifications',
    'notifications.generalNotifications': 'General Notifications',
    'notifications.emailDesc': 'Receive emails about important activities',
    'notifications.pushDesc': 'Receive push notifications in browser',

    // Privacy
    'privacy.profileVisibility': 'Profile Visibility',
    'privacy.showEmail': 'Show Email',
    'privacy.showPhone': 'Show Phone Number',
    'privacy.allowMessages': 'Allow Messages',
    'privacy.allowFriendRequests': 'Allow Friend Requests',
    'privacy.public': '🌍 Public - Everyone',
    'privacy.friends': '👥 Friends',
    'privacy.private': '🔒 Private - Only Me',

    // Account
    'account.loginEmail': 'Login Email',
    'account.emailDesc': 'This email is used for login and receiving notifications',
    'account.exportData': 'Export Personal Data',
    'account.exportDesc': 'Download a copy of your personal data',
    'account.deleteAccount': 'Delete Account',
    'account.deleteDesc': 'Permanently delete your account and all related data. This action cannot be undone.',

    // Common
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.loading': 'Loading...',
    'common.success': 'Success',
    'common.error': 'Error',
    'common.change': 'Change',

    // Messages
    'message.updateSuccess': 'Information updated successfully!',
    'message.updateError': 'An error occurred while updating information',
    'message.passwordMismatch': 'Password confirmation does not match',
    'message.passwordTooShort': 'New password must be at least 6 characters',
    'message.passwordChangeSuccess': 'Password changed successfully!',
    'message.loginRequired': 'Please login to view posts',
    'message.checkingAuth': 'Checking authentication...',
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>(() => {
    // Check localStorage first
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'vi' || savedLanguage === 'en')) {
      return savedLanguage;
    }
    
    // Check browser language
    const browserLang = navigator.language.toLowerCase();
    if (browserLang.startsWith('vi')) {
      return 'vi';
    }
    
    return 'vi'; // Default to Vietnamese
  });

  useEffect(() => {
    // Save to localStorage
    localStorage.setItem('language', language);
    
    // Update document language
    document.documentElement.lang = language;
    
    console.log('🌐 Language changed to:', language);
  }, [language]);

  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
  };

  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  const value = {
    language,
    setLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
