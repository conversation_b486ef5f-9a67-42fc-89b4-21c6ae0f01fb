import { Router, Request, Response } from 'express';
import { createDonation, handlePayOSReturn, handleMomoReturn, handleMomoIPN, getDonationStatus, getDonationsByCampaignId } from '../controllers/donationController';
import express from 'express';
import { Donation } from '../models/Donation';
import { Campaign } from '../models/Campaign';

const router = express.Router();

// Create new donation
router.post('/', createDonation);

// Handle PayOS return URL
router.get('/payos_return', handlePayOSReturn);

// Handle PayOS webhook (for real payments)
router.post('/payos_webhook', handlePayOSReturn);

// Handle PayOS cancel URL
router.get('/payos_cancel', (req, res) => {
  res.redirect('/campaigns');
});

// Handle Momo return URL
router.get('/momo-return', handleMomoReturn);

// Handle Momo IPN (server-to-server)
router.post('/momo_ipn', handleMomoIPN);

// Get donation status by transactionId
router.get('/:transactionId/status', getDonationStatus);

// Get donation by transactionId (for frontend compatibility)
router.get('/:transactionId', getDonationStatus);

// Update donation status (for manual testing/fixing)
router.put('/:transactionId/status', async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { status } = req.body;

    if (!['pending', 'success', 'failed', 'cancelled'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }

    const donation = await require('../models/Donation').Donation.findOne({ transactionId });
    if (!donation) {
      return res.status(404).json({ message: 'Donation not found' });
    }

    const oldStatus = donation.status;
    donation.status = status;
    donation.paymentTime = new Date();
    await donation.save();

    // If changing from pending to success, update campaign
    if (oldStatus === 'pending' && status === 'success') {
      const Campaign = require('../models/Campaign').Campaign;
      const campaign = await Campaign.findById(donation.campaignId);
      if (campaign) {
        const newProgress = Math.min(100, ((campaign.currentAmount + donation.amount) / campaign.targetAmount) * 100);

        // Add donor to list
        const newDonor = {
          name: donation.isAnonymous ? 'Ẩn danh' : donation.name,
          amount: donation.amount,
          date: donation.createdAt,
          paymentMethod: donation.paymentMethod,
          email: donation.email,
          transactionId: donation.transactionId
        };

        // Update campaign using updateOne to avoid validation issues
        await Campaign.updateOne(
          { _id: donation.campaignId },
          {
            $inc: {
              currentAmount: donation.amount,
              totalDonations: 1,
              totalDonors: 1
            },
            $push: {
              donors: newDonor
            },
            $set: {
              progress: newProgress
            }
          },
          {
            runValidators: false // Skip validation for partial updates
          }
        );
      }
    }

    res.json({
      success: true,
      message: 'Donation status updated successfully',
      donation: {
        transactionId: donation.transactionId,
        status: donation.status,
        amount: donation.amount,
        name: donation.name
      }
    });
  } catch (error) {
    console.error('Error updating donation status:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get donations by campaign ID
router.get('/campaign/:campaignId', getDonationsByCampaignId);

// Get all donations (for admin)
router.get('/', async (req, res) => {
  try {
    const donations = await require('../models/Donation').Donation.find()
      .populate('campaignId', 'title')
      .sort({ createdAt: -1 })
      .limit(100);

    const transformedDonations = donations.map((donation: any) => ({
      _id: donation._id,
      name: donation.name,
      email: donation.email,
      amount: donation.amount,
      message: donation.message || '',
      paymentMethod: donation.paymentMethod.toLowerCase(),
      status: donation.status,
      transactionId: donation.transactionId,
      createdAt: donation.createdAt,
      campaign: {
        _id: donation.campaignId._id,
        title: donation.campaignId.title
      },
      isAnonymous: donation.isAnonymous || false
    }));

    res.json({
      success: true,
      donations: transformedDonations
    });
  } catch (error: any) {
    console.error('Error fetching donations:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Auto-complete donation (for test environment)
router.post('/complete/:transactionId', async (req, res) => {
  try {
    const { transactionId } = req.params;

    // Update donation status
    const Donation = require('../models/Donation').Donation;
    const Campaign = require('../models/Campaign').Campaign;
    const donation = await Donation.findOne({ transactionId });

    if (!donation) {
      return res.status(404).json({ message: 'Donation not found' });
    }

    if (donation.status !== 'pending') {
      return res.json({
        success: true,
        message: 'Donation already processed',
        status: donation.status
      });
    }

    // Update to success
    donation.status = 'success';
    donation.paymentTime = new Date();
    await donation.save();

    // Instead of manually updating campaign stats, use fix-campaign to recalculate
    // This prevents duplicate additions and ensures accuracy

    // Get all successful donations for this campaign
    const donations = await Donation.find({
      campaignId: donation.campaignId,
      status: 'success'
    });

    // Calculate correct totals
    const totalAmount = donations.reduce((sum: number, d: any) => sum + d.amount, 0);
    const totalCount = donations.length;

    // Get campaign to calculate progress
    const campaign = await Campaign.findById(donation.campaignId);
    if (campaign) {
      const progress = Math.min(100, (totalAmount / campaign.targetAmount) * 100);

      // Update campaign with correct stats (not incremental)
      await Campaign.updateOne(
        { _id: donation.campaignId },
        {
          $set: {
            currentAmount: totalAmount,
            totalDonations: totalCount,
            totalDonors: totalCount,
            progress: progress
          }
        },
        { runValidators: false }
      );
    }

    res.json({
      success: true,
      message: 'Donation completed successfully',
      donation: {
        transactionId: donation.transactionId,
        status: donation.status,
        amount: donation.amount,
        name: donation.name
      }
    });
  } catch (error) {
    console.error('Error completing donation:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Fix campaign stats (admin endpoint)
router.post('/fix-campaign/:campaignId', async (req, res) => {
  try {
    const { campaignId } = req.params;

    // Get all successful donations for this campaign
    const Donation = require('../models/Donation').Donation;
    const donations = await Donation.find({
      campaignId: campaignId,
      status: 'success'
    });

    // Calculate correct totals
    const totalAmount = donations.reduce((sum: number, donation: any) => sum + donation.amount, 0);
    const totalCount = donations.length;

    // Get campaign to calculate progress
    const Campaign = require('../models/Campaign').Campaign;
    const campaign = await Campaign.findById(campaignId);
    if (!campaign) {
      return res.status(404).json({ message: 'Campaign not found' });
    }

    const progress = Math.min(100, (totalAmount / campaign.targetAmount) * 100);

    // Update campaign stats
    await Campaign.updateOne(
      { _id: campaignId },
      {
        $set: {
          currentAmount: totalAmount,
          totalDonations: totalCount,
          totalDonors: totalCount,
          progress: progress
        }
      },
      { runValidators: false }
    );

    res.json({
      success: true,
      message: 'Campaign stats fixed successfully',
      stats: {
        currentAmount: totalAmount,
        totalDonations: totalCount,
        totalDonors: totalCount,
        progress: progress
      }
    });
  } catch (error) {
    console.error('Error fixing campaign stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Fix all campaign stats
router.post('/fix-all-stats', async (req: Request, res: Response) => {
  try {
    console.log('🔧 Starting to fix all campaign stats...');

    const campaigns = await Campaign.find({});
    let fixedCount = 0;

    for (const campaign of campaigns) {
      // Get all successful donations for this campaign
      const donations = await Donation.find({
        campaignId: campaign._id,
        status: 'success'
      });

      const totalAmount = donations.reduce((sum: number, d: any) => sum + d.amount, 0);
      const totalCount = donations.length;
      const progress = Math.min(100, (totalAmount / campaign.targetAmount) * 100);

      // Check if stats need fixing
      const needsUpdate =
        campaign.currentAmount !== totalAmount ||
        campaign.totalDonations !== totalCount ||
        campaign.totalDonors !== totalCount ||
        Math.abs(campaign.progress - progress) > 0.1;

      if (needsUpdate) {
        await Campaign.updateOne(
          { _id: campaign._id },
          {
            $set: {
              currentAmount: totalAmount,
              totalDonations: totalCount,
              totalDonors: totalCount,
              progress: progress
            }
          },
          { runValidators: false }
        );

        console.log(`✅ Fixed stats for campaign: ${campaign.title}`);
        console.log(`   Amount: ${campaign.currentAmount} → ${totalAmount}`);
        console.log(`   Donations: ${campaign.totalDonations} → ${totalCount}`);
        console.log(`   Progress: ${campaign.progress}% → ${progress}%`);
        fixedCount++;
      }
    }

    console.log(`🎉 Fixed stats for ${fixedCount} campaigns`);

    res.json({
      success: true,
      message: `Fixed stats for ${fixedCount} campaigns`,
      fixedCount
    });
  } catch (error) {
    console.error('Error fixing all campaign stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;