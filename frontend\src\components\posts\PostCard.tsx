import React, { useState, useRef, useEffect } from 'react';
import { ThumbsUp, MessageCircle, Share2, MoreHorizontal, Flag, Trash2 } from 'lucide-react';
import CommentList from './CommentList';
import CommentForm from './CommentForm';
import ReactionsModal from './ReactionsModal';
import CommentsModal from './CommentsModal';
import UserAvatar from '../common/UserAvatar';
import PrimaryBadge from '../badges/PrimaryBadge';
import '../../styles/reactions.css';

interface User {
  _id: string;
  name: string;
  avatar?: string;
  email?: string;
}

interface Comment {
  _id: string;
  user: User;
  content: string;
  createdAt: string;
  reactions?: Array<{ type: string; user: string }>;
  userReaction?: string;
  replies?: Comment[];
}

interface Post {
  _id: string;
  user: User;
  content: string;
  media?: Array<{ type: 'image' | 'video'; url: string }> | string[];
  createdAt: string;
  reactions?: Array<{ type: string; user: User }>;
  userReaction?: string;
  comments?: Comment[];
  reportCount?: number;
  reports?: Array<{ reason: string; user: User; createdAt: string }>;
}

interface PostCardProps {
  post: Post;
  onLike?: (postId: string, reaction: string) => void;
  onComment?: (postId: string, content: string) => void;
  onShare?: () => void;
  onReport?: () => void;
  onDelete?: (postId: string) => void;
  currentUserId?: string;
  fetchPosts?: () => void;
  isAdmin?: boolean;
}

const reactions = [
  { type: 'like', icon: '👍', color: 'text-blue-500', label: 'Thích' },
  { type: 'love', icon: '❤️', color: 'text-red-500', label: 'Yêu thích' },
  { type: 'haha', icon: '😂', color: 'text-yellow-500', label: 'Haha' },
  { type: 'wow', icon: '😮', color: 'text-orange-500', label: 'Wow' },
  { type: 'sad', icon: '😢', color: 'text-yellow-500', label: 'Buồn' },
  { type: 'angry', icon: '😡', color: 'text-red-500', label: 'Phẫn nộ' }
];

const PostCard: React.FC<PostCardProps> = ({
  post,
  onLike,
  onComment,
  onShare,
  onReport,
  onDelete,
  currentUserId,
  fetchPosts,
  isAdmin = false
}) => {
  // const [showComments, setShowComments] = useState(false); // Unused
  const [showReactions, setShowReactions] = useState(false);
  const [showReactionsModal, setShowReactionsModal] = useState(false);
  const [showCommentsModal, setShowCommentsModal] = useState(false);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isReacting, setIsReacting] = useState(false);
  const [reactionAnimation, setReactionAnimation] = useState<string | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Local state for optimistic updates
  const [localPost, setLocalPost] = useState(post);

  // Update local state when post prop changes
  useEffect(() => {
    setLocalPost(post);
  }, [post]);

  // Close more menu when clicking outside (with proper ref checking)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const dropdownElement = document.querySelector('[data-dropdown="more-menu"]');
      const menuButton = document.querySelector('[data-menu-button="more"]');

      if (showMoreMenu && dropdownElement && menuButton) {
        // Don't close if clicking inside dropdown or on menu button
        if (!dropdownElement.contains(target) && !menuButton.contains(target)) {
          setShowMoreMenu(false);
        }
      }
    };

    if (showMoreMenu) {
      // Use setTimeout to ensure click events are processed first
      setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
      }, 100);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMoreMenu]);

  const getCurrentReaction = () => {
    if (!localPost.userReaction) return null;
    return reactions.find(r => r.type === localPost.userReaction);
  };

  const handleLikeClick = () => {
    if (!showReactions && onLike) {
      setIsReacting(true);
      setReactionAnimation('like-click');

      const reaction = localPost.userReaction === 'like' ? '' : 'like';
      onLike(localPost._id, reaction);

      setTimeout(() => {
        setIsReacting(false);
        setReactionAnimation(localPost.userReaction === 'like' ? null : 'like-success');
      }, 600);

      setTimeout(() => setReactionAnimation(null), 1200);
    }
  };

  const handleLikeMouseEnter = () => {
    setIsHovering(true);
    timeoutRef.current = setTimeout(() => {
      setShowReactions(true);
    }, 500); // Giảm từ 1000ms xuống 500ms và bỏ check isHovering
  };

  const handleLikeMouseLeave = () => {
    setIsHovering(false);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setTimeout(() => {
      if (!isHovering) {
        setShowReactions(false);
      }
    }, 300);
  };

  const handleReactionsMouseEnter = () => {
    setIsHovering(true);
  };

  const handleReactionsMouseLeave = () => {
    setIsHovering(false);
    setTimeout(() => {
      if (!isHovering) {
        setShowReactions(false);
      }
    }, 300);
  };

  const handleReactionSelect = (reactionType: string) => {
    if (onLike) {
      setIsReacting(true);
      setReactionAnimation(`reaction-${reactionType}`);

      const finalReaction = localPost.userReaction === reactionType ? '' : reactionType;
      onLike(localPost._id, finalReaction);

      setTimeout(() => {
        setIsReacting(false);
        setReactionAnimation(`reaction-success-${reactionType}`);
      }, 600);

      setTimeout(() => setReactionAnimation(null), 1200);
    }
    setShowReactions(false);
    setIsHovering(false);
  };

  const handleCommentsClick = () => {
    setShowCommentsModal(true);
  };

  const getReactionSummary = () => {
    if (!localPost.reactions || localPost.reactions.length === 0) return null;

    const reactionCounts: { [key: string]: number } = {};
    localPost.reactions.forEach((reaction: any) => {
      reactionCounts[reaction.type] = (reactionCounts[reaction.type] || 0) + 1;
    });

    const sortedReactions = Object.entries(reactionCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);

    return sortedReactions.map(([type, count]) => {
      const reactionData = reactions.find(r => r.type === type);
      return { type, count, icon: reactionData?.icon || '👍' };
    });
  };

  // Calculate total comments including replies
  const getTotalCommentsCount = () => {
    let total = localPost.comments?.length || 0; // Top-level comments
    localPost.comments?.forEach(comment => {
      if (comment.replies) {
        total += comment.replies.length; // Add replies
      }
    });
    return total;
  };

  const currentReaction = getCurrentReaction();
  const totalReactions = localPost.reactions?.length || 0;
  const reactionSummary = getReactionSummary();

  return (
    <div className="bg-white rounded-lg shadow-md mb-6 relative">
      {/* Post Header */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-3">
          <UserAvatar user={localPost.user} size="md" showTooltip />
          <div>
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-gray-900">{localPost.user?.name}</h3>
              <PrimaryBadge userId={localPost.user?._id} size="sm" showTooltip={true} />
            </div>
            <p className="text-sm text-gray-500">
              {new Date(localPost.createdAt).toLocaleString()}
            </p>
          </div>
        </div>
        <div className="relative">
          <button
            onClick={() => setShowMoreMenu(!showMoreMenu)}
            className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100"
            data-menu-button="more"
          >
            <MoreHorizontal className="h-5 w-5" />
          </button>

          {/* More Menu Dropdown */}
          {showMoreMenu && (
            <div
              className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
              style={{
                zIndex: 99999,
                position: 'absolute',
                top: '100%',
                right: '0',
                marginTop: '4px'
              }}
              data-dropdown="more-menu"
            >
              {/* DELETE BUTTON - Only show for post owner */}
              {currentUserId === localPost.user?._id && (
                <button
                  onClick={() => {
                    if (confirm('Bạn có chắc chắn muốn xóa bài viết này?')) {
                      onDelete?.(localPost._id);
                    }
                    setShowMoreMenu(false);
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 flex items-center space-x-2 transition-colors"
                >
                  <Trash2 className="h-4 w-4" />
                  <span>Xóa bài viết</span>
                </button>
              )}

              {/* REPORT BUTTON - Only show for other users' posts */}
              {currentUserId !== localPost.user?._id && (
                <button
                  onClick={() => {
                    onReport?.();
                    // Close menu after a small delay to ensure click is processed
                    setTimeout(() => {
                      setShowMoreMenu(false);
                    }, 50);
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 transition-colors"
                  style={{
                    cursor: 'pointer'
                  }}
                >
                  <Flag className="h-4 w-4" />
                  <span>Báo cáo bài viết</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Admin Report Status */}
      {isAdmin && localPost.reportCount && localPost.reportCount > 0 && (
        <div className="mx-4 mb-3 bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Flag className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-800">
                Bài viết này có {localPost.reportCount} báo cáo
              </span>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => {
                  // TODO: Implement dismiss report
                  console.log('Dismiss reports for post:', localPost._id);
                }}
                className="text-xs px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
              >
                Bỏ qua
              </button>
              <button
                onClick={() => {
                  // TODO: Implement remove post
                  console.log('Remove post:', localPost._id);
                }}
                className="text-xs px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
              >
                Xóa bài viết
              </button>
            </div>
          </div>
          {localPost.reports && localPost.reports.length > 0 && (
            <div className="mt-2 text-xs text-red-700">
              <div className="font-medium mb-1">Lý do báo cáo:</div>
              {localPost.reports.slice(0, 3).map((report, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <span>• {report.reason}</span>
                  <span className="text-red-500">- {report.user.name}</span>
                </div>
              ))}
              {localPost.reports.length > 3 && (
                <div className="text-red-600 font-medium">
                  +{localPost.reports.length - 3} báo cáo khác
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Post Content */}
      <div className="px-4 pb-3">
        <p className="text-gray-800 whitespace-pre-wrap">{localPost.content}</p>
      </div>

      {/* Post Media */}
      {localPost.media && localPost.media.length > 0 && (
        <div className="px-4 pb-3">
          <div className="grid gap-2">
            {localPost.media.map((item, index) => {
              // Handle both string[] (base64) and object[] formats
              const mediaUrl = typeof item === 'string' ? item : item.url;
              const mediaType = typeof item === 'string'
                ? (item.startsWith('data:video/') ? 'video' : 'image')
                : item.type;

              return (
                <div key={index} className="relative">
                  {mediaType === 'image' ? (
                    <img
                      src={mediaUrl}
                      alt="Post media"
                      className="w-full h-auto rounded-lg object-cover max-h-96"
                      onError={(e) => {
                        console.error('Image load error:', e);
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  ) : (
                    <video
                      src={mediaUrl}
                      controls
                      className="w-full h-auto rounded-lg max-h-96"
                      onError={(e) => {
                        console.error('Video load error:', e);
                      }}
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Reactions Summary */}
      {totalReactions > 0 && reactionSummary && reactionSummary.length > 0 && (
        <div className="px-4 pb-2">
          <div
            className="flex items-center justify-between cursor-pointer hover:bg-gray-50 rounded p-1 -m-1"
            onClick={() => setShowReactionsModal(true)}
          >
            <div className="flex items-center space-x-2">
              <div className="flex -space-x-1">
                {reactionSummary.map((reaction, index) => (
                  <span
                    key={index}
                    className="text-sm bg-white rounded-full border border-gray-200 w-6 h-6 flex items-center justify-center hover:scale-110 transition-transform duration-200"
                    title={`${reaction.count} ${reactions.find(r => r.type === reaction.type)?.label}`}
                  >
                    {reaction.icon}
                  </span>
                ))}
              </div>
              <span className="text-sm text-gray-500 font-medium hover:underline">
                {totalReactions}
              </span>
            </div>
            {getTotalCommentsCount() > 0 && (
              <span
                className="text-sm text-gray-500 hover:underline cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowCommentsModal(true);
                }}
              >
                {getTotalCommentsCount()} bình luận
              </span>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="border-t border-gray-100 px-4 py-2">
        <div className="flex items-center justify-between">
          {/* Like Button */}
          <div className="relative flex-1">
            <button
              onClick={handleLikeClick}
              onMouseEnter={handleLikeMouseEnter}
              onMouseLeave={handleLikeMouseLeave}
              disabled={isReacting}
              className={`flex items-center justify-center space-x-2 py-2 px-4 rounded-lg hover:bg-gray-100 reaction-transition w-full disabled:opacity-50 ${
                currentReaction ? `${currentReaction.color} like-button-animated` : 'text-gray-600'
              } ${
                reactionAnimation === 'like-click' ? 'like-click-animation' : ''
              } ${
                reactionAnimation === 'like-success' ? 'like-success-animation' : ''
              } ${
                reactionAnimation?.startsWith('reaction-success') ? 'reaction-success-animation' : ''
              }`}
            >
              {isReacting ? (
                <>
                  <div className="reaction-loading rounded-full h-4 w-4 border-b-2 border-current"></div>
                  <span className="font-medium">Đang xử lý...</span>
                </>
              ) : currentReaction ? (
                <>
                  <span className="text-lg reaction-transition">{currentReaction.icon}</span>
                  <span className="font-medium reaction-color-transition">{currentReaction.label}</span>
                </>
              ) : (
                <>
                  <ThumbsUp className="h-5 w-5 reaction-transition" />
                  <span className="font-medium reaction-color-transition">Thích</span>
                </>
              )}
            </button>

            {/* Reactions Popup */}
            {showReactions && (
              <div
                onMouseEnter={handleReactionsMouseEnter}
                onMouseLeave={handleReactionsMouseLeave}
                className="reaction-popup absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 bg-white rounded-full px-4 py-3 flex items-center space-x-1 reaction-popup-shadow"
                style={{ zIndex: 9999 }}
              >
                {reactions.map((reaction, index) => (
                  <button
                    key={reaction.type}
                    onClick={() => handleReactionSelect(reaction.type)}
                    className={`reaction-button relative p-2 rounded-full reaction-transition reaction-element ${
                      post.userReaction === reaction.type
                        ? 'selected bg-blue-50 reaction-active scale-110'
                        : 'hover:bg-gray-50 hover:scale-125'
                    }`}
                    title={reaction.label}
                    style={{
                      animationDelay: `${index * 50}ms`,
                    }}
                  >
                    <span className="text-2xl block select-none reaction-icon">{reaction.icon}</span>
                    {post.userReaction === reaction.type && (
                      <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-500 rounded-full reaction-active"></div>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Comment Button */}
          <button
            onClick={handleCommentsClick}
            className="flex items-center justify-center space-x-2 py-2 px-4 rounded-lg hover:bg-gray-100 transition-colors text-gray-600 flex-1"
          >
            <MessageCircle className="h-5 w-5" />
            <span className="font-medium">Bình luận</span>
            {getTotalCommentsCount() > 0 && (
              <span className="text-sm text-gray-500 ml-1">({getTotalCommentsCount()})</span>
            )}
          </button>

          {/* Share Button */}
          <button
            onClick={onShare}
            className="flex items-center justify-center space-x-2 py-2 px-4 rounded-lg hover:bg-gray-100 transition-colors text-gray-600 flex-1"
          >
            <Share2 className="h-5 w-5" />
            <span className="font-medium">Chia sẻ</span>
          </button>
        </div>
      </div>

      {/* Comments Section - Disabled, using modal only */}
      {false && (
        <div className="px-4 pb-4 border-t border-gray-100">
          {/* Show existing comments first */}
          {post.comments && post.comments.length > 0 && (
            <div className="py-3">
              <CommentList
                comments={post.comments || []}
                onReactToComment={(commentId, reaction) => {
                  // TODO: Implement comment reactions
                  console.log('React to comment:', commentId, reaction);
                }}
                onReplyToComment={(commentId, content) => {
                  // TODO: Implement comment replies
                  console.log('Reply to comment:', commentId, content);
                }}
                onReportComment={(commentId, reason) => {
                  // TODO: Implement comment reporting
                  console.log('Report comment:', commentId, reason);
                }}
                currentUserId={currentUserId}
              />
            </div>
          )}

          {/* Comment form at bottom like Facebook */}
          {onComment && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <CommentForm onComment={(content: string) => onComment?.(post._id, content)} />
            </div>
          )}
        </div>
      )}

      {/* Reactions Modal */}
      <ReactionsModal
        isOpen={showReactionsModal}
        onClose={() => setShowReactionsModal(false)}
        reactions={localPost.reactions || []}
        postId={localPost._id}
      />

      {/* Comments Modal */}
      <CommentsModal
        isOpen={showCommentsModal}
        onClose={() => setShowCommentsModal(false)}
        comments={localPost.comments as any || []}
        postId={localPost._id}
        onCreateComment={async (postId, content) => {
          try {
            // Optimistic update - add comment immediately
            const tempComment: Comment = {
              _id: `temp-${Date.now()}`,
              user: {
                _id: currentUserId || 'current-user',
                name: 'Bạn',
                avatar: undefined
              },
              content,
              createdAt: new Date().toISOString(),
              reactions: [],
              replies: []
            };

            // Update local state immediately
            setLocalPost(prev => ({
              ...prev,
              comments: [...(prev.comments || []), tempComment]
            }));

            const response = await fetch(`http://localhost:5001/api/posts/${postId}/comment`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              },
              body: JSON.stringify({ content })
            });

            const data = await response.json();

            if (data.success) {
              // Replace temp comment with real one
              setLocalPost(prev => ({
                ...prev,
                comments: prev.comments?.map(c =>
                  c._id === tempComment._id ? data.data.comment : c
                ) || []
              }));

              // Refresh posts in background
              if (typeof fetchPosts === 'function') {
                fetchPosts();
              }
            } else {
              // Remove temp comment on error
              setLocalPost(prev => ({
                ...prev,
                comments: prev.comments?.filter(c => c._id !== tempComment._id) || []
              }));
              console.error('❌ Comment creation failed:', data.message);
            }
          } catch (error) {
            // Remove temp comment on error
            setLocalPost(prev => ({
              ...prev,
              comments: prev.comments?.filter(c => !c._id.startsWith('temp-')) || []
            }));
            console.error('❌ Error creating comment:', error);
          }
        }}
        onReactToComment={async (commentId, reactionType) => {
          try {
            // Optimistic update for comment reactions
            const updateCommentReaction = (comments: Comment[]): Comment[] => {
              return comments.map(comment => {
                if (comment._id === commentId) {
                  const currentReactions = comment.reactions || [];
                  const userReactionIndex = currentReactions.findIndex(r => r.user === currentUserId);

                  let newReactions = [...currentReactions];
                  let newUserReaction = reactionType;

                  if (userReactionIndex >= 0) {
                    if (currentReactions[userReactionIndex].type === reactionType) {
                      // Remove reaction
                      newReactions.splice(userReactionIndex, 1);
                      newUserReaction = '';
                    } else {
                      // Update reaction
                      newReactions[userReactionIndex] = { type: reactionType, user: currentUserId || '' };
                    }
                  } else if (reactionType) {
                    // Add new reaction
                    newReactions.push({ type: reactionType, user: currentUserId || '' });
                  }

                  return {
                    ...comment,
                    reactions: newReactions,
                    userReaction: newUserReaction
                  };
                }

                // Check replies
                if (comment.replies) {
                  return {
                    ...comment,
                    replies: updateCommentReaction(comment.replies)
                  };
                }

                return comment;
              });
            };

            // Update local state immediately
            setLocalPost(prev => ({
              ...prev,
              comments: updateCommentReaction(prev.comments || [])
            }));

            const response = await fetch(`http://localhost:5001/api/posts/comments/${commentId}/react`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              },
              body: JSON.stringify({ type: reactionType })
            });

            const data = await response.json();

            if (data.success) {
              // Refresh posts in background
              if (typeof fetchPosts === 'function') {
                fetchPosts();
              }
            } else {
              console.error('❌ Comment reaction failed:', data.message);
              // Revert optimistic update on error
              setLocalPost(post);
            }
          } catch (error) {
            console.error('❌ Error reacting to comment:', error);
            // Revert optimistic update on error
            setLocalPost(post);
          }
        }}
        onReplyToComment={async (commentId, content) => {
          try {
            // Optimistic update - add reply immediately
            const tempReply: Comment = {
              _id: `temp-reply-${Date.now()}`,
              user: {
                _id: currentUserId || 'current-user',
                name: 'Bạn',
                avatar: undefined
              },
              content,
              createdAt: new Date().toISOString(),
              reactions: [],
              replies: []
            };

            // Update local state immediately
            setLocalPost(prev => ({
              ...prev,
              comments: prev.comments?.map(comment => {
                if (comment._id === commentId) {
                  return {
                    ...comment,
                    replies: [...(comment.replies || []), tempReply]
                  };
                }
                return comment;
              }) || []
            }));

            const response = await fetch(`http://localhost:5001/api/posts/comments/${commentId}/reply`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              },
              body: JSON.stringify({ content })
            });

            const data = await response.json();

            if (data.success) {
              // Replace temp reply with real one
              setLocalPost(prev => ({
                ...prev,
                comments: prev.comments?.map(comment => {
                  if (comment._id === commentId) {
                    return {
                      ...comment,
                      replies: comment.replies?.map(reply =>
                        reply._id === tempReply._id ? data.data.reply : reply
                      ) || []
                    };
                  }
                  return comment;
                }) || []
              }));

              // Refresh posts in background
              if (typeof fetchPosts === 'function') {
                fetchPosts();
              }
            } else {
              // Remove temp reply on error
              setLocalPost(prev => ({
                ...prev,
                comments: prev.comments?.map(comment => {
                  if (comment._id === commentId) {
                    return {
                      ...comment,
                      replies: comment.replies?.filter(reply => reply._id !== tempReply._id) || []
                    };
                  }
                  return comment;
                }) || []
              }));
              console.error('❌ Comment reply failed:', data.message);
            }
          } catch (error) {
            // Remove temp reply on error
            setLocalPost(prev => ({
              ...prev,
              comments: prev.comments?.map(comment => {
                if (comment._id === commentId) {
                  return {
                    ...comment,
                    replies: comment.replies?.filter(reply => !reply._id.startsWith('temp-reply-')) || []
                  };
                }
                return comment;
              }) || []
            }));
            console.error('❌ Error replying to comment:', error);
          }
        }}
        onDeleteComment={async (commentId) => {
          try {
            // Optimistic update - remove comment immediately
            setLocalPost(prev => ({
              ...prev,
              comments: prev.comments?.filter(comment => comment._id !== commentId) || []
            }));

            const response = await fetch(`http://localhost:5001/api/posts/comments/${commentId}`, {
              method: 'DELETE',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              }
            });

            const data = await response.json();

            if (data.success) {
              // Refresh posts in background
              if (typeof fetchPosts === 'function') {
                fetchPosts();
              }
            } else {
              // Revert optimistic update on error
              setLocalPost(post);
              console.error('❌ Comment deletion failed:', data.message);
            }
          } catch (error) {
            // Revert optimistic update on error
            setLocalPost(post);
            console.error('❌ Error deleting comment:', error);
          }
        }}
        onReportComment={async (commentId, reason) => {
          try {
            const response = await fetch(`http://localhost:5001/api/posts/comments/${commentId}/report`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              },
              body: JSON.stringify({ reason })
            });

            const data = await response.json();

            if (data.success) {
              alert('Đã gửi báo cáo thành công');
            } else {
              alert('Có lỗi xảy ra khi gửi báo cáo: ' + data.message);
            }
          } catch (error) {
            console.error('❌ Error reporting comment:', error);
            alert('Có lỗi xảy ra khi gửi báo cáo');
          }
        }}
        currentUserId={currentUserId}
      />
    </div>
  );
};

export default PostCard;
