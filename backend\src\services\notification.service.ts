import { Notification, INotification } from '../models/Notification';
import { User } from '../models/user.model';
import { Types } from 'mongoose';
import { sendEmail } from './emailService';
import { io } from '../index';

interface CreateNotificationParams {
  userId: Types.ObjectId;
  type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
  title: string;
  message: string;
  data?: {
    campaignId?: Types.ObjectId;
    donationId?: Types.ObjectId;
    eventId?: Types.ObjectId;
    postId?: Types.ObjectId;
    commentId?: Types.ObjectId;
    reportId?: Types.ObjectId;
    amount?: number;
    status?: string;
    reason?: string;
    actionType?: 'created' | 'updated' | 'deleted' | 'cancelled' | 'completed' | 'reminder';
    relatedUserId?: Types.ObjectId;
    relatedUserName?: string;
  };
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  sendEmail?: boolean;
}

export const createNotification = async (params: CreateNotificationParams) => {
  try {
    const notification = new Notification({
      ...params,
      isRead: false,
      emailSent: false,
      priority: params.priority || 'medium'
    });
    await notification.save();

    // Emit real-time notification
    io.to(`user-${params.userId}`).emit('new_notification', notification);

    // Send email if required and user has email notifications enabled
    if (params.sendEmail !== false) {
      await sendNotificationEmail(notification);
    }

    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

// Send notification email
const sendNotificationEmail = async (notification: INotification) => {
  try {
    const user = await User.findById(notification.userId);
    if (!user || !user.preferences?.emailNotifications) {
      return;
    }

    // Determine if this notification type should send email
    const emailTypes = ['campaign', 'event', 'system', 'admin', 'comment'];
    if (!emailTypes.includes(notification.type)) {
      return;
    }

    await sendEmail({
      to: user.email,
      subject: notification.title,
      template: 'notification',
      data: {
        userName: user.name,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        createdAt: notification.createdAt
      }
    });

    // Mark email as sent
    await Notification.findByIdAndUpdate(notification._id, { emailSent: true });
  } catch (error) {
    console.error('Error sending notification email:', error);
  }
};

export const getUnreadNotifications = async (userId: Types.ObjectId) => {
  try {
    return await Notification.find({ userId, isRead: false })
      .sort({ createdAt: -1 })
      .limit(50);
  } catch (error) {
    console.error('Error getting unread notifications:', error);
    throw error;
  }
};

export const markNotificationAsRead = async (notificationId: string, userId: Types.ObjectId) => {
  try {
    return await Notification.findOneAndUpdate(
      { _id: notificationId, userId },
      { isRead: true },
      { new: true }
    );
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

export const createReportNotificationForAdmins = async (reportId: Types.ObjectId, postId: Types.ObjectId, reporterName: string, reason: string) => {
  try {
    // Import User model to find admins
    const { User } = await import('../models/user.model');

    // Find all admin users
    const admins = await User.find({ role: 'admin' }).select('_id');

    // Create notification for each admin
    const notifications = await Promise.all(
      admins.map(admin =>
        createNotification({
          userId: admin._id,
          type: 'report',
          title: 'Báo cáo bài viết mới',
          message: `${reporterName} đã báo cáo một bài viết với lý do: ${reason}`,
          data: {
            reportId,
            postId
          }
        })
      )
    );

    return notifications;
  } catch (error) {
    console.error('Error creating report notifications for admins:', error);
    throw error;
  }
};

export const markAllNotificationsAsRead = async (userId: Types.ObjectId) => {
  try {
    return await Notification.updateMany(
      { userId, isRead: false },
      { isRead: true }
    );
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
};

export const createCommentReportNotificationForAdmins = async (
  reportId: Types.ObjectId,
  commentId: Types.ObjectId,
  postId: Types.ObjectId,
  reporterName: string,
  reason: string
) => {
  try {
    // Import User model to find admins
    const { User } = await import('../models/user.model');

    // Find all admin users
    const admins = await User.find({ role: 'admin' }).select('_id');

    // Create notification for each admin
    const notifications = await Promise.all(
      admins.map(admin =>
        createNotification({
          userId: admin._id,
          type: 'report',
          title: 'Báo cáo comment mới',
          message: `${reporterName} đã báo cáo một comment với lý do: ${reason}`,
          data: {
            reportId,
            commentId,
            postId
          }
        })
      )
    );

    return notifications;
  } catch (error) {
    console.error('Error creating comment report notifications for admins:', error);
    throw error;
  }
};

export const deleteNotification = async (notificationId: string, userId: Types.ObjectId) => {
  try {
    return await Notification.findOneAndDelete({ _id: notificationId, userId });
  } catch (error) {
    console.error('Error deleting notification:', error);
    throw error;
  }
};

// Notification helpers for specific events
export const notifyNewComment = async (postOwnerId: Types.ObjectId, commenterName: string, postId: Types.ObjectId) => {
  return createNotification({
    userId: postOwnerId,
    type: 'comment',
    title: 'Bình luận mới',
    message: `${commenterName} đã bình luận vào bài viết của bạn`,
    data: {
      postId,
      actionType: 'created',
      relatedUserName: commenterName
    },
    priority: 'low',
    sendEmail: false // Don't send email for comments
  });
};

export const notifyCampaignDeleted = async (donorIds: Types.ObjectId[], campaignTitle: string, campaignId: Types.ObjectId, reason?: string) => {
  const notifications = donorIds.map(donorId =>
    createNotification({
      userId: donorId,
      type: 'campaign',
      title: '📢 Thông báo quan trọng về chiến dịch',
      message: `Kính gửi quý nhà hảo tâm,

Chúng tôi rất tiếc phải thông báo rằng chiến dịch "${campaignTitle}" đã phải tạm dừng${reason ? ` do ${reason.toLowerCase()}` : ' vì lý do bất khả kháng'}.

🔄 Quyền lợi của bạn:
• Toàn bộ số tiền ủng hộ sẽ được hoàn trả 100%
• Thời gian xử lý: 3-5 ngày làm việc
• Liên hệ hỗ trợ: <EMAIL> hoặc hotline 1900-xxxx

Chúng tôi chân thành cảm ơn tấm lòng hảo tâm của bạn và mong nhận được sự ủng hộ trong các chiến dịch khác.

Trân trọng,
Ban Quản trị KeyDyWeb`,
      data: {
        campaignId,
        actionType: 'cancelled',
        reason
      },
      priority: 'urgent',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyEventDeleted = async (participantIds: Types.ObjectId[], eventTitle: string, eventId: Types.ObjectId, reason?: string) => {
  const notifications = participantIds.map(participantId =>
    createNotification({
      userId: participantId,
      type: 'event',
      title: '📅 Thông báo hủy sự kiện',
      message: `Kính gửi bạn,

Chúng tôi rất tiếc phải thông báo rằng sự kiện "${eventTitle}" đã phải hủy bỏ${reason ? ` do ${reason.toLowerCase()}` : ' vì lý do khách quan'}.

ℹ️ Thông tin quan trọng:
• Bạn không cần tham gia sự kiện này nữa
• Mọi chi phí đã thanh toán (nếu có) sẽ được hoàn trả
• Chúng tôi sẽ thông báo khi có sự kiện thay thế

Chúng tôi chân thành xin lỗi vì sự bất tiện này và cảm ơn sự quan tâm của bạn.

Trân trọng,
Ban Tổ chức KeyDyWeb`,
      data: {
        eventId,
        actionType: 'cancelled',
        reason
      },
      priority: 'urgent',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyPostDeleted = async (userId: Types.ObjectId, postTitle: string, reason: string) => {
  return createNotification({
    userId,
    type: 'post',
    title: 'Bài viết đã bị xóa',
    message: `Bài viết "${postTitle}" của bạn đã bị xóa bởi quản trị viên. Lý do: ${reason}`,
    data: {
      actionType: 'deleted',
      reason
    },
    priority: 'medium',
    sendEmail: false
  });
};

export const notifyCommentDeleted = async (userId: Types.ObjectId, commentContent: string, reason: string) => {
  return createNotification({
    userId,
    type: 'comment',
    title: 'Bình luận đã bị xóa',
    message: `Bình luận "${commentContent.substring(0, 50)}..." của bạn đã bị xóa bởi quản trị viên. Lý do: ${reason}`,
    data: {
      actionType: 'deleted',
      reason
    },
    priority: 'medium',
    sendEmail: false
  });
};

export const notifyNewCampaign = async (userIds: Types.ObjectId[], campaignTitle: string, campaignId: Types.ObjectId) => {
  const notifications = userIds.map(userId =>
    createNotification({
      userId,
      type: 'campaign',
      title: '🌟 Chiến dịch thiện nguyện mới',
      message: `Chào bạn!

Chúng tôi vui mừng thông báo về chiến dịch thiện nguyện mới: "${campaignTitle}"

💝 Đây là cơ hội tuyệt vời để bạn:
• Góp phần tạo nên những thay đổi tích cực
• Lan tỏa tình yêu thương trong cộng đồng
• Nhận được huy hiệu "Người Ủng Hộ" đặc biệt

👆 Nhấn để xem chi tiết và tham gia ủng hộ ngay!

Cảm ơn bạn đã luôn đồng hành cùng KeyDyWeb! 🙏`,
      data: {
        campaignId,
        actionType: 'created'
      },
      priority: 'medium',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyNewEvent = async (userIds: Types.ObjectId[], eventTitle: string, eventId: Types.ObjectId) => {
  const notifications = userIds.map(userId =>
    createNotification({
      userId,
      type: 'event',
      title: '🎉 Sự kiện tình nguyện mới',
      message: `Chào bạn!

Chúng tôi hân hạnh mời bạn tham gia sự kiện: "${eventTitle}"

🤝 Lợi ích khi tham gia:
• Đóng góp ý nghĩa cho cộng đồng
• Kết nối với những người cùng chí hướng
• Nhận huy hiệu "Tình Nguyện Viên" danh giá
• Tích lũy kinh nghiệm quý báu

📅 Đăng ký ngay để không bỏ lỡ cơ hội tuyệt vời này!

Cảm ơn bạn đã quan tâm! 💚`,
      data: {
        eventId,
        actionType: 'created'
      },
      priority: 'medium',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyAdminMessage = async (userIds: Types.ObjectId[], title: string, message: string) => {
  const notifications = userIds.map(userId =>
    createNotification({
      userId,
      type: 'admin',
      title,
      message,
      data: {
        actionType: 'created'
      },
      priority: 'high',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

export const notifyDonationSuccess = async (
  donorEmail: string,
  donorName: string,
  amount: number,
  campaignTitle: string,
  campaignId: Types.ObjectId,
  transactionId: string,
  isAnonymous: boolean = false,
  userId?: Types.ObjectId
) => {
  try {
    // Send email notification
    await sendEmail({
      to: donorEmail,
      subject: 'Cảm ơn bạn đã quyên góp - KeyDyWeb',
      template: 'donation-thank-you',
      data: {
        donorName: isAnonymous ? 'Người ủng hộ ẩn danh' : donorName,
        amount: new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(amount),
        campaignTitle,
        transactionId,
        donationDate: new Date().toLocaleDateString('vi-VN'),
        isAnonymous
      }
    });

    console.log('📧 [Donation] Thank you email sent to:', donorEmail);

    // Send in-app notification if user is logged in
    if (userId) {
      await createNotification({
        userId,
        type: 'donation',
        title: '💜 Cảm ơn tấm lòng hảo tâm của bạn!',
        message: `Chân thành cảm ơn bạn đã quyên góp ${new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(amount)} cho chiến dịch "${campaignTitle}".

🌟 Sự đóng góp của bạn không chỉ là tiền bạc, mà còn là tình yêu thương và hy vọng cho những người cần được giúp đỡ.

💝 Bạn đã trở thành một phần của hành trình tạo nên những thay đổi tích cực trong cộng đồng. Hãy tiếp tục đồng hành cùng chúng tôi trong các chiến dịch khác!

Trân trọng cảm ơn,
Đội ngũ KeyDyWeb`,
        data: {
          campaignId,
          amount,
          actionType: 'completed',
          status: 'success'
        },
        priority: 'medium',
        sendEmail: false // Already sent email above
      });

      console.log('📱 [Donation] In-app notification sent to user:', userId);
    }
  } catch (error) {
    console.error('❌ [Donation] Error sending thank you notifications:', error);
    throw error;
  }
};

// Campaign milestone notifications
export const notifyCampaignMilestone = async (
  donorIds: Types.ObjectId[],
  campaignTitle: string,
  campaignId: Types.ObjectId,
  milestone: number,
  currentAmount: number,
  targetAmount: number
) => {
  const milestoneEmojis = {
    25: '🌱',
    50: '🌿',
    75: '🌳',
    100: '🎊'
  };

  const milestoneMessages = {
    25: 'Khởi đầu tuyệt vời!',
    50: 'Đã đi được nửa chặng đường!',
    75: 'Sắp đến đích rồi!',
    100: 'Hoàn thành mục tiêu!'
  };

  const notifications = donorIds.map(donorId =>
    createNotification({
      userId: donorId,
      type: 'campaign',
      title: `${milestoneEmojis[milestone as keyof typeof milestoneEmojis]} ${milestoneMessages[milestone as keyof typeof milestoneMessages]}`,
      message: `Kính gửi nhà hảo tâm,

Chúng tôi vui mừng thông báo chiến dịch "${campaignTitle}" đã đạt ${milestone}% mục tiêu!

📊 Tiến độ hiện tại:
• Đã quyên góp: ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(currentAmount)}
• Mục tiêu: ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(targetAmount)}
• Hoàn thành: ${milestone}%

💝 Thành công này có một phần đóng góp quý báu của bạn. Sự ủng hộ của bạn đã và đang tạo nên những thay đổi tích cực trong cộng đồng.

${milestone < 100 ? '🚀 Hãy tiếp tục chia sẻ để cùng nhau hoàn thành mục tiêu!' : '🎉 Cảm ơn bạn đã giúp chúng tôi hoàn thành chiến dịch này!'}

Trân trọng cảm ơn,
Đội ngũ KeyDyWeb`,
      data: {
        campaignId,
        actionType: 'updated',
        amount: currentAmount
      },
      priority: 'medium',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

// Campaign deadline reminder
export const notifyCampaignDeadline = async (
  userIds: Types.ObjectId[],
  campaignTitle: string,
  campaignId: Types.ObjectId,
  daysLeft: number
) => {
  const notifications = userIds.map(userId =>
    createNotification({
      userId,
      type: 'campaign',
      title: 'Chiến dịch sắp kết thúc! ⏰',
      message: `Chiến dịch "${campaignTitle}" chỉ còn ${daysLeft} ngày để kết thúc. Hãy nhanh tay ủng hộ!`,
      data: {
        campaignId,
        actionType: 'reminder'
      },
      priority: 'high',
      sendEmail: true
    })
  );
  return Promise.all(notifications);
};

// Event reminder notifications
export const notifyEventReminder = async (
  participantIds: Types.ObjectId[],
  eventTitle: string,
  eventId: Types.ObjectId,
  eventDate: Date,
  reminderType: '7days' | '1day' | '1hour'
) => {
  const reminderMessages = {
    '7days': 'Sự kiện sẽ diễn ra trong 7 ngày tới',
    '1day': 'Sự kiện sẽ diễn ra vào ngày mai',
    '1hour': 'Sự kiện sẽ bắt đầu trong 1 giờ nữa'
  };

  const priorities = {
    '7days': 'medium' as const,
    '1day': 'high' as const,
    '1hour': 'urgent' as const
  };

  const notifications = participantIds.map(participantId =>
    createNotification({
      userId: participantId,
      type: 'event',
      title: 'Nhắc nhở sự kiện 📅',
      message: `${reminderMessages[reminderType]}. Sự kiện "${eventTitle}" - ${eventDate.toLocaleDateString('vi-VN')}. Đừng quên tham gia nhé!`,
      data: {
        eventId,
        actionType: 'reminder'
      },
      priority: priorities[reminderType],
      sendEmail: reminderType !== '7days' // Send email for 1day and 1hour reminders
    })
  );
  return Promise.all(notifications);
};

// User account security notifications
export const notifySecurityAlert = async (
  userId: Types.ObjectId,
  alertType: 'login' | 'password_change' | 'email_change',
  details: string
) => {
  const titles = {
    'login': 'Đăng nhập từ thiết bị mới',
    'password_change': 'Mật khẩu đã được thay đổi',
    'email_change': 'Email đã được thay đổi'
  };

  const messages = {
    'login': `Tài khoản của bạn vừa được đăng nhập từ: ${details}. Nếu không phải bạn, hãy thay đổi mật khẩu ngay.`,
    'password_change': `Mật khẩu tài khoản của bạn đã được thay đổi lúc ${details}. Nếu không phải bạn, hãy liên hệ hỗ trợ ngay.`,
    'email_change': `Email tài khoản của bạn đã được thay đổi thành ${details}. Nếu không phải bạn, hãy liên hệ hỗ trợ ngay.`
  };

  return createNotification({
    userId,
    type: 'system',
    title: titles[alertType],
    message: messages[alertType],
    data: {
      actionType: 'updated'
    },
    priority: 'urgent',
    sendEmail: true
  });
};