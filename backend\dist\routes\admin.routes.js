"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const adminController = __importStar(require("../controllers/admin.controller"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const express_validator_1 = require("express-validator");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const reviewAdminController_1 = require("../controllers/reviewAdminController");
const router = (0, express_1.Router)();
// Validation middleware
const getPostsValidation = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page phải là số nguyên dương'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit phải từ 1-100'),
    (0, express_validator_1.query)('search')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Từ khóa tìm kiếm tối đa 100 ký tự'),
    (0, express_validator_1.query)('status')
        .optional()
        .isIn(['all', 'reported', 'recent'])
        .withMessage('Status phải là all, reported hoặc recent')
];
const deletePostValidation = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('ID bài viết không hợp lệ')
];
/**
 * @route GET /admin/posts
 * @desc Lấy danh sách tất cả bài viết cho admin
 * @access Private (Admin only)
 * @query page - Trang hiện tại (default: 1)
 * @query limit - Số bài viết mỗi trang (default: 20, max: 100)
 * @query search - Từ khóa tìm kiếm
 * @query status - Lọc theo trạng thái (all/reported/recent)
 */
router.get('/posts', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, getPostsValidation, validation_middleware_1.validateRequest, adminController.getAllPosts);
/**
 * @route DELETE /admin/posts/:id
 * @desc Xóa bài viết (chỉ admin)
 * @access Private (Admin only)
 */
router.delete('/posts/:id', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, deletePostValidation, validation_middleware_1.validateRequest, adminController.deletePost);
/**
 * @route DELETE /admin/posts-test/:id
 * @desc Xóa bài viết (test endpoint không cần auth)
 * @access Public (for testing)
 */
router.delete('/posts-test/:id', deletePostValidation, validation_middleware_1.validateRequest, adminController.deletePost);
/**
 * @route DELETE /admin/posts/:postId/comments/:commentId
 * @desc Xóa comment (chỉ admin)
 * @access Private (Admin only)
 */
router.delete('/posts/:postId/comments/:commentId', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.param)('postId').isMongoId().withMessage('ID bài viết không hợp lệ'), (0, express_validator_1.param)('commentId').isMongoId().withMessage('ID comment không hợp lệ'), validation_middleware_1.validateRequest, adminController.deleteComment);
/**
 * @route POST /admin/posts/:id/reports/resolve
 * @desc Xử lý báo cáo bài viết
 * @access Private (Admin only)
 */
router.post('/posts/:id/reports/resolve', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.param)('id').isMongoId().withMessage('ID bài viết không hợp lệ'), (0, express_validator_1.body)('action').isIn(['dismiss', 'remove']).withMessage('Action phải là dismiss hoặc remove'), validation_middleware_1.validateRequest, adminController.resolveReport);
// Donations management
router.get('/donations', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, adminController.getAllDonations);
// Campaigns management
router.get('/campaigns', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, adminController.getAllCampaigns);
// Users management
router.get('/users', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, adminController.getAllUsers);
router.get('/users/:id', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, adminController.getUserById);
router.put('/users/:id/status', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, adminController.updateUserStatus);
router.put('/users/:id/role', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, adminController.updateUserRole);
router.delete('/users/:id', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, adminController.deleteUser);
// Online status tracking
router.get('/online-status', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, adminController.getOnlineStatus);
// Test users endpoint with new auth middleware (for development only)
router.get('/users-auth-test', auth_middleware_1.authMiddleware, adminController.getAllUsers);
// Dashboard stats
router.get('/dashboard/stats', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, adminController.getDashboardStats);
// Test dashboard stats (no auth for development)
router.get('/dashboard/stats-test', adminController.getDashboardStats);
// Test posts endpoint (no auth for development)
router.get('/posts-test', adminController.getPostsTest);
// Test delete post endpoint (no auth for development)
router.delete('/posts-test/:id', adminController.deletePostTest);
// Simple test delete endpoint without validation
router.delete('/posts-simple/:id', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id } = req.params;
        console.log('🗑️ [Simple Test] Deleting post:', id);
        // Import Post model
        const { Post } = require('../models/Post');
        // Find and delete the post
        const deletedPost = yield Post.findByIdAndDelete(id);
        if (!deletedPost) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài viết'
            });
        }
        console.log('✅ [Simple Test] Post deleted successfully');
        res.json({
            success: true,
            message: 'Đã xóa bài viết thành công (simple test)',
            data: {
                deletedPost: {
                    _id: deletedPost._id,
                    content: ((_a = deletedPost.content) === null || _a === void 0 ? void 0 : _a.substring(0, 50)) + '...'
                }
            }
        });
    }
    catch (error) {
        console.error('❌ [Simple Test] Error deleting post:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi xóa bài viết',
            error: error.message
        });
    }
}));
/**
 * @route GET /admin/comment-reports
 * @desc Lấy danh sách báo cáo comment
 * @access Private (Admin only)
 * @query page - Trang hiện tại (default: 1)
 * @query limit - Số báo cáo mỗi trang (default: 20)
 * @query status - Lọc theo trạng thái (pending/resolved/dismissed/all)
 */
router.get('/comment-reports', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.query)('page').optional().isInt({ min: 1 }).withMessage('Page phải là số nguyên dương'), (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit phải từ 1-100'), (0, express_validator_1.query)('status').optional().isIn(['pending', 'resolved', 'dismissed', 'all']).withMessage('Status không hợp lệ'), validation_middleware_1.validateRequest, adminController.getCommentReports);
/**
 * @route POST /admin/comment-reports/:reportId/resolve
 * @desc Xử lý báo cáo comment
 * @access Private (Admin only)
 */
router.post('/comment-reports/:reportId/resolve', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.param)('reportId').isMongoId().withMessage('ID báo cáo không hợp lệ'), (0, express_validator_1.body)('action').isIn(['dismiss', 'remove_comment']).withMessage('Action phải là dismiss hoặc remove_comment'), validation_middleware_1.validateRequest, adminController.resolveCommentReport);
/**
 * @route GET /admin/comment-reports/:reportId/test
 * @desc Test endpoint để debug comment report resolution
 * @access Private (Admin only)
 */
router.get('/comment-reports/:reportId/test', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.param)('reportId').isMongoId().withMessage('ID báo cáo không hợp lệ'), validation_middleware_1.validateRequest, adminController.testCommentReportResolution);
/**
 * @route GET /admin/posts/:postId/comments
 * @desc Lấy danh sách comment của bài viết với thông tin báo cáo
 * @access Private (Admin only)
 */
router.get('/posts/:postId/comments', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.param)('postId').isMongoId().withMessage('ID bài viết không hợp lệ'), (0, express_validator_1.query)('page').optional().isInt({ min: 1 }).withMessage('Page phải là số nguyên dương'), (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit phải từ 1-100'), validation_middleware_1.validateRequest, adminController.getPostComments);
// Review management routes
/**
 * @route GET /admin/reviews
 * @desc Lấy tất cả đánh giá (admin)
 * @access Private (Admin only)
 */
router.get('/reviews', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.query)('page').optional().isInt({ min: 1 }).withMessage('Page phải là số nguyên dương'), (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit phải từ 1-100'), (0, express_validator_1.query)('search').optional().trim().isLength({ max: 100 }).withMessage('Từ khóa tìm kiếm tối đa 100 ký tự'), (0, express_validator_1.query)('rating').optional().isIn(['1', '2', '3', '4', '5', 'all']).withMessage('Rating không hợp lệ'), (0, express_validator_1.query)('eventId').optional().isMongoId().withMessage('Event ID không hợp lệ'), validation_middleware_1.validateRequest, reviewAdminController_1.getAllReviews);
/**
 * @route POST /admin/reviews/:id/respond
 * @desc Phản hồi đánh giá (admin)
 * @access Private (Admin only)
 */
router.post('/reviews/:id/respond', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.param)('id').isMongoId().withMessage('Review ID không hợp lệ'), (0, express_validator_1.body)('response').trim().isLength({ min: 1, max: 1000 }).withMessage('Phản hồi phải từ 1-1000 ký tự'), validation_middleware_1.validateRequest, reviewAdminController_1.respondToReview);
/**
 * @route DELETE /admin/reviews/:id
 * @desc Xóa đánh giá (admin)
 * @access Private (Admin only)
 */
router.delete('/reviews/:id', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.param)('id').isMongoId().withMessage('Review ID không hợp lệ'), validation_middleware_1.validateRequest, reviewAdminController_1.deleteReview);
/**
 * @route GET /admin/reviews/stats
 * @desc Lấy thống kê đánh giá (admin)
 * @access Private (Admin only)
 */
router.get('/reviews/stats', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, reviewAdminController_1.getReviewStats);
/**
 * @route GET /admin/events/:eventId/reviews
 * @desc Lấy đánh giá theo sự kiện (admin)
 * @access Private (Admin only)
 */
router.get('/events/:eventId/reviews', auth_middleware_1.verifyToken, auth_middleware_1.requireAdmin, (0, express_validator_1.param)('eventId').isMongoId().withMessage('Event ID không hợp lệ'), (0, express_validator_1.query)('page').optional().isInt({ min: 1 }).withMessage('Page phải là số nguyên dương'), (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit phải từ 1-100'), validation_middleware_1.validateRequest, reviewAdminController_1.getReviewsByEvent);
exports.default = router;
