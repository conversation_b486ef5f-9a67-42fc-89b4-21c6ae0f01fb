import cron from 'node-cron';
import { Campaign } from '../models/Campaign';
import { User } from '../models/user.model';
import { notifyCampaignDeadline } from '../services/notification.service';

// Run every day at 9:00 AM
export const campaignDeadlineReminder = cron.createTask('0 9 * * *', async () => {
  try {
    console.log('🔔 [Cron] Running campaign deadline reminder job...');
    
    const now = new Date();
    const threeDaysFromNow = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000));
    const sevenDaysFromNow = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
    
    // Find campaigns ending in 3 days
    const campaignsEndingIn3Days = await Campaign.find({
      status: 'active',
      endDate: {
        $gte: now,
        $lte: threeDaysFromNow
      }
    });
    
    // Find campaigns ending in 7 days
    const campaignsEndingIn7Days = await Campaign.find({
      status: 'active',
      endDate: {
        $gte: threeDaysFromNow,
        $lte: sevenDaysFromNow
      }
    });
    
    // Get all active users
    const allUsers = await User.find({ 
      role: 'user',
      'preferences.emailNotifications': true 
    }).select('_id');
    const userIds = allUsers.map(user => user._id);
    
    // Send 3-day reminders
    for (const campaign of campaignsEndingIn3Days) {
      const daysLeft = Math.ceil((campaign.endDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000));
      
      if (userIds.length > 0) {
        await notifyCampaignDeadline(
          userIds,
          campaign.title,
          campaign._id,
          daysLeft
        );
        console.log(`📧 [Cron] 3-day reminder sent for campaign: ${campaign.title} (${daysLeft} days left)`);
      }
    }
    
    // Send 7-day reminders
    for (const campaign of campaignsEndingIn7Days) {
      const daysLeft = Math.ceil((campaign.endDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000));
      
      if (userIds.length > 0) {
        await notifyCampaignDeadline(
          userIds,
          campaign.title,
          campaign._id,
          daysLeft
        );
        console.log(`📧 [Cron] 7-day reminder sent for campaign: ${campaign.title} (${daysLeft} days left)`);
      }
    }
    
    console.log(`✅ [Cron] Campaign deadline reminders completed. Processed ${campaignsEndingIn3Days.length + campaignsEndingIn7Days.length} campaigns`);
    
  } catch (error) {
    console.error('❌ [Cron] Error in campaign deadline reminder job:', error);
  }
});

// Start the cron job
export const startCampaignReminderJob = () => {
  campaignDeadlineReminder.start();
  console.log('⏰ [Cron] Campaign deadline reminder job started (daily at 9:00 AM)');
};
