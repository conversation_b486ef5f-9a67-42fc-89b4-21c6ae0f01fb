import mongoose, { Document } from 'mongoose';
export interface INotification extends Document {
    userId: mongoose.Types.ObjectId;
    type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
    title: string;
    message: string;
    data?: {
        campaignId?: mongoose.Types.ObjectId;
        donationId?: mongoose.Types.ObjectId;
        eventId?: mongoose.Types.ObjectId;
        postId?: mongoose.Types.ObjectId;
        commentId?: mongoose.Types.ObjectId;
        reportId?: mongoose.Types.ObjectId;
        amount?: number;
        status?: string;
        reason?: string;
        actionType?: 'created' | 'updated' | 'deleted' | 'cancelled' | 'completed' | 'reminder';
        relatedUserId?: mongoose.Types.ObjectId;
        relatedUserName?: string;
    };
    isRead: boolean;
    emailSent: boolean;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    createdAt: Date;
    updatedAt: Date;
}
export declare const Notification: mongoose.Model<any, {}, {}, {}, any, any>;
//# sourceMappingURL=Notification.d.ts.map