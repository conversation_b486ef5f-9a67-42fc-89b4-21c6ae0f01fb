import { Request, Response } from 'express';
import { Post } from '../models/Post';
import { Donation } from '../models/Donation';
import EventRegistration from '../models/EventRegistration';
import { User } from '../models/user.model';
import mongoose from 'mongoose';

// GET /users/:userId/stats - <PERSON><PERSON><PERSON> thống kê của user
export const getUserStats = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    console.log('📊 Getting stats for user:', userId);

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        success: false,
        message: 'ID người dùng không hợp lệ'
      });
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '<PERSON>hô<PERSON> tìm thấy người dùng'
      });
    }

    // Parallel queries for better performance
    const [
      totalPosts,
      totalDonations,
      totalEvents,
      donationStats,
      postStats
    ] = await Promise.all([
      // Count total posts
      Post.countDocuments({ user: userId }).catch(() => 0),

      // Count total donations
      Donation.countDocuments({ userId, status: 'success' }).catch(() => 0),

      // Count total events registered
      EventRegistration.countDocuments({ userId, status: 'registered' }).catch(() => 0),

      // Get donation amount stats
      Donation.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(userId),
            status: 'success'
          }
        },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$amount' }
          }
        }
      ]).catch(() => []),

      // Get post engagement stats
      Post.aggregate([
        {
          $match: {
            user: new mongoose.Types.ObjectId(userId)
          }
        },
        {
          $group: {
            _id: null,
            totalReactions: { $sum: { $size: '$reactions' } },
            totalComments: { $sum: { $size: '$comments' } }
          }
        }
      ]).catch(() => [])
    ]);

    const totalDonationAmount = donationStats[0]?.totalAmount || 0;
    const totalReactions = postStats[0]?.totalReactions || 0;
    const totalComments = postStats[0]?.totalComments || 0;

    const stats = {
      totalPosts: totalPosts || 0,
      totalReactions: totalReactions || 0,
      totalComments: totalComments || 0,
      totalDonations: totalDonations || 0,
      totalEvents: totalEvents || 0,
      totalDonationAmount: totalDonationAmount || 0
    };

    console.log(`📊 User stats for ${userId}:`, stats);

    res.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    console.error('❌ Error getting user stats:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy thống kê người dùng',
      error: error.message
    });
  }
};
