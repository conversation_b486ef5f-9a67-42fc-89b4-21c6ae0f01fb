import { Request, Response } from 'express';
import { Post } from '../models/Post';
import { Donation } from '../models/Donation';
import EventRegistration from '../models/EventRegistration';
import mongoose from 'mongoose';

// GET /users/:userId/stats - <PERSON><PERSON><PERSON> thống kê của user
export const getUserStats = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        success: false,
        message: 'ID người dùng không hợp lệ'
      });
    }

    // Parallel queries for better performance
    const [
      totalPosts,
      totalDonations,
      totalEvents,
      donationStats,
      postStats
    ] = await Promise.all([
      // Count total posts
      Post.countDocuments({ user: userId }),
      
      // Count total donations
      Donation.countDocuments({ userId, status: 'success' }),
      
      // Count total events registered
      EventRegistration.countDocuments({ userId, status: 'registered' }),
      
      // Get donation amount stats
      Donation.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(userId),
            status: 'success'
          }
        },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$amount' }
          }
        }
      ]),
      
      // Get post engagement stats
      Post.aggregate([
        {
          $match: {
            user: new mongoose.Types.ObjectId(userId)
          }
        },
        {
          $group: {
            _id: null,
            totalReactions: { $sum: { $size: '$reactions' } },
            totalComments: { $sum: { $size: '$comments' } }
          }
        }
      ])
    ]);

    const totalDonationAmount = donationStats[0]?.totalAmount || 0;
    const totalReactions = postStats[0]?.totalReactions || 0;
    const totalComments = postStats[0]?.totalComments || 0;

    const stats = {
      totalPosts,
      totalReactions,
      totalComments,
      totalDonations,
      totalEvents,
      totalDonationAmount
    };

    console.log(`📊 User stats for ${userId}:`, stats);

    res.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    console.error('Error getting user stats:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy thống kê người dùng',
      error: error.message
    });
  }
};
