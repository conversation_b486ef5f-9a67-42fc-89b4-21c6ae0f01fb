import React, { useState, useRef } from 'react';
import { X, Heart, MessageCircle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';
import CommentReactionsModal from './CommentReactionsModal';
import UserAvatar from '../common/UserAvatar';

interface Comment {
  _id: string;
  user: {
    _id: string;
    name: string;
    avatar?: string;
  };
  content: string;
  createdAt: string;
  reactions?: {
    user: {
      _id: string;
      name: string;
      avatar?: string;
    };
    type: string;
  }[];
  replies?: Comment[];
  userReaction?: string | null;
}

interface CommentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  comments: Comment[];
  postId: string;
  onReactToComment?: (commentId: string, reactionType: string) => void;
  onReplyToComment?: (commentId: string, content: string) => void;
  onCreateComment?: (postId: string, content: string) => void;
  onDeleteComment?: (commentId: string) => void;
  onReportComment?: (commentId: string, reason: string) => void;
  currentUserId?: string;
}

const CommentsModal: React.FC<CommentsModalProps> = ({
  isOpen,
  onClose,
  comments,
  postId,
  onReactToComment,
  onReplyToComment,
  onCreateComment,
  onDeleteComment,
  onReportComment,
  currentUserId
}) => {
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [showReactions, setShowReactions] = useState<string | null>(null);
  const [isHovering, setIsHovering] = useState(false);
  const [newCommentContent, setNewCommentContent] = useState('');
  const [isCreatingComment, setIsCreatingComment] = useState(false);
  const [isCreatingReply, setIsCreatingReply] = useState<string | null>(null);
  const [showCommentReactionsModal, setShowCommentReactionsModal] = useState(false);
  const [selectedCommentReactions, setSelectedCommentReactions] = useState<any[]>([]);
  const [selectedCommentId, setSelectedCommentId] = useState<string>('');
  const hoverTimeoutRef = useRef<NodeJS.Timeout>();
  const reactionTimeoutRef = useRef<NodeJS.Timeout>();

  // Reaction types
  const reactions = [
    { type: 'like', icon: '👍', color: 'text-blue-500', label: 'Thích' },
    { type: 'love', icon: '❤️', color: 'text-red-500', label: 'Yêu thích' },
    { type: 'haha', icon: '😂', color: 'text-yellow-500', label: 'Haha' },
    { type: 'wow', icon: '😮', color: 'text-orange-500', label: 'Wow' },
    { type: 'sad', icon: '😢', color: 'text-blue-400', label: 'Buồn' },
    { type: 'angry', icon: '😡', color: 'text-red-500', label: 'Phẫn nộ' }
  ];



  if (!isOpen) return null;

  const handleReply = async (commentId: string) => {
    if (replyContent.trim() && onReplyToComment) {
      setIsCreatingReply(commentId);
      try {
        await onReplyToComment(commentId, replyContent.trim());
        setReplyContent('');
        setReplyingTo(null);
      } finally {
        setIsCreatingReply(null);
      }
    }
  };

  const handleReactToComment = (commentId: string, reactionType: string) => {
    if (onReactToComment) {
      onReactToComment(commentId, reactionType);
    }
    setShowReactions(null);
  };

  const handleLikeMouseEnter = (commentId: string) => {
    setIsHovering(true);
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    if (reactionTimeoutRef.current) {
      clearTimeout(reactionTimeoutRef.current);
    }

    hoverTimeoutRef.current = setTimeout(() => {
      setShowReactions(commentId);
    }, 600);
  };

  const handleLikeMouseLeave = () => {
    setIsHovering(false);
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    reactionTimeoutRef.current = setTimeout(() => {
      setShowReactions(null);
    }, 200);
  };

  const handleReactionsMouseEnter = () => {
    setIsHovering(true);
    if (reactionTimeoutRef.current) {
      clearTimeout(reactionTimeoutRef.current);
    }
  };

  const handleReactionsMouseLeave = () => {
    setIsHovering(false);
    reactionTimeoutRef.current = setTimeout(() => {
      setShowReactions(null);
    }, 150);
  };

  const handleCreateComment = async () => {
    if (newCommentContent.trim() && onCreateComment) {
      setIsCreatingComment(true);
      try {
        await onCreateComment(postId, newCommentContent.trim());
        setNewCommentContent('');
      } finally {
        setIsCreatingComment(false);
      }
    }
  };

  // Calculate total comments including replies
  const getTotalCommentsCount = () => {
    let total = comments.length; // Top-level comments
    comments.forEach(comment => {
      if (comment.replies) {
        total += comment.replies.length; // Add replies
      }
    });
    return total;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Bình luận ({getTotalCommentsCount()})
          </h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {comments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              Chưa có bình luận nào
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment, index) => {

                // Handle different user data structures
                const userData = comment.user || {};
                const userName = userData.name || userData.username || `User ${userData._id || index + 1}`;
                const userAvatar = userData.avatar;
                const userId = userData._id || userData.id || index;

                return (
                  <div key={comment._id || index} className="flex space-x-3">
                    {/* Avatar */}
                    <UserAvatar
                      user={{ name: userName, avatar: userAvatar, _id: userId }}
                      size="sm"
                    />

                    {/* Comment Content */}
                    <div className="flex-1 min-w-0">
                      {/* Comment Bubble */}
                      <div className="bg-gray-100 rounded-2xl px-3 py-2">
                        <p className="text-sm font-medium text-gray-900 mb-1">
                          {userName}
                        </p>
                        <p className="text-sm text-gray-800">
                          {comment.content}
                        </p>
                      </div>

                      {/* Comment Actions */}
                      <div className="flex items-center space-x-4 mt-1 ml-3">
                        <span className="text-xs text-gray-500">
                          {formatDistanceToNow(new Date(comment.createdAt), {
                            addSuffix: true,
                            locale: vi
                          })}
                        </span>

                        <div className="relative">
                          <button
                            onClick={() => {
                              const currentReaction = comment.userReaction;
                              const newReaction = currentReaction === 'like' ? '' : 'like';
                              handleReactToComment(comment._id, newReaction);
                            }}
                            onMouseEnter={() => handleLikeMouseEnter(comment._id)}
                            onMouseLeave={handleLikeMouseLeave}
                            className={`text-xs font-medium transition-colors ${
                              comment.userReaction
                                ? reactions.find(r => r.type === comment.userReaction)?.color || 'text-blue-600'
                                : 'text-gray-500 hover:text-blue-600'
                            }`}
                          >
                            {comment.userReaction ? (
                              <>
                                {reactions.find(r => r.type === comment.userReaction)?.icon} {reactions.find(r => r.type === comment.userReaction)?.label}
                              </>
                            ) : (
                              'Thích'
                            )}
                          </button>

                          {/* Reactions Popup */}
                          {showReactions === comment._id && (
                            <div
                              onMouseEnter={handleReactionsMouseEnter}
                              onMouseLeave={handleReactionsMouseLeave}
                              className="absolute bottom-full left-0 mb-2 bg-white rounded-full px-3 py-2 flex items-center space-x-1 shadow-lg border z-50"
                            >
                              {reactions.map((reaction) => (
                                <button
                                  key={reaction.type}
                                  onClick={() => {
                                    const currentReaction = comment.userReaction;
                                    const newReaction = currentReaction === reaction.type ? '' : reaction.type;
                                    handleReactToComment(comment._id, newReaction);
                                  }}
                                  className="hover:scale-125 transition-transform duration-200 text-lg"
                                  title={reaction.label}
                                >
                                  {reaction.icon}
                                </button>
                              ))}
                            </div>
                          )}
                        </div>

                        <button
                          onClick={() => setReplyingTo(comment._id)}
                          className="text-xs text-gray-500 hover:text-blue-600 font-medium"
                        >
                          Phản hồi
                        </button>

                        {/* Report button - only show for other users' comments */}
                        {currentUserId !== userId && onReportComment && (
                          <button
                            onClick={() => {
                              const reason = prompt('Lý do báo cáo comment này:');
                              if (reason && reason.trim()) {
                                onReportComment(comment._id, reason.trim());
                              }
                            }}
                            className="text-xs text-gray-500 hover:text-red-600 font-medium"
                          >
                            Báo cáo
                          </button>
                        )}

                        {/* Reactions count */}
                        {comment.reactions && comment.reactions.length > 0 && (
                          <div
                            className="flex items-center space-x-1 cursor-pointer hover:bg-gray-100 rounded px-1"
                            onClick={() => {
                              setSelectedCommentReactions(comment.reactions || []);
                              setSelectedCommentId(comment._id);
                              setShowCommentReactionsModal(true);
                            }}
                          >
                            {(() => {
                              // Get unique reaction types and their counts
                              const reactionCounts: { [key: string]: number } = {};
                              comment.reactions.forEach((reaction: any) => {
                                reactionCounts[reaction.type] = (reactionCounts[reaction.type] || 0) + 1;
                              });

                              const sortedReactions = Object.entries(reactionCounts)
                                .sort(([,a], [,b]) => b - a)
                                .slice(0, 3);

                              return (
                                <>
                                  <div className="flex -space-x-1">
                                    {sortedReactions.map(([type], index) => {
                                      const reactionData = reactions.find(r => r.type === type);
                                      return (
                                        <span
                                          key={index}
                                          className="text-xs bg-white rounded-full border border-gray-200 w-4 h-4 flex items-center justify-center hover:scale-110 transition-transform duration-200"
                                          title={`${reactionCounts[type]} ${reactionData?.label}`}
                                        >
                                          {reactionData?.icon || '👍'}
                                        </span>
                                      );
                                    })}
                                  </div>
                                  <span className="text-xs text-gray-500 hover:underline">
                                    {comment.reactions.length}
                                  </span>
                                </>
                              );
                            })()}
                          </div>
                        )}
                      </div>

                      {/* Reply Form */}
                      {replyingTo === comment._id && (
                        <div className="mt-2 ml-3">
                          <div className="flex space-x-2">
                            <input
                              type="text"
                              value={replyContent}
                              onChange={(e) => setReplyContent(e.target.value)}
                              placeholder="Viết phản hồi..."
                              className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  handleReply(comment._id);
                                }
                              }}
                            />
                            <button
                              onClick={() => handleReply(comment._id)}
                              disabled={isCreatingReply === comment._id}
                              className="px-3 py-1 text-sm bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                            >
                              {isCreatingReply === comment._id ? (
                                <>
                                  <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                                  <span>Đang gửi...</span>
                                </>
                              ) : (
                                <span>Gửi</span>
                              )}
                            </button>
                            <button
                              onClick={() => {
                                setReplyingTo(null);
                                setReplyContent('');
                              }}
                              className="px-3 py-1 text-sm text-gray-500 hover:text-gray-700"
                            >
                              Hủy
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Replies */}
                      {(() => {
                        const replies = comment.replies || [];
                        const hasReplies = Array.isArray(replies) && replies.length > 0;

                        if (!hasReplies) {
                          return null;
                        }

                        return (
                          <div className="mt-3 ml-8 space-y-3">
                            <div className="text-xs text-gray-500 mb-2 font-medium">
                              {replies.length} phản hồi
                            </div>
                            {replies.map((reply, replyIndex) => {
                            const replyUserData = reply.user || {};
                            const replyUserName = replyUserData.name || replyUserData.username || `User ${replyUserData._id || replyIndex + 1}`;
                            const replyUserAvatar = replyUserData.avatar;
                            const replyUserId = replyUserData._id || replyUserData.id || replyIndex;

                            return (
                              <div key={reply._id || replyIndex} className="flex space-x-2">
                                {/* Reply Avatar */}
                                <UserAvatar
                                  user={{ name: replyUserName, avatar: replyUserAvatar, _id: replyUserId }}
                                  size="xs"
                                />

                                {/* Reply Content */}
                                <div className="flex-1 min-w-0">
                                  <div className="bg-gray-100 rounded-2xl px-3 py-2">
                                    <p className="text-xs font-medium text-gray-900 mb-1">
                                      {replyUserName}
                                    </p>
                                    <p className="text-xs text-gray-800">
                                      {reply.content}
                                    </p>
                                  </div>

                                  {/* Reply Actions */}
                                  <div className="flex items-center space-x-3 mt-1 ml-3">
                                    <span className="text-xs text-gray-500">
                                      {formatDistanceToNow(new Date(reply.createdAt), {
                                        addSuffix: true,
                                        locale: vi
                                      })}
                                    </span>

                                    <div className="relative">
                                      <button
                                        onClick={() => {
                                          const currentReaction = reply.userReaction;
                                          const newReaction = currentReaction === 'like' ? '' : 'like';
                                          handleReactToComment(reply._id, newReaction);
                                        }}
                                        onMouseEnter={() => handleLikeMouseEnter(reply._id)}
                                        onMouseLeave={handleLikeMouseLeave}
                                        className={`text-xs font-medium transition-colors ${
                                          reply.userReaction
                                            ? reactions.find(r => r.type === reply.userReaction)?.color || 'text-blue-600'
                                            : 'text-gray-500 hover:text-blue-600'
                                        }`}
                                      >
                                        {reply.userReaction ? (
                                          <>
                                            {reactions.find(r => r.type === reply.userReaction)?.icon} {reactions.find(r => r.type === reply.userReaction)?.label}
                                          </>
                                        ) : (
                                          'Thích'
                                        )}
                                      </button>

                                      {/* Reply Reactions Popup */}
                                      {showReactions === reply._id && (
                                        <div
                                          onMouseEnter={handleReactionsMouseEnter}
                                          onMouseLeave={handleReactionsMouseLeave}
                                          className="absolute bottom-full left-0 mb-2 bg-white rounded-full px-3 py-2 flex items-center space-x-1 shadow-lg border z-50"
                                        >
                                          {reactions.map((reaction) => (
                                            <button
                                              key={reaction.type}
                                              onClick={() => {
                                                const currentReaction = reply.userReaction;
                                                const newReaction = currentReaction === reaction.type ? '' : reaction.type;
                                                handleReactToComment(reply._id, newReaction);
                                              }}
                                              className="hover:scale-125 transition-transform duration-200 text-lg"
                                              title={reaction.label}
                                            >
                                              {reaction.icon}
                                            </button>
                                          ))}
                                        </div>
                                      )}
                                    </div>

                                    {/* Report button for replies - only show for other users' replies */}
                                    {currentUserId !== replyUserId && onReportComment && (
                                      <button
                                        onClick={() => {
                                          const reason = prompt('Lý do báo cáo phản hồi này:');
                                          if (reason && reason.trim()) {
                                            onReportComment(reply._id, reason.trim());
                                          }
                                        }}
                                        className="text-xs text-gray-500 hover:text-red-600 font-medium"
                                      >
                                        Báo cáo
                                      </button>
                                    )}

                                    {/* Reply Reactions count */}
                                    {reply.reactions && reply.reactions.length > 0 && (
                                      <div
                                        className="flex items-center space-x-1 cursor-pointer hover:bg-gray-100 rounded px-1"
                                        onClick={() => {
                                          setSelectedCommentReactions(reply.reactions || []);
                                          setSelectedCommentId(reply._id);
                                          setShowCommentReactionsModal(true);
                                        }}
                                      >
                                        {(() => {
                                          // Get unique reaction types and their counts
                                          const reactionCounts: { [key: string]: number } = {};
                                          reply.reactions.forEach((reaction: any) => {
                                            reactionCounts[reaction.type] = (reactionCounts[reaction.type] || 0) + 1;
                                          });

                                          const sortedReactions = Object.entries(reactionCounts)
                                            .sort(([,a], [,b]) => b - a)
                                            .slice(0, 3);

                                          return (
                                            <>
                                              <div className="flex -space-x-1">
                                                {sortedReactions.map(([type], index) => {
                                                  const reactionData = reactions.find(r => r.type === type);
                                                  return (
                                                    <span
                                                      key={index}
                                                      className="text-xs bg-white rounded-full border border-gray-200 w-4 h-4 flex items-center justify-center hover:scale-110 transition-transform duration-200"
                                                      title={`${reactionCounts[type]} ${reactionData?.label}`}
                                                    >
                                                      {reactionData?.icon || '👍'}
                                                    </span>
                                                  );
                                                })}
                                              </div>
                                              <span className="text-xs text-gray-500 hover:underline">
                                                {reply.reactions.length}
                                              </span>
                                            </>
                                          );
                                        })()}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                        );
                      })()}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Comment Form */}
        <div className="border-t p-4">
          <div className="flex space-x-3">
            {/* User Avatar */}
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden flex-shrink-0">
              {/* Note: We would need current user data here to show their avatar */}
              <span className="text-xs font-medium text-gray-600">
                U
              </span>
            </div>

            {/* Comment Input */}
            <div className="flex-1 flex space-x-2">
              <input
                type="text"
                value={newCommentContent}
                onChange={(e) => setNewCommentContent(e.target.value)}
                placeholder="Viết bình luận..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleCreateComment();
                  }
                }}
              />
              <button
                onClick={handleCreateComment}
                disabled={!newCommentContent.trim() || isCreatingComment}
                className="px-4 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                {isCreatingComment ? (
                  <>
                    <div className="w-4 h-4 border border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Đang gửi...</span>
                  </>
                ) : (
                  <span>Gửi</span>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Comment Reactions Modal */}
        <CommentReactionsModal
          isOpen={showCommentReactionsModal}
          onClose={() => setShowCommentReactionsModal(false)}
          reactions={selectedCommentReactions}
          commentId={selectedCommentId}
        />
      </div>
    </div>
  );
};

export default CommentsModal;
