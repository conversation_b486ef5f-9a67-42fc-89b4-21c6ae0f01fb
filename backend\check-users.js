const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb+srv://trankhanhduy1409:<EMAIL>/keydyweb?retryWrites=true&w=majority&appName=Cluster0')
.then(async () => {
  console.log('Connected to MongoDB');
  
  const User = mongoose.model('User', new mongoose.Schema({
    name: String,
    email: String,
    role: String,
    createdAt: Date
  }));
  
  const users = await User.find({}, 'name email role createdAt').lean();
  console.log('=== ALL USERS ===');
  users.forEach(user => {
    console.log(`ID: ${user._id}`);
    console.log(`Name: ${user.name}`);
    console.log(`Email: ${user.email}`);
    console.log(`Role: ${user.role}`);
    console.log(`Created: ${user.createdAt}`);
    console.log('---');
  });
  
  // Find users with similar names
  const nameGroups = {};
  users.forEach(user => {
    const name = user.name.toLowerCase().trim();
    if (!nameGroups[name]) nameGroups[name] = [];
    nameGroups[name].push(user);
  });
  
  console.log('\n=== DUPLICATE NAMES ===');
  Object.keys(nameGroups).forEach(name => {
    if (nameGroups[name].length > 1) {
      console.log(`Name: ${name}`);
      nameGroups[name].forEach(user => {
        console.log(`  - ${user.email} (${user.role}) - ${user.createdAt}`);
      });
      console.log('---');
    }
  });
  
  // Find users with "khanh duy" in name
  console.log('\n=== KHANH DUY USERS ===');
  const khanhDuyUsers = users.filter(user => 
    user.name.toLowerCase().includes('khanh') && 
    user.name.toLowerCase().includes('duy')
  );
  khanhDuyUsers.forEach(user => {
    console.log(`${user.name}: ${user.email} (${user.role})`);
  });
  
  mongoose.disconnect();
})
.catch(console.error);
