"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTestNotification = exports.deleteNotification = exports.getAllNotifications = exports.getNotificationStats = exports.createAdminNotification = exports.getUnreadCount = exports.markAllAsRead = exports.markAsRead = exports.getUserNotifications = exports.createNotification = void 0;
const Notification_1 = require("../models/Notification");
const user_model_1 = require("../models/user.model");
const index_1 = require("../index");
const mongoose_1 = __importDefault(require("mongoose"));
const notification_service_1 = require("../services/notification.service");
// Create a new notification (legacy function - use service instead)
const createNotification = (data) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const notification = new Notification_1.Notification(data);
        yield notification.save();
        // Emit socket event for real-time notification
        index_1.io.to(`user-${data.userId}`).emit('new_notification', notification);
        return notification;
    }
    catch (error) {
        console.error('Error creating notification:', error);
        throw error;
    }
});
exports.createNotification = createNotification;
// Get user's notifications
const getUserNotifications = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const [notifications, total] = yield Promise.all([
            Notification_1.Notification.find({ userId })
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            Notification_1.Notification.countDocuments({ userId })
        ]);
        return res.json({
            success: true,
            data: notifications,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error('Error getting user notifications:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getUserNotifications = getUserNotifications;
// Mark notification as read
const markAsRead = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }
        const { notificationId } = req.params;
        const notification = yield Notification_1.Notification.findOneAndUpdate({ _id: notificationId, userId }, { isRead: true }, { new: true });
        if (!notification) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }
        return res.json({
            success: true,
            data: notification
        });
    }
    catch (error) {
        console.error('Error marking notification as read:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.markAsRead = markAsRead;
// Mark all notifications as read
const markAllAsRead = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }
        yield Notification_1.Notification.updateMany({ userId, isRead: false }, { isRead: true });
        return res.json({
            success: true,
            message: 'All notifications marked as read'
        });
    }
    catch (error) {
        console.error('Error marking all notifications as read:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.markAllAsRead = markAllAsRead;
// Get unread notifications count
const getUnreadCount = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }
        const count = yield Notification_1.Notification.countDocuments({
            userId,
            isRead: false
        });
        return res.json({
            success: true,
            count
        });
    }
    catch (error) {
        console.error('Error getting unread notifications count:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getUnreadCount = getUnreadCount;
// Admin: Create notification for users
const createAdminNotification = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin only.'
            });
        }
        const { title, message, priority = 'medium', userIds, sendToAll } = req.body;
        if (!title || !message) {
            return res.status(400).json({
                success: false,
                message: 'Title and message are required'
            });
        }
        let targetUserIds = [];
        if (sendToAll) {
            // Send to all users
            const allUsers = yield user_model_1.User.find({ role: 'user' }).select('_id');
            targetUserIds = allUsers.map(u => u._id);
        }
        else if (userIds && Array.isArray(userIds)) {
            // Send to specific users
            targetUserIds = userIds.map((id) => new mongoose_1.default.Types.ObjectId(id));
        }
        else {
            return res.status(400).json({
                success: false,
                message: 'Either userIds array or sendToAll flag is required'
            });
        }
        if (targetUserIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No users found to send notification'
            });
        }
        // Create notifications for each user
        const notifications = targetUserIds.map(userId => ({
            userId,
            type: 'admin',
            title,
            message,
            priority,
            isRead: false,
            emailSent: false,
            data: {
                actionType: 'admin_message',
                createdBy: user._id
            }
        }));
        yield Notification_1.Notification.insertMany(notifications);
        return res.json({
            success: true,
            message: `Notification sent to ${targetUserIds.length} users`,
            count: targetUserIds.length
        });
    }
    catch (error) {
        console.error('Error creating admin notification:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.createAdminNotification = createAdminNotification;
// Admin: Get notification statistics
const getNotificationStats = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin only.'
            });
        }
        const [totalNotifications, unreadNotifications, notificationsByType] = yield Promise.all([
            Notification_1.Notification.countDocuments(),
            Notification_1.Notification.countDocuments({ isRead: false }),
            Notification_1.Notification.aggregate([
                {
                    $group: {
                        _id: '$type',
                        count: { $sum: 1 }
                    }
                }
            ])
        ]);
        return res.json({
            success: true,
            data: {
                total: totalNotifications,
                unread: unreadNotifications,
                byType: notificationsByType
            }
        });
    }
    catch (error) {
        console.error('Error getting notification stats:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getNotificationStats = getNotificationStats;
// Get all notifications for admin
const getAllNotifications = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin role required.'
            });
        }
        const notifications = yield Notification_1.Notification.find()
            .populate('userId', 'name email')
            .sort({ createdAt: -1 })
            .limit(500); // Limit to prevent performance issues
        const formattedNotifications = notifications.map(notification => (Object.assign(Object.assign({}, notification.toObject()), { user: notification.userId ? {
                name: notification.userId.name,
                email: notification.userId.email
            } : null })));
        return res.json({
            success: true,
            data: formattedNotifications
        });
    }
    catch (error) {
        console.error('Error fetching all notifications:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Failed to fetch notifications'
        });
    }
});
exports.getAllNotifications = getAllNotifications;
// Delete notification (admin only)
const deleteNotification = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin role required.'
            });
        }
        const { notificationId } = req.params;
        const notification = yield Notification_1.Notification.findByIdAndDelete(notificationId);
        if (!notification) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }
        return res.json({
            success: true,
            message: 'Notification deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting notification:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Failed to delete notification'
        });
    }
});
exports.deleteNotification = deleteNotification;
// Test notification endpoint (for development)
const createTestNotification = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }
        const { type = 'system', title = 'Test Notification', message = 'This is a test notification', sendEmail = false } = req.body;
        console.log('🧪 Creating test notification for user:', user._id);
        const notification = yield (0, notification_service_1.createNotification)({
            userId: user._id,
            type,
            title,
            message,
            data: {
                actionType: 'created'
            },
            priority: 'medium',
            sendEmail
        });
        console.log('✅ Test notification created:', notification._id);
        return res.json({
            success: true,
            message: 'Test notification created successfully',
            notification: {
                id: notification._id,
                title: notification.title,
                message: notification.message,
                type: notification.type,
                createdAt: notification.createdAt
            }
        });
    }
    catch (error) {
        console.error('Error creating test notification:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.createTestNotification = createTestNotification;
