/**
 * Avatar utility functions
 */

/**
 * Get user initials from name
 */
export const getUserInitials = (name?: string): string => {
  if (!name) return 'U';
  
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2); // Limit to 2 characters
};

/**
 * Get avatar URL with fallback
 */
export const getAvatarUrl = (avatar?: string): string | null => {
  if (!avatar) return null;

  // If it's already a full URL, return as is
  if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
    return avatar;
  }

  // If it's base64 data, return as is
  if (avatar.startsWith('data:')) {
    // Log warning if base64 is too large (for debugging)
    if (avatar.length > 100000) { // ~100KB
      console.warn('⚠️ Avatar base64 is very large:', avatar.length, 'characters');
    }
    return avatar;
  }

  // If it's a relative path, prepend the API URL
  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';
  return `${API_URL}/${avatar}`;
};

/**
 * Generate a consistent background color based on user name
 */
export const getAvatarBackgroundColor = (name?: string): string => {
  if (!name) return '#6B7280'; // gray-500
  
  const colors = [
    '#EF4444', // red-500
    '#F97316', // orange-500
    '#F59E0B', // amber-500
    '#EAB308', // yellow-500
    '#84CC16', // lime-500
    '#22C55E', // green-500
    '#10B981', // emerald-500
    '#14B8A6', // teal-500
    '#06B6D4', // cyan-500
    '#0EA5E9', // sky-500
    '#3B82F6', // blue-500
    '#6366F1', // indigo-500
    '#8B5CF6', // violet-500
    '#A855F7', // purple-500
    '#D946EF', // fuchsia-500
    '#EC4899', // pink-500
    '#F43F5E', // rose-500
  ];
  
  // Generate a hash from the name
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Use the hash to pick a color
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

/**
 * Avatar component props interface
 */
export interface AvatarData {
  name?: string;
  avatar?: string;
  _id?: string;
}

/**
 * Get avatar display data
 */
export const getAvatarDisplayData = (user?: AvatarData) => {
  const name = user?.name || 'User';
  const avatarUrl = getAvatarUrl(user?.avatar);
  const initials = getUserInitials(name);
  const backgroundColor = getAvatarBackgroundColor(name);
  
  return {
    name,
    avatarUrl,
    initials,
    backgroundColor,
    hasAvatar: !!avatarUrl
  };
};
