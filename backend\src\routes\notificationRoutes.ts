import express from 'express';
import {
  getUserNotifications,
  markAs<PERSON><PERSON>,
  markAll<PERSON>Read,
  getUnreadCount,
  createAdminNotification,
  getNotificationStats,
  getAllNotifications,
  createTestNotification
} from '../controllers/notificationController';
import { protect } from '../middleware/authMiddleware';

const router = express.Router();

// Get unread notifications count (no auth required for polling)
router.get('/unread-count', protect, getUnreadCount);

// Apply protect middleware to routes that need authentication
// Get user's notifications
router.get('/', protect, getUserNotifications);

// Mark notification as read
router.patch('/:notificationId/read', protect, markAsRead);

// Mark all notifications as read
router.patch('/read-all', protect, markAllAsRead);

// Admin routes
router.post('/admin/create', protect, createAdminNotification);
router.get('/admin/stats', protect, getNotificationStats);
router.get('/admin/all', protect, getAllNotifications);

// Test route (for development)
router.post('/test', protect, createTestNotification);

export default router;