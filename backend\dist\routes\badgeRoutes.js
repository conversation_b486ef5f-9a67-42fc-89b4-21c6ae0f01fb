"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const badgeController_1 = require("../controllers/badgeController");
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = express_1.default.Router();
// Public routes
router.get('/config', badgeController_1.getBadgeConfig);
router.get('/test', badgeController_1.testBadgeSystem);
router.get('/user/:userId', badgeController_1.getPublicUserBadges);
router.get('/user/:userId/primary', badgeController_1.getPublicUserPrimaryBadge);
// Protected routes
router.get('/my-badges', authMiddleware_1.protect, badgeController_1.getUserBadgesController);
router.get('/my-primary', authMiddleware_1.protect, badgeController_1.getUserPrimaryBadgeController);
router.post('/check', authMiddleware_1.protect, badgeController_1.checkUserBadges);
// Admin routes
router.get('/admin/stats', authMiddleware_1.protect, badgeController_1.getBadgeStats);
router.get('/admin/users', authMiddleware_1.protect, badgeController_1.getUsersWithBadge);
exports.default = router;
