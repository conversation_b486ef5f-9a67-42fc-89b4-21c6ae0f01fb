"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startCampaignReminderJob = exports.campaignDeadlineReminder = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const Campaign_1 = require("../models/Campaign");
const user_model_1 = require("../models/user.model");
const notification_service_1 = require("../services/notification.service");
// Run every day at 9:00 AM
exports.campaignDeadlineReminder = node_cron_1.default.createTask('0 9 * * *', () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🔔 [Cron] Running campaign deadline reminder job...');
        const now = new Date();
        const threeDaysFromNow = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000));
        const sevenDaysFromNow = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
        // Find campaigns ending in 3 days
        const campaignsEndingIn3Days = yield Campaign_1.Campaign.find({
            status: 'active',
            endDate: {
                $gte: now,
                $lte: threeDaysFromNow
            }
        });
        // Find campaigns ending in 7 days
        const campaignsEndingIn7Days = yield Campaign_1.Campaign.find({
            status: 'active',
            endDate: {
                $gte: threeDaysFromNow,
                $lte: sevenDaysFromNow
            }
        });
        // Get all active users
        const allUsers = yield user_model_1.User.find({
            role: 'user',
            'preferences.emailNotifications': true
        }).select('_id');
        const userIds = allUsers.map(user => user._id);
        // Send 3-day reminders
        for (const campaign of campaignsEndingIn3Days) {
            const daysLeft = Math.ceil((campaign.endDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000));
            if (userIds.length > 0) {
                yield (0, notification_service_1.notifyCampaignDeadline)(userIds, campaign.title, campaign._id, daysLeft);
                console.log(`📧 [Cron] 3-day reminder sent for campaign: ${campaign.title} (${daysLeft} days left)`);
            }
        }
        // Send 7-day reminders
        for (const campaign of campaignsEndingIn7Days) {
            const daysLeft = Math.ceil((campaign.endDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000));
            if (userIds.length > 0) {
                yield (0, notification_service_1.notifyCampaignDeadline)(userIds, campaign.title, campaign._id, daysLeft);
                console.log(`📧 [Cron] 7-day reminder sent for campaign: ${campaign.title} (${daysLeft} days left)`);
            }
        }
        console.log(`✅ [Cron] Campaign deadline reminders completed. Processed ${campaignsEndingIn3Days.length + campaignsEndingIn7Days.length} campaigns`);
    }
    catch (error) {
        console.error('❌ [Cron] Error in campaign deadline reminder job:', error);
    }
}));
// Start the cron job
const startCampaignReminderJob = () => {
    exports.campaignDeadlineReminder.start();
    console.log('⏰ [Cron] Campaign deadline reminder job started (daily at 9:00 AM)');
};
exports.startCampaignReminderJob = startCampaignReminderJob;
