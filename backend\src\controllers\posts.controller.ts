import { Request, Response } from 'express';
import { Post, IPost } from '../models/Post';
import { Comment } from '../models/Comment';
import { Report } from '../models/Report';
import mongoose from 'mongoose';

interface AuthRequest extends Request {
  user?: {
    _id: string;
    name: string;
    email: string;
  };
}

// Rate limiting map to track requests per IP
const requestCounts = new Map<string, { count: number; lastReset: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_REQUESTS_PER_WINDOW = 100; // Max 100 requests per minute (increased for development)

// GET /posts - <PERSON><PERSON>y danh sách bài viết công khai (newsfeed)
export const getPosts = async (req: Request, res: Response) => {
  try {
    // Rate limiting check
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();

    if (!requestCounts.has(clientIP)) {
      requestCounts.set(clientIP, { count: 1, lastReset: now });
    } else {
      const clientData = requestCounts.get(clientIP)!;

      // Reset count if window has passed
      if (now - clientData.lastReset > RATE_LIMIT_WINDOW) {
        clientData.count = 1;
        clientData.lastReset = now;
      } else {
        clientData.count++;

        // Block if exceeded rate limit
        if (clientData.count > MAX_REQUESTS_PER_WINDOW) {
          console.log(`🚫 Rate limit exceeded for IP: ${clientIP} (${clientData.count} requests)`);
          return res.status(429).json({
            success: false,
            message: 'Too many requests. Please try again later.',
            retryAfter: Math.ceil((RATE_LIMIT_WINDOW - (now - clientData.lastReset)) / 1000)
          });
        }
      }
    }

    console.log(`📊 Request from ${clientIP}: ${requestCounts.get(clientIP)?.count}/${MAX_REQUESTS_PER_WINDOW}`);

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Query filters
    const search = req.query.search as string;
    const author = req.query.author as string;
    const hasMedia = req.query.hasMedia === 'true';

    // Build query
    const query: any = { visibility: 'public' };

    if (search) {
      query.content = { $regex: search, $options: 'i' };
    }

    if (author) {
      query.user = author;
    }

    if (hasMedia) {
      query.media = { $exists: true, $not: { $size: 0 } };
    }

    const posts = await Post.find(query)
      .populate('user', 'name avatar email')
      .populate('reactions.user', 'name avatar')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Manually populate ALL comments for each post (including replies)
    for (const post of posts) {
      const allComments = await Comment.find({ post: post._id })
        .populate('user', 'name avatar')
        .populate('reactions.user', 'name avatar')
        .lean();

      (post as any).comments = allComments;
      console.log(`🔍 Post ${post._id}: Loaded ${allComments.length} total comments from DB`);
    }

    // Process comments and replies properly
    const userId = req.user?._id;
    const postsWithProcessedComments = posts.map((post: any) => {
      // Find current user's reaction for post
      const userReaction = post.reactions?.find(
        (reaction: any) => reaction.user._id?.toString() === userId?.toString()
      );

      // Separate top-level comments and replies
      const allComments = post.comments || [];
      console.log(`🔍 Post ${post._id} - Total comments from DB:`, allComments.length);

      const topLevelComments = allComments.filter((comment: any) => {
        const isTopLevel = !comment.parentComment;
        console.log(`  📝 Comment ${comment._id}: parentComment=${comment.parentComment}, isTopLevel=${isTopLevel}`);
        return isTopLevel;
      });

      const replies = allComments.filter((comment: any) => {
        const isReply = !!comment.parentComment;
        if (isReply) {
          console.log(`  🔄 Reply ${comment._id}: parentComment=${comment.parentComment}`);
        }
        return isReply;
      });

      console.log(`📊 Post ${post._id}: ${topLevelComments.length} top-level, ${replies.length} replies`);

      // Group replies by parent comment
      const repliesByParent = replies.reduce((acc: any, reply: any) => {
        const parentId = reply.parentComment.toString();
        console.log(`🔗 Grouping reply ${reply._id} under parent ${parentId}`);
        if (!acc[parentId]) acc[parentId] = [];
        acc[parentId].push(reply);
        return acc;
      }, {});

      console.log(`📋 Replies grouped:`, Object.keys(repliesByParent).map(parentId => ({
        parentId,
        repliesCount: repliesByParent[parentId].length
      })));

      // Add replies to their parent comments and userReaction
      const processedComments = topLevelComments.map((comment: any) => {
        const commentUserReaction = comment.reactions?.find(
          (reaction: any) => reaction.user._id?.toString() === userId?.toString()
        );

        const commentId = comment._id.toString();
        const commentReplies = repliesByParent[commentId] || [];
        console.log(`🔗 Comment ${commentId} looking for replies in:`, Object.keys(repliesByParent));
        console.log(`🔗 Comment ${commentId} found ${commentReplies.length} replies`);

        const processedReplies = commentReplies.map((reply: any) => {
          console.log(`  ✅ Processing reply ${reply._id}: "${reply.content?.substring(0, 30)}..."`);
          const replyUserReaction = reply.reactions?.find(
            (reaction: any) => reaction.user._id?.toString() === userId?.toString()
          );

          return {
            ...reply,
            userReaction: replyUserReaction?.type || null
          };
        });

        const result = {
          ...comment,
          userReaction: commentUserReaction?.type || null,
          replies: processedReplies
        };

        console.log(`📝 Final comment ${commentId} has ${result.replies.length} replies attached`);
        return result;
      });

      return {
        ...post,
        userReaction: userReaction?.type || null,
        comments: processedComments
      };
    });

    const total = await Post.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    console.log(`📊 Posts endpoint: Found ${posts.length} posts (total: ${total}) for query:`, query);
    console.log(`📊 Posts from users:`, posts.map(p => ({ id: (p as any)._id, author: (p as any).user?.name, content: (p as any).content?.substring(0, 30) })));

    // Debug: Check all comments in database for the first post with comments
    const postWithComments = posts.find((p: any) => p.comments && p.comments.length > 0);
    if (postWithComments) {
      console.log(`🔍 Checking all comments for post ${postWithComments._id}:`);
      const allCommentsForPost = await Comment.find({ post: postWithComments._id })
        .populate('user', 'name')
        .lean();

      console.log(`📋 Found ${allCommentsForPost.length} total comments in DB:`);
      allCommentsForPost.forEach((comment: any, index: number) => {
        console.log(`  ${index + 1}. ${comment._id}: "${comment.content?.substring(0, 30)}..." by ${comment.user?.name}`);
        console.log(`     parentComment: ${comment.parentComment || 'null (top-level)'}`);
      });
    }

    // Debug processed comments and replies
    postsWithProcessedComments.forEach((post: any, index: number) => {
      if (post.comments && post.comments.length > 0) {
        console.log(`📝 Post ${index + 1} has ${post.comments.length} top-level comments:`);
        post.comments.forEach((comment: any, commentIndex: number) => {
          console.log(`  💬 Comment ${commentIndex + 1}: "${comment.content?.substring(0, 30)}..." by ${comment.user?.name}`);
          if (comment.replies && comment.replies.length > 0) {
            console.log(`    🔄 Has ${comment.replies.length} replies:`);
            comment.replies.forEach((reply: any, replyIndex: number) => {
              console.log(`      ↳ Reply ${replyIndex + 1}: "${reply.content?.substring(0, 30)}..." by ${reply.user?.name}`);
            });
          } else {
            console.log(`    🔄 No replies`);
          }
        });
      } else {
        console.log(`📝 Post ${index + 1} has no comments`);
      }
    });

    res.json({
      success: true,
      data: {
        posts: postsWithProcessedComments,
        pagination: {
          currentPage: page,
          totalPages,
          totalPosts: total,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching posts:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải bài viết',
      error: error.message
    });
  }
};

// POST /posts - Tạo bài viết mới
export const createPost = async (req: Request, res: Response) => {
  try {
    const { content, media, visibility = 'public' } = req.body;
    const userId = req.user?._id;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Nội dung bài viết không được để trống'
      });
    }

    const newPost = new Post({
      user: userId,
      content: content.trim(),
      media: media || [],
      visibility
    });

    console.log(`📝 Creating post with visibility: ${visibility} by user: ${req.user?.name || 'Unknown'} (role: ${req.user?.role || 'Unknown'})`);

    await newPost.save();

    // Populate user info for response
    const populatedPost = await Post.findById(newPost._id)
      .populate('user', 'name avatar email')
      .lean();

    // Emit socket event for real-time updates
    if (req.io) {
      req.io.emit('new-post', {
        post: populatedPost,
        message: `${(populatedPost as any)?.user?.name} đã đăng bài viết mới`
      });
    }

    res.status(201).json({
      success: true,
      message: 'Đăng bài thành công',
      data: { post: populatedPost }
    });
  } catch (error: any) {
    console.error('Error creating post:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi đăng bài viết',
      error: error.message
    });
  }
};

// GET /posts/:id - Lấy chi tiết bài viết
export const getPostById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'ID bài viết không hợp lệ'
      });
    }

    const post = await Post.findOne({ _id: id, visibility: 'public' })
      .populate('user', 'name avatar email')
      .populate({
        path: 'comments',
        populate: {
          path: 'user',
          select: 'name avatar'
        }
      })
      .lean();

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy bài viết'
      });
    }

    res.json({
      success: true,
      data: { post }
    });
  } catch (error: any) {
    console.error('Error fetching post:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải bài viết',
      error: error.message
    });
  }
};

// POST /posts/:id/react - Thả cảm xúc
export const reactToPost = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { type } = req.body;
    const userId = req.user?._id;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'ID bài viết không hợp lệ'
      });
    }

    const validReactions = ['like', 'love', 'haha', 'wow', 'sad', 'angry'];
    if (type && !validReactions.includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Loại cảm xúc không hợp lệ'
      });
    }

    const post = await Post.findOne({ _id: id, visibility: 'public' });
    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy bài viết'
      });
    }

    // Remove existing reaction from this user
    post.reactions = post.reactions.filter(
      (reaction: any) => reaction.user.toString() !== userId?.toString()
    );

    // Add new reaction if type is provided
    if (type) {
      post.reactions.push({ user: userId!, type });
    }

    await post.save();

    // Emit socket event
    if (req.io) {
      req.io.emit('post-reaction', {
        postId: id,
        userId,
        type,
        totalReactions: post.reactions.length
      });
    }

    res.json({
      success: true,
      message: type ? 'Đã thả cảm xúc' : 'Đã bỏ cảm xúc',
      data: {
        reactions: post.reactions,
        totalReactions: post.reactions.length,
        userReaction: type || null
      }
    });
  } catch (error: any) {
    console.error('Error reacting to post:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi thả cảm xúc',
      error: error.message
    });
  }
};

// POST /posts/:id/comment - Bình luận bài viết
export const commentOnPost = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { content } = req.body;
    const userId = req.user?._id;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'ID bài viết không hợp lệ'
      });
    }

    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Nội dung bình luận không được để trống'
      });
    }

    const post = await Post.findOne({ _id: id, visibility: 'public' });
    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy bài viết'
      });
    }

    const newComment = new Comment({
      user: userId,
      post: id,
      content: content.trim()
    });

    await newComment.save();
    post.comments.push(newComment._id);
    await post.save();

    // Populate comment for response
    const populatedComment = await Comment.findById(newComment._id)
      .populate('user', 'name avatar')
      .lean();

    // Emit socket event
    if (req.io) {
      req.io.emit('new-comment', {
        postId: id,
        comment: populatedComment
      });
    }

    // Tạo thông báo cho chủ bài viết (nếu không phải chính họ comment)
    if (post.user.toString() !== userId?.toString()) {
      try {
        console.log('🔔 Creating comment notification for post owner:', {
          postOwnerId: post.user,
          commenterId: userId,
          postId: id
        });

        const { createNotification } = await import('../services/notification.service');
        const commenterUser = await (await import('../models/user.model')).User.findById(userId).select('name');
        const commenterName = commenterUser?.name || 'Ai đó';

        await createNotification({
          userId: post.user,
          type: 'comment',
          title: `${commenterName} đã bình luận về bài viết của bạn`,
          message: `"${content.trim().substring(0, 100)}${content.trim().length > 100 ? '...' : ''}"`,
          priority: 'medium',
          data: {
            postId: new mongoose.Types.ObjectId(id),
            commentId: newComment._id,
            relatedUserName: commenterName,
            actionType: 'created' as const
          },
          sendEmail: true
        });
        console.log('✅ Comment notification created for post owner');
      } catch (notificationError) {
        console.error('❌ Error creating comment notification:', notificationError);
        // Don't fail the comment if notification fails
      }
    } else {
      console.log('🔕 Skipping notification - user commenting on own post');
    }

    res.status(201).json({
      success: true,
      message: 'Đã thêm bình luận',
      data: { comment: populatedComment }
    });
  } catch (error: any) {
    console.error('Error commenting on post:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi bình luận',
      error: error.message
    });
  }
};

// React to comment
export const reactToComment = async (req: any, res: Response) => {
  try {
    const { type } = req.body;
    const { commentId } = req.params;
    const userId = req.user._id;

    console.log('🎯 Comment reaction request:', { commentId, type, userId });

    if (!mongoose.Types.ObjectId.isValid(commentId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid comment ID'
      });
    }

    const comment = await Comment.findById(commentId);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    // Remove existing reaction from this user
    comment.reactions = comment.reactions.filter(
      (r: any) => r.user.toString() !== userId.toString()
    );

    // Add new reaction if type is provided and not empty
    if (type && type.trim() !== '') {
      comment.reactions.push({
        user: userId,
        type: type.trim(),
        createdAt: new Date()
      });
    }

    comment.updatedAt = new Date();
    await comment.save();

    // Populate reactions.user to get user details for response
    const populatedComment = await Comment.findById(commentId)
      .populate('reactions.user', 'name avatar')
      .lean() as any;

    // Calculate reaction summary
    const reactionSummary = populatedComment?.reactions?.reduce((acc: any, reaction: any) => {
      acc[reaction.type] = (acc[reaction.type] || 0) + 1;
      return acc;
    }, {}) || {};

    // Find current user's reaction
    const userReaction = populatedComment?.reactions?.find(
      (r: any) => r.user._id.toString() === userId.toString()
    );

    console.log('✅ Comment reaction success:', {
      commentId,
      userReaction: userReaction?.type || null,
      totalReactions: populatedComment?.reactions.length || 0
    });

    res.json({
      success: true,
      data: {
        commentId,
        reactions: populatedComment?.reactions || [],
        reactionSummary,
        userReaction: userReaction?.type || null,
        totalReactions: populatedComment?.reactions.length || 0
      }
    });
  } catch (err: any) {
    console.error('❌ Error reacting to comment:', err);
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// Reply to comment
export const replyToComment = async (req: any, res: Response) => {
  try {
    const { content } = req.body;
    const { commentId } = req.params;
    const userId = req.user._id;

    console.log('🎯 Comment reply request:', { commentId, content, userId });

    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Reply content cannot be empty'
      });
    }

    if (!mongoose.Types.ObjectId.isValid(commentId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid comment ID'
      });
    }

    const parentComment = await Comment.findById(commentId);
    if (!parentComment) {
      return res.status(404).json({
        success: false,
        message: 'Parent comment not found'
      });
    }

    // Create reply comment (simple structure)
    const reply = await Comment.create({
      user: userId,
      post: parentComment.post,
      content: content.trim(),
      parentComment: commentId, // This makes it a reply
      reactions: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    console.log('✅ Reply created:', {
      parentCommentId: commentId,
      replyId: reply._id,
      content: content.trim()
    });

    // Populate reply with user data
    const populatedReply = await Comment.findById(reply._id)
      .populate('user', 'name avatar')
      .lean();

    console.log('✅ Comment reply success:', {
      replyId: reply._id,
      parentCommentId: commentId,
      content: content.trim()
    });

    // Tạo thông báo cho người được reply (nếu không phải chính họ reply)
    if (parentComment.user.toString() !== userId.toString()) {
      try {
        const { createNotification } = await import('../services/notification.service');
        const { User } = await import('../models/user.model');
        const replierUser = await User.findById(userId).select('name');
        const replierName = replierUser?.name || 'Ai đó';

        await createNotification({
          userId: parentComment.user,
          type: 'comment',
          title: `${replierName} đã phản hồi bình luận của bạn`,
          message: `"${content.trim().substring(0, 100)}${content.trim().length > 100 ? '...' : ''}"`,
          priority: 'medium',
          data: {
            postId: parentComment.post,
            commentId: parentComment._id,
            relatedUserName: replierName,
            actionType: 'created' as const
          },
          sendEmail: true
        });
        console.log('✅ Reply notification created for comment owner');
      } catch (notificationError) {
        console.error('❌ Error creating reply notification:', notificationError);
        // Don't fail the reply if notification fails
      }
    }

    res.json({
      success: true,
      data: {
        reply: populatedReply,
        parentCommentId: commentId
      }
    });
  } catch (err: any) {
    console.error('❌ Error replying to comment:', err);
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// DELETE /posts/comments/:commentId - Xóa comment của chính mình
export const deleteComment = async (req: Request, res: Response) => {
  try {
    const { commentId } = req.params;
    const userId = req.user?._id;

    if (!mongoose.Types.ObjectId.isValid(commentId)) {
      return res.status(400).json({
        success: false,
        message: 'ID comment không hợp lệ'
      });
    }

    const comment = await Comment.findById(commentId);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy comment'
      });
    }

    // Kiểm tra quyền xóa (chỉ chủ sở hữu hoặc admin)
    if (comment.user.toString() !== userId.toString() && req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Bạn không có quyền xóa comment này'
      });
    }

    // Xóa comment và tất cả replies
    await Comment.deleteMany({
      $or: [
        { _id: commentId },
        { parentComment: commentId }
      ]
    });

    // Cập nhật post để xóa comment khỏi danh sách
    await Post.updateOne(
      { comments: commentId },
      { $pull: { comments: commentId } }
    );

    console.log('✅ Comment deleted:', {
      commentId,
      userId,
      deletedBy: req.user?.role === 'admin' ? 'admin' : 'owner'
    });

    res.json({
      success: true,
      message: 'Đã xóa comment thành công'
    });
  } catch (err: any) {
    console.error('❌ Error deleting comment:', err);
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// DELETE /posts/:id - Xóa bài viết
export const deletePost = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?._id;
    const userRole = req.user?.role;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: 'ID bài viết không hợp lệ'
      });
    }

    const post = await Post.findById(id);
    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy bài viết'
      });
    }

    // Kiểm tra quyền xóa (chỉ chủ sở hữu hoặc admin)
    if (post.user.toString() !== userId?.toString() && userRole !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Bạn không có quyền xóa bài viết này'
      });
    }

    // Xóa tất cả comments của post này
    await Comment.deleteMany({ post: id });

    // Xóa bài viết
    await Post.findByIdAndDelete(id);

    console.log('✅ Post deleted:', {
      postId: id,
      userId,
      deletedBy: userRole === 'admin' ? 'admin' : 'owner'
    });

    // Emit socket event
    if (req.io) {
      req.io.emit('post-deleted', {
        postId: id,
        deletedBy: userRole === 'admin' ? 'admin' : 'owner'
      });
    }

    res.json({
      success: true,
      message: 'Đã xóa bài viết thành công'
    });
  } catch (error: any) {
    console.error('❌ Error deleting post:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi xóa bài viết',
      error: error.message
    });
  }
};

// POST /posts/comments/:commentId/report - Báo cáo comment
export const reportComment = async (req: Request, res: Response) => {
  try {
    const { commentId } = req.params;
    const { reason } = req.body;
    const userId = req.user?._id;

    if (!mongoose.Types.ObjectId.isValid(commentId)) {
      return res.status(400).json({
        success: false,
        message: 'ID comment không hợp lệ'
      });
    }

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Lý do báo cáo là bắt buộc'
      });
    }

    const comment = await Comment.findById(commentId);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy comment'
      });
    }

    // Import CommentReport model
    const { CommentReport } = await import('../models/CommentReport');

    // Kiểm tra xem user đã báo cáo comment này chưa
    const existingReport = await CommentReport.findOne({
      user: userId,
      comment: commentId
    });

    if (existingReport) {
      return res.status(400).json({
        success: false,
        message: 'Bạn đã báo cáo comment này rồi'
      });
    }

    // Tạo báo cáo mới
    const report = new CommentReport({
      user: userId,
      comment: commentId,
      post: comment.post,
      reason: reason.trim()
    });

    await report.save();

    console.log('✅ Comment report created:', {
      reportId: report._id,
      commentId,
      userId,
      reason: reason.trim()
    });

    // Tạo thông báo cho admin (tương tự như báo cáo post)
    try {
      const { createCommentReportNotificationForAdmins } = await import('../services/notification.service');
      const { User } = await import('../models/user.model');

      const reporterUser = await User.findById(userId).select('name');
      const reporterName = reporterUser?.name || 'Người dùng';

      await createCommentReportNotificationForAdmins(
        report._id as any,
        comment._id,
        comment.post,
        reporterName,
        reason.trim()
      );
      console.log('✅ Admin notification created for comment report');
    } catch (notificationError) {
      console.error('❌ Error creating comment report notification:', notificationError);
      // Don't fail the report if notification fails
    }

    res.json({
      success: true,
      message: 'Đã gửi báo cáo thành công'
    });
  } catch (err: any) {
    console.error('❌ Error reporting comment:', err);
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};