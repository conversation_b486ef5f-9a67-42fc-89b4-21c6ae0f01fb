import mongoose, { Document, Schema } from 'mongoose';

export interface IUserBadge extends Document {
  userId: mongoose.Types.ObjectId;
  badgeType: 'supporter' | 'volunteer' | 'organizer' | 'vip' | 'champion' | 'hero';
  badgeLevel: number; // 1-5 levels for each badge type
  earnedAt: Date;
  totalDonations?: number;
  totalEvents?: number;
  totalPosts?: number;
  isActive: boolean;
  metadata?: {
    lastDonationAmount?: number;
    lastDonationDate?: Date;
    consecutiveMonths?: number;
    specialAchievements?: string[];
  };
}

const UserBadgeSchema = new Schema<IUserBadge>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  badgeType: {
    type: String,
    enum: ['supporter', 'volunteer', 'organizer', 'vip', 'champion', 'hero'],
    required: true
  },
  badgeLevel: {
    type: Number,
    min: 1,
    max: 5,
    default: 1
  },
  earnedAt: {
    type: Date,
    default: Date.now
  },
  totalDonations: {
    type: Number,
    default: 0
  },
  totalEvents: {
    type: Number,
    default: 0
  },
  totalPosts: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  metadata: {
    lastDonationAmount: Number,
    lastDonationDate: Date,
    consecutiveMonths: {
      type: Number,
      default: 0
    },
    specialAchievements: [String]
  }
}, {
  timestamps: true
});

// Compound index for efficient queries
UserBadgeSchema.index({ userId: 1, badgeType: 1 }, { unique: true });
UserBadgeSchema.index({ badgeType: 1, badgeLevel: -1 });

export const UserBadge = mongoose.model<IUserBadge>('UserBadge', UserBadgeSchema);

// Badge configuration
export const BADGE_CONFIG = {
  supporter: {
    name: 'Người Ủng Hộ',
    icon: '💜',
    color: '#8B5CF6',
    levels: {
      1: { name: 'Tân Binh', minDonations: 1, minAmount: 50000 },
      2: { name: 'Nhiệt Tình', minDonations: 3, minAmount: 200000 },
      3: { name: 'Tận Tâm', minDonations: 5, minAmount: 500000 },
      4: { name: 'Hảo Tâm', minDonations: 10, minAmount: 1000000 },
      5: { name: 'Thiện Nguyện', minDonations: 20, minAmount: 5000000 }
    }
  },
  volunteer: {
    name: 'Tình Nguyện Viên',
    icon: '🤝',
    color: '#10B981',
    levels: {
      1: { name: 'Mới Tham Gia', minEvents: 1 },
      2: { name: 'Tích Cực', minEvents: 3 },
      3: { name: 'Nhiệt Huyết', minEvents: 5 },
      4: { name: 'Cam Kết', minEvents: 10 },
      5: { name: 'Tận Hiến', minEvents: 20 }
    }
  },
  organizer: {
    name: 'Người Tổ Chức',
    icon: '🎯',
    color: '#F59E0B',
    levels: {
      1: { name: 'Khởi Đầu', minOrganized: 1 },
      2: { name: 'Kinh Nghiệm', minOrganized: 3 },
      3: { name: 'Chuyên Nghiệp', minOrganized: 5 },
      4: { name: 'Xuất Sắc', minOrganized: 10 },
      5: { name: 'Chuyên Gia', minOrganized: 20 }
    }
  },
  vip: {
    name: 'VIP',
    icon: '👑',
    color: '#EF4444',
    levels: {
      1: { name: 'Bạc', minAmount: 10000000 },
      2: { name: 'Vàng', minAmount: 50000000 },
      3: { name: 'Bạch Kim', minAmount: 100000000 },
      4: { name: 'Kim Cương', minAmount: 500000000 },
      5: { name: 'Huyền Thoại', minAmount: 1000000000 }
    }
  },
  champion: {
    name: 'Nhà Vô Địch',
    icon: '🏆',
    color: '#8B5CF6',
    levels: {
      1: { name: 'Đồng', minScore: 100 },
      2: { name: 'Bạc', minScore: 500 },
      3: { name: 'Vàng', minScore: 1000 },
      4: { name: 'Bạch Kim', minScore: 5000 },
      5: { name: 'Huyền Thoại', minScore: 10000 }
    }
  },
  hero: {
    name: 'Anh Hùng Cộng Đồng',
    icon: '🦸',
    color: '#DC2626',
    levels: {
      1: { name: 'Người Hùng Mới', special: true },
      2: { name: 'Người Hùng', special: true },
      3: { name: 'Siêu Anh Hùng', special: true },
      4: { name: 'Huyền Thoại', special: true },
      5: { name: 'Thần Thoại', special: true }
    }
  }
};
