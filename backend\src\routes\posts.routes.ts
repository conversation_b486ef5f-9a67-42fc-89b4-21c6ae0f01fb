import { Router } from 'express';
import {
  getPosts,
  createPost,
  getPostById,
  reactToPost,
  commentOnPost,
  reactToComment,
  replyToComment,
  deleteComment,
  deletePost,
  reportComment
} from '../controllers/posts.controller';
import { reportPost } from '../controllers/post.controller';
import { verifyToken } from '../middlewares/auth.middleware';
import { body, query } from 'express-validator';
import { validateRequest } from '../middlewares/validation.middleware';

const router = Router();

// Validation middleware
const createPostValidation = [
  body('content')
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Nội dung bài viết phải từ 1-2000 ký tự'),
  body('visibility')
    .optional()
    .isIn(['public', 'private', 'friends'])
    .withMessage('Visibility phải là public, private hoặc friends'),
  body('media')
    .optional()
    .isArray()
    .withMessage('Media phải là một mảng'),
  body('media.*')
    .optional()
    .custom((value) => {
      // Chấp nhận cả URL và base64 data URI
      if (typeof value === 'string') {
        // Kiểm tra nếu là URL hợp lệ
        try {
          new URL(value);
          return true;
        } catch {
          // Nếu không phải URL, kiểm tra nếu là base64 data URI
          if (value.startsWith('data:image/') && value.includes('base64,')) {
            return true;
          }
          throw new Error('Media phải là URL hợp lệ hoặc base64 data URI');
        }
      }
      throw new Error('Media phải là string');
    })
];

const getPostsValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page phải là số nguyên dương'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit phải từ 1-50'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Từ khóa tìm kiếm tối đa 100 ký tự'),
  query('hasMedia')
    .optional()
    .isBoolean()
    .withMessage('hasMedia phải là boolean')
];

const reactValidation = [
  body('type')
    .optional()
    .custom((value) => {
      // Allow null, empty string, or valid reaction types
      if (value === null || value === '' || ['like', 'love', 'haha', 'wow', 'sad', 'angry'].includes(value)) {
        return true;
      }
      throw new Error('Loại cảm xúc không hợp lệ');
    })
];

const commentValidation = [
  body('content')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Nội dung bình luận phải từ 1-500 ký tự')
];

// Routes

/**
 * @route GET /posts
 * @desc Lấy danh sách bài viết công khai (newsfeed)
 * @access Private (cần đăng nhập)
 * @query page - Trang hiện tại (default: 1)
 * @query limit - Số bài viết mỗi trang (default: 10, max: 50)
 * @query search - Từ khóa tìm kiếm trong nội dung
 * @query author - ID người đăng
 * @query hasMedia - Chỉ lấy bài có media (true/false)
 */
router.get('/',
  verifyToken,
  getPostsValidation,
  validateRequest,
  getPosts
);

/**
 * @route POST /posts
 * @desc Tạo bài viết mới
 * @access Private (cần đăng nhập)
 * @body content - Nội dung bài viết (required)
 * @body media - Mảng URL media (optional)
 * @body visibility - public/private/friends (default: public)
 */
router.post('/',
  verifyToken,
  createPostValidation,
  validateRequest,
  createPost
);

/**
 * @route GET /posts/:id
 * @desc Lấy chi tiết bài viết
 * @access Private (cần đăng nhập)
 */
router.get('/:id',
  verifyToken,
  getPostById
);

/**
 * @route DELETE /posts/:id
 * @desc Xóa bài viết (chỉ chủ sở hữu hoặc admin)
 * @access Private (cần đăng nhập)
 */
router.delete('/:id',
  verifyToken,
  deletePost
);

/**
 * @route POST /posts/:id/react
 * @desc Thả cảm xúc cho bài viết
 * @access Private (cần đăng nhập)
 * @body type - Loại cảm xúc (like/love/haha/sad/angry) hoặc null để bỏ cảm xúc
 */
router.post('/:id/react',
  verifyToken,
  reactValidation,
  validateRequest,
  reactToPost
);

/**
 * @route POST /posts/:id/comment
 * @desc Bình luận bài viết
 * @access Private (cần đăng nhập)
 * @body content - Nội dung bình luận (required)
 */
router.post('/:id/comment',
  verifyToken,
  commentValidation,
  validateRequest,
  commentOnPost
);

/**
 * @route POST /posts/comments/:commentId/react
 * @desc Thả cảm xúc cho comment
 * @access Private (cần đăng nhập)
 * @body type - Loại cảm xúc (like/love/haha/wow/sad/angry) hoặc null để bỏ cảm xúc
 */
router.post('/comments/:commentId/react',
  verifyToken,
  reactValidation,
  validateRequest,
  reactToComment
);

/**
 * @route POST /posts/comments/:commentId/reply
 * @desc Reply comment
 * @access Private (cần đăng nhập)
 * @body content - Nội dung reply (required)
 */
router.post('/comments/:commentId/reply',
  verifyToken,
  commentValidation,
  validateRequest,
  replyToComment
);

/**
 * @route POST /posts/:id/report
 * @desc Báo cáo bài viết
 * @access Private (cần đăng nhập)
 * @body reason - Lý do báo cáo (required)
 */
router.post('/:id/report',
  verifyToken,
  body('reason').notEmpty().withMessage('Lý do báo cáo là bắt buộc'),
  validateRequest,
  reportPost
);

/**
 * @route DELETE /posts/comments/:commentId
 * @desc Xóa comment của chính mình
 * @access Private (cần đăng nhập)
 */
router.delete('/comments/:commentId',
  verifyToken,
  deleteComment
);

/**
 * @route POST /posts/comments/:commentId/report
 * @desc Báo cáo comment
 * @access Private (cần đăng nhập)
 * @body reason - Lý do báo cáo (required)
 */
router.post('/comments/:commentId/report',
  verifyToken,
  body('reason').notEmpty().withMessage('Lý do báo cáo là bắt buộc'),
  validateRequest,
  reportComment
);

export default router;
