"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.reportPost = exports.commentPost = exports.reactPost = exports.getPosts = exports.deletePost = exports.createPost = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const Report_1 = require("../models/Report");
const notification_service_1 = require("../services/notification.service");
// Create Post model directly to avoid import issues
const Schema = mongoose_1.default.Schema;
const ReactionSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    type: { type: String, enum: ['like', 'love', 'haha', 'sad', 'angry'], required: true }
}, { _id: false });
const PostSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    content: { type: String, required: true },
    media: [{ type: String }],
    reactions: [ReactionSchema],
    comments: [{ type: Schema.Types.ObjectId, ref: 'Comment' }],
    reports: [{ type: Schema.Types.ObjectId, ref: 'Report' }],
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});
const Post = mongoose_1.default.models.Post || mongoose_1.default.model('Post', PostSchema);
// Comment model
const CommentSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    post: { type: Schema.Types.ObjectId, ref: 'Post', required: true },
    content: { type: String, required: true },
    createdAt: { type: Date, default: Date.now }
});
const Comment = mongoose_1.default.models.Comment || mongoose_1.default.model('Comment', CommentSchema);
// Report model is imported from ../models/Report
// User model for populate
const UserSchema = new Schema({
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    avatar: { type: String, default: '' },
    role: { type: String, enum: ['user', 'admin'], default: 'user' },
    createdAt: { type: Date, default: Date.now }
});
const User = mongoose_1.default.models.User || mongoose_1.default.model('User', UserSchema);
// Models are now properly initialized
const createPost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { content } = req.body;
        if (!content || content.trim() === '') {
            return res.status(400).json({ success: false, message: 'Content is required' });
        }
        const post = yield Post.create({
            user: (_a = req.user) === null || _a === void 0 ? void 0 : _a._id,
            content: content.trim(),
            media: [] // Tạm thời không hỗ trợ media
        });
        // Populate user information để frontend không cần gọi API thêm
        const populatedPost = yield Post.findById(post._id).populate('user', 'name email avatar');
        res.json({ success: true, post: populatedPost });
    }
    catch (err) {
        console.error('CREATE POST ERROR:', err);
        res.status(500).json({ success: false, message: err.message });
    }
});
exports.createPost = createPost;
const deletePost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const post = yield Post.findById(req.params.id);
        if (!post)
            return res.status(404).json({ message: 'Post not found' });
        if (post.user.toString() !== ((_a = req.user) === null || _a === void 0 ? void 0 : _a._id.toString()) && ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) !== 'admin') {
            return res.status(403).json({ message: 'Not allowed' });
        }
        yield post.remove();
        res.json({ success: true });
    }
    catch (err) {
        res.status(500).json({ success: false, message: err.message });
    }
});
exports.deletePost = deletePost;
const getPosts = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const posts = yield Post.find()
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .populate('user', 'name avatar')
            .populate({ path: 'comments', populate: { path: 'user', select: 'name avatar' } });
        res.json({ success: true, posts });
    }
    catch (err) {
        res.status(500).json({ success: false, message: err.message });
    }
});
exports.getPosts = getPosts;
const reactPost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { type } = req.body;
        const post = yield Post.findById(req.params.id);
        if (!post)
            return res.status(404).json({ message: 'Post not found' });
        post.reactions = post.reactions.filter((r) => { var _a; return r.user.toString() !== ((_a = req.user) === null || _a === void 0 ? void 0 : _a._id.toString()); });
        post.reactions.push({ user: (_a = req.user) === null || _a === void 0 ? void 0 : _a._id, type });
        yield post.save();
        res.json({ success: true, reactions: post.reactions });
    }
    catch (err) {
        res.status(500).json({ success: false, message: err.message });
    }
});
exports.reactPost = reactPost;
const commentPost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { content } = req.body;
        const post = yield Post.findById(req.params.id).populate('user', 'name');
        if (!post)
            return res.status(404).json({ message: 'Post not found' });
        const comment = yield Comment.create({
            user: (_a = req.user) === null || _a === void 0 ? void 0 : _a._id,
            post: post._id,
            content
        });
        post.comments.push(comment._id);
        yield post.save();
        // Send notification to post owner (if not commenting on own post)
        const commenterId = (_b = req.user) === null || _b === void 0 ? void 0 : _b._id;
        const postOwnerId = post.user._id || post.user;
        if (commenterId && postOwnerId && commenterId.toString() !== postOwnerId.toString()) {
            try {
                const commenterUser = yield User.findById(commenterId).select('name');
                if (commenterUser) {
                    yield (0, notification_service_1.notifyNewComment)(postOwnerId, commenterUser.name, post._id);
                    console.log('📧 [Comment] Notification sent to post owner');
                }
            }
            catch (notificationError) {
                console.error('❌ [Comment] Error sending notification:', notificationError);
                // Don't fail the comment creation if notification fails
            }
        }
        res.json({ success: true, comment });
    }
    catch (err) {
        res.status(500).json({ success: false, message: err.message });
    }
});
exports.commentPost = commentPost;
const reportPost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e;
    try {
        console.log('🚨 Report request received:', {
            postId: req.params.id,
            reason: req.body.reason,
            userId: (_a = req.user) === null || _a === void 0 ? void 0 : _a._id
        });
        const { reason } = req.body;
        if (!reason || reason.trim() === '') {
            console.log('❌ Missing reason');
            return res.status(400).json({ success: false, message: 'Lý do báo cáo là bắt buộc' });
        }
        const post = yield Post.findById(req.params.id);
        if (!post) {
            console.log('❌ Post not found:', req.params.id);
            return res.status(404).json({ success: false, message: 'Không tìm thấy bài viết' });
        }
        console.log('✅ Post found:', post._id);
        const existed = yield Report_1.Report.findOne({ user: (_b = req.user) === null || _b === void 0 ? void 0 : _b._id, post: post._id });
        if (existed) {
            console.log('❌ Already reported by user:', (_c = req.user) === null || _c === void 0 ? void 0 : _c._id);
            return res.status(400).json({ success: false, message: 'Bạn đã báo cáo bài viết này rồi' });
        }
        console.log('✅ Creating new report...');
        const report = yield Report_1.Report.create({
            user: (_d = req.user) === null || _d === void 0 ? void 0 : _d._id,
            post: post._id,
            reason: reason.trim()
        });
        console.log('✅ Report created:', report._id);
        post.reports.push(report._id);
        yield post.save();
        console.log('✅ Post updated with report');
        // Create notification for admins
        try {
            const { createReportNotificationForAdmins } = yield Promise.resolve().then(() => __importStar(require('../services/notification.service')));
            const reporterUser = yield User.findById((_e = req.user) === null || _e === void 0 ? void 0 : _e._id).select('name');
            const reporterName = (reporterUser === null || reporterUser === void 0 ? void 0 : reporterUser.name) || 'Người dùng';
            yield createReportNotificationForAdmins(report._id, post._id, reporterName, reason);
            console.log('✅ Admin notification created');
        }
        catch (notificationError) {
            console.error('❌ Error creating report notification:', notificationError);
            // Don't fail the report if notification fails
        }
        console.log('✅ Report successful, sending response');
        res.json({ success: true, message: 'Đã báo cáo bài viết thành công', report });
    }
    catch (err) {
        console.error('❌ Report error:', err);
        res.status(500).json({ success: false, message: 'Lỗi khi báo cáo bài viết', error: err.message });
    }
});
exports.reportPost = reportPost;
