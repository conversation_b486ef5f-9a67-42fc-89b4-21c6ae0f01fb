import React, { useState, useEffect } from 'react';
import axios from 'axios';
import UserBadge from './UserBadge';
import { useAuth } from '../../contexts/AuthContext';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface Badge {
  _id: string;
  badgeType: string;
  badgeLevel: number;
  earnedAt: string;
  config: {
    name: string;
    icon: string;
    color: string;
    levels: {
      [key: number]: {
        name: string;
        [key: string]: any;
      };
    };
  };
  levelName: string;
}

interface BadgeListProps {
  userId?: string; // If provided, show badges for this user (public view)
  showTitle?: boolean;
  maxDisplay?: number; // Maximum number of badges to display
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const BadgeList: React.FC<BadgeListProps> = ({ 
  userId, 
  showTitle = true, 
  maxDisplay,
  size = 'md',
  className = ''
}) => {
  const { user } = useAuth();
  const [badges, setBadges] = useState<Badge[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBadges();
  }, [userId, user]);

  const fetchBadges = async () => {
    try {
      setLoading(true);
      setError(null);

      let url: string;
      const headers: any = {};

      if (userId) {
        // Public view - get badges for specific user
        url = `${API_URL}/api/badges/user/${userId}`;
      } else {
        // Private view - get current user's badges
        url = `${API_URL}/api/badges/my-badges`;
        const token = localStorage.getItem('token');
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        }
      }

      const response = await axios.get(url, { headers });

      if (response.data.success) {
        let userBadges = response.data.data || [];
        
        // Limit display if maxDisplay is set
        if (maxDisplay && userBadges.length > maxDisplay) {
          userBadges = userBadges.slice(0, maxDisplay);
        }

        setBadges(userBadges);
      } else {
        setError('Không thể tải huy hiệu');
      }
    } catch (err) {
      console.error('Error fetching badges:', err);
      setError('Lỗi khi tải huy hiệu');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        {showTitle && <h3 className="text-sm font-medium text-gray-700 mb-2">Huy hiệu</h3>}
        <div className="flex gap-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse bg-gray-200 rounded-full h-6 w-16"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className}`}>
        {showTitle && <h3 className="text-sm font-medium text-gray-700 mb-2">Huy hiệu</h3>}
        <p className="text-xs text-gray-500">{error}</p>
      </div>
    );
  }

  if (badges.length === 0) {
    return (
      <div className={`${className}`}>
        {showTitle && <h3 className="text-sm font-medium text-gray-700 mb-2">Huy hiệu</h3>}
        <p className="text-xs text-gray-500">Chưa có huy hiệu nào</p>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {showTitle && (
        <h3 className="text-sm font-medium text-gray-700 mb-2">
          Huy hiệu {maxDisplay && badges.length >= maxDisplay && `(${maxDisplay}+)`}
        </h3>
      )}
      
      <div className="flex flex-wrap gap-1.5">
        {badges.map((badge) => (
          <UserBadge
            key={badge._id}
            badge={badge}
            size={size}
            showTooltip={true}
          />
        ))}
        
        {maxDisplay && badges.length >= maxDisplay && (
          <span className="text-xs text-gray-500 self-center ml-1">
            +{badges.length - maxDisplay} khác
          </span>
        )}
      </div>
    </div>
  );
};

export default BadgeList;
