import mongoose, { Document } from 'mongoose';
export interface IUserBadge extends Document {
    userId: mongoose.Types.ObjectId;
    badgeType: 'supporter' | 'volunteer' | 'organizer' | 'vip' | 'champion' | 'hero';
    badgeLevel: number;
    earnedAt: Date;
    totalDonations?: number;
    totalEvents?: number;
    totalPosts?: number;
    isActive: boolean;
    metadata?: {
        lastDonationAmount?: number;
        lastDonationDate?: Date;
        consecutiveMonths?: number;
        specialAchievements?: string[];
    };
}
export declare const UserBadge: mongoose.Model<IUserBadge, {}, {}, {}, mongoose.Document<unknown, {}, IUserBadge, {}> & IUserBadge & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export declare const BADGE_CONFIG: {
    supporter: {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            2: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            3: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            4: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            5: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
        };
    };
    volunteer: {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minEvents: number;
            };
            2: {
                name: string;
                minEvents: number;
            };
            3: {
                name: string;
                minEvents: number;
            };
            4: {
                name: string;
                minEvents: number;
            };
            5: {
                name: string;
                minEvents: number;
            };
        };
    };
    organizer: {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minOrganized: number;
            };
            2: {
                name: string;
                minOrganized: number;
            };
            3: {
                name: string;
                minOrganized: number;
            };
            4: {
                name: string;
                minOrganized: number;
            };
            5: {
                name: string;
                minOrganized: number;
            };
        };
    };
    vip: {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minAmount: number;
            };
            2: {
                name: string;
                minAmount: number;
            };
            3: {
                name: string;
                minAmount: number;
            };
            4: {
                name: string;
                minAmount: number;
            };
            5: {
                name: string;
                minAmount: number;
            };
        };
    };
    champion: {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minScore: number;
            };
            2: {
                name: string;
                minScore: number;
            };
            3: {
                name: string;
                minScore: number;
            };
            4: {
                name: string;
                minScore: number;
            };
            5: {
                name: string;
                minScore: number;
            };
        };
    };
    hero: {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                special: boolean;
            };
            2: {
                name: string;
                special: boolean;
            };
            3: {
                name: string;
                special: boolean;
            };
            4: {
                name: string;
                special: boolean;
            };
            5: {
                name: string;
                special: boolean;
            };
        };
    };
};
//# sourceMappingURL=UserBadge.d.ts.map