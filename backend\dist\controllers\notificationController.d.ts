import { Request, Response } from 'express';
import mongoose from 'mongoose';
export declare const createNotification: (data: {
    userId: mongoose.Types.ObjectId;
    type: "donation" | "campaign" | "event" | "post" | "comment" | "system" | "report" | "admin";
    title: string;
    message: string;
    data?: {
        campaignId?: mongoose.Types.ObjectId;
        donationId?: mongoose.Types.ObjectId;
        eventId?: mongoose.Types.ObjectId;
        postId?: mongoose.Types.ObjectId;
        commentId?: mongoose.Types.ObjectId;
        reportId?: mongoose.Types.ObjectId;
        amount?: number;
        status?: string;
        reason?: string;
        actionType?: "created" | "updated" | "deleted" | "cancelled" | "completed" | "reminder";
        relatedUserId?: mongoose.Types.ObjectId;
        relatedUserName?: string;
    };
}) => Promise<any>;
export declare const getUserNotifications: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const markAsRead: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const markAllAsRead: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getUnreadCount: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const createAdminNotification: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getNotificationStats: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const createTestNotification: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
//# sourceMappingURL=notificationController.d.ts.map