{"version": 3, "file": "Notification.d.ts", "sourceRoot": "", "sources": ["../../src/models/Notification.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,EAAE,EAAE,QAAQ,EAAU,MAAM,UAAU,CAAC;AAEtD,MAAM,WAAW,aAAc,SAAQ,QAAQ;IAC7C,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;IAChC,IAAI,EAAE,UAAU,GAAG,UAAU,GAAG,OAAO,GAAG,MAAM,GAAG,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC;IAC7F,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE;QACL,UAAU,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QACrC,UAAU,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QACrC,OAAO,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QAClC,MAAM,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QACjC,SAAS,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QACpC,QAAQ,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QACnC,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,UAAU,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,WAAW,GAAG,UAAU,CAAC;QACxF,aAAa,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QACxC,eAAe,CAAC,EAAE,MAAM,CAAC;KAC1B,CAAC;IACF,MAAM,EAAE,OAAO,CAAC;IAChB,SAAS,EAAE,OAAO,CAAC;IACnB,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC;IAC/C,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;CACjB;AAqFD,eAAO,MAAM,YAAY,2CAAoG,CAAC"}