import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import {
  User,
  Lock,
  Bell,
  Globe,
  Shield,
  Eye,
  EyeOff,
  Save,
  Camera,
  Mail,
  Phone,
  MapPin,
  Edit3,
  Trash2,
  Download,
  Upload,
  Moon,
  Sun,
  Languages,
  Smartphone,
  Monitor
} from 'lucide-react';
import api from '../services/api';

interface UserSettings {
  name: string;
  email: string;
  phone: string;
  address: string;
  bio: string;
  avatar: string;
  preferences: {
    theme: 'light' | 'dark';
    language: 'vi' | 'en';
    emailNotifications: boolean;
    pushNotifications: boolean;
  };
}

const Settings: React.FC = () => {
  const { user, updateUser, logout } = useAuth();
  const { theme, setTheme } = useTheme();
  const { language, setLanguage, t } = useLanguage();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'general' | 'account' | 'privacy' | 'notifications' | 'security'>('general');
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Form states
  const [generalForm, setGeneralForm] = useState<UserSettings>({
    name: '',
    email: '',
    phone: '',
    address: '',
    bio: '',
    avatar: '',
    preferences: {
      theme: 'light',
      language: 'vi',
      emailNotifications: true,
      pushNotifications: true
    }
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: 'public', // public, friends, private
    showEmail: false,
    showPhone: false,
    allowMessages: true,
    allowFriendRequests: true
  });

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    // Initialize form with user data
    setGeneralForm({
      name: user.name || '',
      email: user.email || '',
      phone: user.phone || '',
      address: user.address || '',
      bio: user.bio || '',
      avatar: user.avatar || '',
      preferences: {
        theme: theme,
        language: language,
        emailNotifications: user.notificationPreferences?.emailNotifications ?? true,
        pushNotifications: user.notificationPreferences?.pushNotifications ?? true
      }
    });
  }, [user, navigate]);

  const handleGeneralSave = async () => {
    setLoading(true);
    try {
      // Update basic profile info
      await updateUser({
        name: generalForm.name,
        phone: generalForm.phone,
        address: generalForm.address,
        bio: generalForm.bio
      });

      // Update preferences separately if needed
      if (generalForm.preferences) {
        try {
          await api.put('/api/auth/preferences', generalForm.preferences);

          // Update local theme and language
          setTheme(generalForm.preferences.theme);
          setLanguage(generalForm.preferences.language);

          console.log('✅ Preferences updated successfully');
        } catch (prefError) {
          console.log('⚠️ Preferences update failed, but profile updated');
        }
      }

      toast.success(t('message.updateSuccess'));
    } catch (error: any) {
      console.error('❌ Settings save error:', error);
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật thông tin');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast.error('Mật khẩu xác nhận không khớp');
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      toast.error('Mật khẩu mới phải có ít nhất 6 ký tự');
      return;
    }

    setLoading(true);
    try {
      await api.put('/api/auth/change-password', {
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword
      });
      
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      
      toast.success('Đổi mật khẩu thành công!');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Có lỗi xảy ra khi đổi mật khẩu');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 5 * 1024 * 1024) {
      toast.error('Kích thước ảnh không được vượt quá 5MB');
      return;
    }

    const formData = new FormData();
    formData.append('avatar', file);

    setLoading(true);
    try {
      const response = await api.put('/api/auth/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.success) {
        setGeneralForm(prev => ({
          ...prev,
          avatar: response.data.data.avatar
        }));
        toast.success('Cập nhật ảnh đại diện thành công!');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Có lỗi xảy ra khi tải ảnh lên');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (!confirm('Bạn có chắc chắn muốn xóa tài khoản? Hành động này không thể hoàn tác!')) {
      return;
    }

    const confirmText = prompt('Nhập "XÓA TÀI KHOẢN" để xác nhận:');
    if (confirmText !== 'XÓA TÀI KHOẢN') {
      toast.error('Xác nhận không đúng');
      return;
    }

    setLoading(true);
    try {
      await api.delete('/api/auth/delete-account');
      toast.success('Tài khoản đã được xóa');
      logout();
      navigate('/');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa tài khoản');
    } finally {
      setLoading(false);
    }
  };

  const exportData = async () => {
    try {
      const response = await api.get('/api/auth/export-data', {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `keydyweb-data-${new Date().toISOString().split('T')[0]}.json`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      toast.success('Xuất dữ liệu thành công!');
    } catch (error: any) {
      toast.error('Có lỗi xảy ra khi xuất dữ liệu');
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'general', name: t('settings.general'), icon: User },
    { id: 'account', name: t('settings.account'), icon: Mail },
    { id: 'security', name: t('settings.security'), icon: Lock },
    { id: 'notifications', name: t('settings.notifications'), icon: Bell },
    { id: 'privacy', name: t('settings.privacy'), icon: Shield }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-6xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('settings.title')}</h1>
          <p className="text-gray-600">{t('settings.subtitle')}</p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-1/4">
            <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-8">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`w-full flex items-center px-4 py-3 text-left rounded-xl transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-purple-600'
                      }`}
                    >
                      <Icon className="w-5 h-5 mr-3" />
                      <span className="font-medium">{tab.name}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:w-3/4">
            <div className="bg-white rounded-2xl shadow-lg p-8">
              {/* General Settings */}
              {activeTab === 'general' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">Thông tin chung</h2>
                    
                    {/* Avatar Section */}
                    <div className="flex items-center space-x-6 mb-8 p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl">
                      <div className="relative">
                        <div className="w-24 h-24 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white text-2xl font-bold overflow-hidden">
                          {generalForm.avatar ? (
                            <img src={generalForm.avatar} alt="Avatar" className="w-full h-full object-cover" />
                          ) : (
                            generalForm.name.charAt(0).toUpperCase()
                          )}
                        </div>
                        <label className="absolute -bottom-2 -right-2 bg-white rounded-full p-2 shadow-lg cursor-pointer hover:bg-gray-50 transition-colors">
                          <Camera className="w-4 h-4 text-gray-600" />
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleAvatarUpload}
                            className="hidden"
                          />
                        </label>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{generalForm.name}</h3>
                        <p className="text-gray-600">{generalForm.email}</p>
                        <p className="text-sm text-purple-600 mt-1">Thành viên từ {new Date(user.createdAt || '').toLocaleDateString('vi-VN')}</p>
                      </div>
                    </div>

                    {/* Form Fields */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <User className="w-4 h-4 inline mr-2" />
                          Họ và tên
                        </label>
                        <input
                          type="text"
                          value={generalForm.name}
                          onChange={(e) => setGeneralForm(prev => ({ ...prev, name: e.target.value }))}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                          placeholder="Nhập họ và tên"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <Phone className="w-4 h-4 inline mr-2" />
                          Số điện thoại
                        </label>
                        <input
                          type="tel"
                          value={generalForm.phone}
                          onChange={(e) => setGeneralForm(prev => ({ ...prev, phone: e.target.value }))}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                          placeholder="Nhập số điện thoại"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <MapPin className="w-4 h-4 inline mr-2" />
                          Địa chỉ
                        </label>
                        <input
                          type="text"
                          value={generalForm.address}
                          onChange={(e) => setGeneralForm(prev => ({ ...prev, address: e.target.value }))}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                          placeholder="Nhập địa chỉ"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <Edit3 className="w-4 h-4 inline mr-2" />
                          Giới thiệu bản thân
                        </label>
                        <textarea
                          value={generalForm.bio}
                          onChange={(e) => setGeneralForm(prev => ({ ...prev, bio: e.target.value }))}
                          rows={4}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all resize-none"
                          placeholder="Viết vài dòng giới thiệu về bản thân..."
                        />
                      </div>
                    </div>

                    {/* Theme & Language */}
                    <div className="mt-8 p-6 bg-gray-50 rounded-2xl">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('settings.displayOptions')}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            <Monitor className="w-4 h-4 inline mr-2" />
                            {t('settings.theme')}
                          </label>
                          <select
                            value={theme}
                            onChange={(e) => {
                              const newTheme = e.target.value as 'light' | 'dark';
                              setTheme(newTheme);
                              setGeneralForm(prev => ({
                                ...prev,
                                preferences: { ...prev.preferences, theme: newTheme }
                              }));
                            }}
                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                          >
                            <option value="light">{t('settings.theme.light')}</option>
                            <option value="dark">{t('settings.theme.dark')}</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            <Languages className="w-4 h-4 inline mr-2" />
                            {t('settings.language')}
                          </label>
                          <select
                            value={language}
                            onChange={(e) => {
                              const newLanguage = e.target.value as 'vi' | 'en';
                              setLanguage(newLanguage);
                              setGeneralForm(prev => ({
                                ...prev,
                                preferences: { ...prev.preferences, language: newLanguage }
                              }));
                            }}
                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                          >
                            <option value="vi">{t('settings.language.vi')}</option>
                            <option value="en">{t('settings.language.en')}</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end pt-6">
                      <button
                        onClick={handleGeneralSave}
                        disabled={loading}
                        className="flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {loading ? 'Đang lưu...' : 'Lưu thay đổi'}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Account Settings */}
              {activeTab === 'account' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">Thông tin tài khoản</h2>

                    <div className="space-y-6">
                      <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Email đăng nhập</h3>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-gray-900 font-medium">{user.email}</p>
                            <p className="text-sm text-gray-600">Email này được sử dụng để đăng nhập và nhận thông báo</p>
                          </div>
                          <button className="px-4 py-2 text-purple-600 border border-purple-600 rounded-lg hover:bg-purple-50 transition-colors">
                            Thay đổi
                          </button>
                        </div>
                      </div>

                      <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Xuất dữ liệu cá nhân</h3>
                        <p className="text-gray-600 mb-4">Tải xuống bản sao dữ liệu cá nhân của bạn</p>
                        <button
                          onClick={exportData}
                          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Xuất dữ liệu
                        </button>
                      </div>

                      <div className="p-6 bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl border border-red-200">
                        <h3 className="text-lg font-semibold text-red-900 mb-4">Xóa tài khoản</h3>
                        <p className="text-red-700 mb-4">
                          Xóa vĩnh viễn tài khoản và tất cả dữ liệu liên quan. Hành động này không thể hoàn tác.
                        </p>
                        <button
                          onClick={handleDeleteAccount}
                          disabled={loading}
                          className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Xóa tài khoản
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Security Settings */}
              {activeTab === 'security' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">Bảo mật</h2>

                    {/* Change Password */}
                    <div className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Đổi mật khẩu</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Mật khẩu hiện tại</label>
                          <div className="relative">
                            <input
                              type={showCurrentPassword ? 'text' : 'password'}
                              value={passwordForm.currentPassword}
                              onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                              className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                              placeholder="Nhập mật khẩu hiện tại"
                            />
                            <button
                              type="button"
                              onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                            >
                              {showCurrentPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                            </button>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Mật khẩu mới</label>
                          <div className="relative">
                            <input
                              type={showNewPassword ? 'text' : 'password'}
                              value={passwordForm.newPassword}
                              onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                              className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                              placeholder="Nhập mật khẩu mới"
                            />
                            <button
                              type="button"
                              onClick={() => setShowNewPassword(!showNewPassword)}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                            >
                              {showNewPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                            </button>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Xác nhận mật khẩu mới</label>
                          <div className="relative">
                            <input
                              type={showConfirmPassword ? 'text' : 'password'}
                              value={passwordForm.confirmPassword}
                              onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                              className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                              placeholder="Nhập lại mật khẩu mới"
                            />
                            <button
                              type="button"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                            >
                              {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                            </button>
                          </div>
                        </div>

                        <button
                          onClick={handlePasswordChange}
                          disabled={loading || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
                          className="flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Lock className="w-4 h-4 mr-2" />
                          {loading ? 'Đang cập nhật...' : 'Đổi mật khẩu'}
                        </button>
                      </div>
                    </div>

                    {/* Login Sessions */}
                    <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Phiên đăng nhập</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-4 bg-white rounded-lg border">
                          <div className="flex items-center space-x-3">
                            <Monitor className="w-5 h-5 text-green-600" />
                            <div>
                              <p className="font-medium text-gray-900">Thiết bị hiện tại</p>
                              <p className="text-sm text-gray-600">Đăng nhập lúc {new Date().toLocaleString('vi-VN')}</p>
                            </div>
                          </div>
                          <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Đang hoạt động</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Notifications Settings */}
              {activeTab === 'notifications' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">Cài đặt thông báo</h2>

                    <div className="space-y-6">
                      <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Thông báo email</h3>
                        <div className="space-y-4">
                          <label className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">Thông báo chung</p>
                              <p className="text-sm text-gray-600">Nhận email về các hoạt động quan trọng</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={generalForm.preferences.emailNotifications}
                              onChange={(e) => setGeneralForm(prev => ({
                                ...prev,
                                preferences: { ...prev.preferences, emailNotifications: e.target.checked }
                              }))}
                              className="w-5 h-5 text-purple-600 rounded focus:ring-purple-500"
                            />
                          </label>
                        </div>
                      </div>

                      <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Thông báo đẩy</h3>
                        <div className="space-y-4">
                          <label className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">Thông báo trên thiết bị</p>
                              <p className="text-sm text-gray-600">Nhận thông báo đẩy trên trình duyệt</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={generalForm.preferences.pushNotifications}
                              onChange={(e) => setGeneralForm(prev => ({
                                ...prev,
                                preferences: { ...prev.preferences, pushNotifications: e.target.checked }
                              }))}
                              className="w-5 h-5 text-purple-600 rounded focus:ring-purple-500"
                            />
                          </label>
                        </div>
                      </div>

                      <div className="flex justify-end pt-6">
                        <button
                          onClick={handleGeneralSave}
                          disabled={loading}
                          className="flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          {loading ? 'Đang lưu...' : 'Lưu cài đặt'}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Privacy Settings */}
              {activeTab === 'privacy' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">Quyền riêng tư</h2>

                    <div className="space-y-6">
                      <div className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Hiển thị hồ sơ</h3>
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Ai có thể xem hồ sơ của bạn?</label>
                            <select
                              value={privacySettings.profileVisibility}
                              onChange={(e) => setPrivacySettings(prev => ({ ...prev, profileVisibility: e.target.value }))}
                              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                            >
                              <option value="public">🌍 Công khai - Mọi người</option>
                              <option value="friends">👥 Bạn bè</option>
                              <option value="private">🔒 Riêng tư - Chỉ mình tôi</option>
                            </select>
                          </div>

                          <label className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">Hiển thị email</p>
                              <p className="text-sm text-gray-600">Cho phép người khác xem email của bạn</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={privacySettings.showEmail}
                              onChange={(e) => setPrivacySettings(prev => ({ ...prev, showEmail: e.target.checked }))}
                              className="w-5 h-5 text-purple-600 rounded focus:ring-purple-500"
                            />
                          </label>

                          <label className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">Hiển thị số điện thoại</p>
                              <p className="text-sm text-gray-600">Cho phép người khác xem số điện thoại của bạn</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={privacySettings.showPhone}
                              onChange={(e) => setPrivacySettings(prev => ({ ...prev, showPhone: e.target.checked }))}
                              className="w-5 h-5 text-purple-600 rounded focus:ring-purple-500"
                            />
                          </label>
                        </div>
                      </div>

                      <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Tương tác</h3>
                        <div className="space-y-4">
                          <label className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">Cho phép nhắn tin</p>
                              <p className="text-sm text-gray-600">Người khác có thể gửi tin nhắn cho bạn</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={privacySettings.allowMessages}
                              onChange={(e) => setPrivacySettings(prev => ({ ...prev, allowMessages: e.target.checked }))}
                              className="w-5 h-5 text-purple-600 rounded focus:ring-purple-500"
                            />
                          </label>

                          <label className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">Cho phép kết bạn</p>
                              <p className="text-sm text-gray-600">Người khác có thể gửi lời mời kết bạn</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={privacySettings.allowFriendRequests}
                              onChange={(e) => setPrivacySettings(prev => ({ ...prev, allowFriendRequests: e.target.checked }))}
                              className="w-5 h-5 text-purple-600 rounded focus:ring-purple-500"
                            />
                          </label>
                        </div>
                      </div>

                      <div className="flex justify-end pt-6">
                        <button
                          disabled={loading}
                          className="flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Save className="w-4 h-4 mr-2" />
                          {loading ? 'Đang lưu...' : 'Lưu cài đặt'}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
