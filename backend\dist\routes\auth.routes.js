"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_model_1 = require("../models/user.model");
const notification_service_1 = require("../services/notification.service");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const express_validator_1 = require("express-validator");
const emailService_1 = require("../services/emailService");
const passport_1 = __importDefault(require("passport"));
const passport_google_oauth20_1 = require("passport-google-oauth20");
const mongoose_1 = require("mongoose");
const crypto_1 = __importDefault(require("crypto"));
const router = (0, express_1.Router)();
// Passport Google Strategy
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
    passport_1.default.use(new passport_google_oauth20_1.Strategy({
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL: 'http://localhost:5001/api/auth/google/callback'
    }, (accessToken, refreshToken, profile, done) => __awaiter(void 0, void 0, void 0, function* () {
        var _a, _b, _c, _d;
        try {
            console.log('Google Strategy callback triggered for profile:', profile.id, (_a = profile.emails) === null || _a === void 0 ? void 0 : _a[0].value);
            // Check if user exists by email
            let user = yield user_model_1.User.findOne({ email: (_b = profile.emails) === null || _b === void 0 ? void 0 : _b[0].value });
            if (user) {
                // Nếu user đã có, nhưng chưa có googleId, thì cập nhật googleId
                if (!user.googleId) {
                    user.googleId = profile.id;
                    yield user.save();
                }
                return done(null, user);
            }
            else {
                // Nếu chưa có user, tạo mới
                user = yield user_model_1.User.create({
                    email: (_c = profile.emails) === null || _c === void 0 ? void 0 : _c[0].value,
                    name: profile.displayName,
                    googleId: profile.id,
                    avatar: (_d = profile.photos) === null || _d === void 0 ? void 0 : _d[0].value,
                    password: crypto_1.default.randomBytes(32).toString('hex')
                });
                return done(null, user);
            }
        }
        catch (error) {
            return done(error);
        }
    })));
}
// Validation middleware
const validateRegistration = [
    (0, express_validator_1.body)('name').trim().notEmpty().withMessage('Tên không được để trống'),
    (0, express_validator_1.body)('email').isEmail().withMessage('Email không hợp lệ'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 6 })
        .withMessage('Mật khẩu phải có ít nhất 6 ký tự')
        .matches(/\d/)
        .withMessage('Mật khẩu phải chứa ít nhất một số'),
    (0, express_validator_1.body)('phone')
        .optional()
        .matches(/^[0-9]{10}$/)
        .withMessage('Số điện thoại không hợp lệ')
];
// Validation middleware for login
const validateLogin = [
    (0, express_validator_1.body)('email').isEmail().withMessage('Email không hợp lệ'),
    (0, express_validator_1.body)('password').notEmpty().withMessage('Mật khẩu không được để trống')
];
// Kiểm tra email đã tồn tại
const checkEmail = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = req.body;
        const user = yield user_model_1.User.findOne({ email });
        res.json({ available: !user });
    }
    catch (error) {
        console.error('Error checking email:', error);
        res.status(500).json({ message: 'Lỗi khi kiểm tra email' });
    }
});
// Gửi OTP về email
const otpStore = new Map(); // { email: { otp, expiresAt } }
const sendOtp = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = req.body;
        const user = yield user_model_1.User.findOne({ email });
        if (user) {
            res.status(400).json({ message: 'Email đã được sử dụng' });
            return;
        }
        const otp = yield (0, emailService_1.sendOTP)(email);
        res.json({ message: 'OTP đã được gửi' });
    }
    catch (error) {
        console.error('Error sending OTP:', error);
        res.status(500).json({ message: 'Lỗi khi gửi OTP' });
    }
});
// Xác thực OTP
const verifyOtp = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, otp } = req.body;
        const isValid = yield (0, emailService_1.verifyOTP)(email, otp);
        if (!isValid) {
            res.status(400).json({ message: 'OTP không hợp lệ' });
            return;
        }
        res.json({ message: 'Xác thực OTP thành công' });
    }
    catch (error) {
        console.error('Error verifying OTP:', error);
        res.status(500).json({ message: 'Lỗi khi xác thực OTP' });
    }
});
// Đăng ký tài khoản
const register = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ errors: errors.array() });
            return;
        }
        const { name, email, password, phone, role } = req.body;
        const existingUser = yield user_model_1.User.findOne({ email });
        if (existingUser) {
            res.status(400).json({ message: 'Email đã được sử dụng' });
            return;
        }
        const user = new user_model_1.User({
            name,
            email,
            password,
            phone,
            role: role || 'user' // Allow setting role, default to 'user'
        });
        yield user.save();
        const token = jsonwebtoken_1.default.sign({ _id: user._id }, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: '7d' });
        yield (0, notification_service_1.createNotification)({
            userId: user._id,
            type: 'system',
            title: 'Welcome',
            message: 'Welcome to our platform!'
        });
        res.status(201).json({
            message: 'Đăng ký thành công',
            token,
            user: {
                id: user._id,
                name: user.name,
                email: user.email,
                phone: user.phone
            }
        });
    }
    catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ message: 'Lỗi khi đăng ký' });
    }
});
// Đăng nhập
const login = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Dữ liệu không hợp lệ',
                errors: errors.array()
            });
        }
        const { email, password } = req.body;
        const user = yield user_model_1.User.findOne({ email }).select('+password');
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Email hoặc mật khẩu không đúng'
            });
        }
        const isMatch = yield user.comparePassword(password);
        if (!isMatch) {
            return res.status(401).json({
                success: false,
                message: 'Email hoặc mật khẩu không đúng'
            });
        }
        const token = jsonwebtoken_1.default.sign({ _id: user._id }, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: '7d' });
        // Send security alert for login
        try {
            const userAgent = req.get('User-Agent') || 'Unknown device';
            const ip = req.ip || req.connection.remoteAddress || 'Unknown IP';
            yield (0, notification_service_1.notifySecurityAlert)(user._id, 'login', `${userAgent} (IP: ${ip}) lúc ${new Date().toLocaleString('vi-VN')}`);
        }
        catch (notificationError) {
            console.error('Error sending login notification:', notificationError);
            // Don't fail login if notification fails
        }
        res.json({
            success: true,
            data: {
                token,
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    phone: user.phone,
                    avatar: user.avatar,
                    role: user.role,
                    preferences: user.preferences
                }
            }
        });
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi đăng nhập'
        });
    }
});
// Lấy thông tin user hiện tại
const getCurrentUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🔍 getCurrentUser called, authUserId:', req.authUserId);
        if (!req.authUserId) {
            console.log('❌ No authUserId found');
            return res.status(401).json({
                success: false,
                message: 'Không có quyền truy cập'
            });
        }
        console.log('🔍 Looking for user with ID:', req.authUserId);
        const user = yield user_model_1.User.findById(new mongoose_1.Types.ObjectId(req.authUserId)).select('-password');
        if (!user) {
            console.log('❌ User not found in database');
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy người dùng'
            });
        }
        console.log('✅ User found:', user.name, user.email);
        const responseData = {
            success: true,
            data: {
                id: user._id,
                name: user.name,
                email: user.email,
                phone: user.phone,
                avatar: user.avatar,
                role: user.role,
                preferences: user.preferences
            }
        };
        console.log('📤 Sending response:', responseData);
        res.json(responseData);
    }
    catch (error) {
        console.error('❌ Get user error:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi lấy thông tin người dùng'
        });
    }
});
// Cập nhật thông tin người dùng
const updateProfile = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.authUserId) {
            return res.status(401).json({ message: 'Không có quyền truy cập' });
        }
        const { name, phone, address, bio } = req.body;
        const user = yield user_model_1.User.findById(new mongoose_1.Types.ObjectId(req.authUserId));
        if (!user) {
            return res.status(404).json({ message: 'Không tìm thấy người dùng.' });
        }
        if (name)
            user.name = name;
        if (phone)
            user.phone = phone;
        if (address)
            user.address = address;
        if (bio)
            user.bio = bio;
        yield user.save();
        res.json({ message: 'Cập nhật thông tin thành công.', user });
    }
    catch (error) {
        next(error);
    }
});
// Cập nhật avatar
const updateAvatar = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.authUserId) {
            return res.status(401).json({ message: 'Không có quyền truy cập' });
        }
        const { avatar } = req.body;
        const user = yield user_model_1.User.findById(new mongoose_1.Types.ObjectId(req.authUserId));
        if (!user) {
            return res.status(404).json({ message: 'Không tìm thấy người dùng.' });
        }
        user.avatar = avatar;
        yield user.save();
        res.json({ message: 'Cập nhật avatar thành công.', user });
    }
    catch (error) {
        next(error);
    }
});
// Đổi mật khẩu
const changePassword = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.authUserId) {
            return res.status(401).json({ message: 'Không có quyền truy cập' });
        }
        const { currentPassword, newPassword } = req.body;
        const user = yield user_model_1.User.findById(new mongoose_1.Types.ObjectId(req.authUserId)).select('+password');
        if (!user) {
            return res.status(404).json({ message: 'Không tìm thấy người dùng.' });
        }
        const isMatch = yield user.comparePassword(currentPassword);
        if (!isMatch) {
            return res.status(400).json({ message: 'Mật khẩu hiện tại không đúng.' });
        }
        user.password = newPassword;
        yield user.save();
        res.json({ message: 'Đổi mật khẩu thành công.' });
    }
    catch (error) {
        next(error);
    }
});
// Cập nhật cài đặt người dùng
const updatePreferences = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.authUserId) {
            return res.status(401).json({ message: 'Không có quyền truy cập' });
        }
        const { theme, language, emailNotifications, pushNotifications } = req.body;
        const user = yield user_model_1.User.findById(new mongoose_1.Types.ObjectId(req.authUserId));
        if (!user) {
            return res.status(404).json({ message: 'Không tìm thấy người dùng.' });
        }
        if (theme)
            user.preferences.theme = theme;
        if (language)
            user.preferences.language = language;
        if (emailNotifications !== undefined)
            user.preferences.emailNotifications = emailNotifications;
        if (pushNotifications !== undefined)
            user.preferences.pushNotifications = pushNotifications;
        yield user.save();
        res.json({ message: 'Cập nhật cài đặt thành công.', preferences: user.preferences });
    }
    catch (error) {
        next(error);
    }
});
// Lấy danh sách thông báo
const getNotifications = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.authUserId;
        const notifications = yield (0, notification_service_1.getUnreadNotifications)(userId);
        res.json({ notifications });
    }
    catch (error) {
        next(error);
    }
});
// Đánh dấu thông báo đã đọc
const markNotificationRead = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { notificationId } = req.params;
        const userId = req.authUserId;
        const notification = yield (0, notification_service_1.markNotificationAsRead)(notificationId, userId);
        if (!notification) {
            res.status(404).json({ message: 'Không tìm thấy thông báo.' });
            return;
        }
        res.json({ message: 'Đã đánh dấu thông báo đã đọc.' });
    }
    catch (error) {
        next(error);
    }
});
// Đánh dấu tất cả thông báo đã đọc
const markAllNotificationsRead = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.authUserId;
        yield (0, notification_service_1.markAllNotificationsAsRead)(userId);
        res.json({ message: 'Đã đánh dấu tất cả thông báo đã đọc.' });
    }
    catch (error) {
        next(error);
    }
});
// Google OAuth routes
router.get('/google', passport_1.default.authenticate('google', { scope: ['profile', 'email'] }));
router.get('/google/callback', passport_1.default.authenticate('google', { session: false }), (req, res) => {
    try {
        console.log('Google callback route reached.');
        const user = req.user;
        console.log('User authenticated successfully in callback:', user.email);
        const token = jsonwebtoken_1.default.sign({ _id: user._id }, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: '7d' });
        // Redirect về FE, truyền token và user qua query string
        const redirectUrl = `http://localhost:5173/login?token=${token}&name=${encodeURIComponent(user.name)}&email=${encodeURIComponent(user.email)}`;
        console.log('Redirecting to frontend URL:', redirectUrl);
        res.redirect(redirectUrl);
    }
    catch (error) {
        console.error('Error in Google callback:', error);
        res.status(500).json({ message: 'Lỗi khi xử lý callback Google' });
    }
});
// Routes
router.post('/check-email', checkEmail);
router.post('/send-otp', sendOtp);
router.post('/verify-otp', verifyOtp);
router.post('/register', validateRegistration, register);
router.post('/login', validateLogin, login);
router.get('/me', auth_middleware_1.authMiddleware, getCurrentUser);
router.put('/profile', auth_middleware_1.authMiddleware, updateProfile);
router.put('/avatar', auth_middleware_1.authMiddleware, updateAvatar);
router.put('/change-password', auth_middleware_1.authMiddleware, changePassword);
router.put('/preferences', auth_middleware_1.authMiddleware, updatePreferences);
router.get('/notifications', auth_middleware_1.authMiddleware, getNotifications);
router.put('/notifications/:notificationId/read', auth_middleware_1.authMiddleware, markNotificationRead);
router.put('/notifications/read-all', auth_middleware_1.authMiddleware, markAllNotificationsRead);
exports.default = router;
