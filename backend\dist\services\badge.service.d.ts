import mongoose from 'mongoose';
import { IUserBadge } from '../models/UserBadge';
export interface BadgeEarnedResult {
    earned: boolean;
    badge?: IUserBadge;
    levelUp?: boolean;
    previousLevel?: number;
}
export declare const calculateDonationStats: (userId: mongoose.Types.ObjectId) => Promise<any>;
export declare const calculateEventStats: (userId: mongoose.Types.ObjectId) => Promise<any>;
export declare const calculatePostStats: (userId: mongoose.Types.ObjectId) => Promise<any>;
export declare const checkSupporterBadge: (userId: mongoose.Types.ObjectId) => Promise<BadgeEarnedResult>;
export declare const checkVolunteerBadge: (userId: mongoose.Types.ObjectId) => Promise<BadgeEarnedResult>;
export declare const getUserBadges: (userId: mongoose.Types.ObjectId) => Promise<{
    config: {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            2: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            3: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            4: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            5: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minEvents: number;
            };
            2: {
                name: string;
                minEvents: number;
            };
            3: {
                name: string;
                minEvents: number;
            };
            4: {
                name: string;
                minEvents: number;
            };
            5: {
                name: string;
                minEvents: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minOrganized: number;
            };
            2: {
                name: string;
                minOrganized: number;
            };
            3: {
                name: string;
                minOrganized: number;
            };
            4: {
                name: string;
                minOrganized: number;
            };
            5: {
                name: string;
                minOrganized: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minAmount: number;
            };
            2: {
                name: string;
                minAmount: number;
            };
            3: {
                name: string;
                minAmount: number;
            };
            4: {
                name: string;
                minAmount: number;
            };
            5: {
                name: string;
                minAmount: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minScore: number;
            };
            2: {
                name: string;
                minScore: number;
            };
            3: {
                name: string;
                minScore: number;
            };
            4: {
                name: string;
                minScore: number;
            };
            5: {
                name: string;
                minScore: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                special: boolean;
            };
            2: {
                name: string;
                special: boolean;
            };
            3: {
                name: string;
                special: boolean;
            };
            4: {
                name: string;
                special: boolean;
            };
            5: {
                name: string;
                special: boolean;
            };
        };
    };
    levelName: string;
    userId: mongoose.Types.ObjectId;
    badgeType: "supporter" | "volunteer" | "organizer" | "vip" | "champion" | "hero";
    badgeLevel: number;
    earnedAt: Date;
    totalDonations?: number;
    totalEvents?: number;
    totalPosts?: number;
    isActive: boolean;
    metadata?: {
        lastDonationAmount?: number;
        lastDonationDate?: Date;
        consecutiveMonths?: number;
        specialAchievements?: string[];
    };
    _id: unknown;
    $locals: Record<string, unknown>;
    $op: "save" | "validate" | "remove" | null;
    $where: Record<string, unknown>;
    baseModelName?: string;
    collection: mongoose.Collection;
    db: mongoose.Connection;
    errors?: mongoose.Error.ValidationError;
    id?: any;
    isNew: boolean;
    schema: mongoose.Schema;
    __v: number;
}[]>;
export declare const getUserPrimaryBadge: (userId: mongoose.Types.ObjectId) => Promise<{
    config: {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            2: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            3: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            4: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
            5: {
                name: string;
                minDonations: number;
                minAmount: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minEvents: number;
            };
            2: {
                name: string;
                minEvents: number;
            };
            3: {
                name: string;
                minEvents: number;
            };
            4: {
                name: string;
                minEvents: number;
            };
            5: {
                name: string;
                minEvents: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minOrganized: number;
            };
            2: {
                name: string;
                minOrganized: number;
            };
            3: {
                name: string;
                minOrganized: number;
            };
            4: {
                name: string;
                minOrganized: number;
            };
            5: {
                name: string;
                minOrganized: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minAmount: number;
            };
            2: {
                name: string;
                minAmount: number;
            };
            3: {
                name: string;
                minAmount: number;
            };
            4: {
                name: string;
                minAmount: number;
            };
            5: {
                name: string;
                minAmount: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                minScore: number;
            };
            2: {
                name: string;
                minScore: number;
            };
            3: {
                name: string;
                minScore: number;
            };
            4: {
                name: string;
                minScore: number;
            };
            5: {
                name: string;
                minScore: number;
            };
        };
    } | {
        name: string;
        icon: string;
        color: string;
        levels: {
            1: {
                name: string;
                special: boolean;
            };
            2: {
                name: string;
                special: boolean;
            };
            3: {
                name: string;
                special: boolean;
            };
            4: {
                name: string;
                special: boolean;
            };
            5: {
                name: string;
                special: boolean;
            };
        };
    };
    levelName: string;
    userId: mongoose.Types.ObjectId;
    badgeType: "supporter" | "volunteer" | "organizer" | "vip" | "champion" | "hero";
    badgeLevel: number;
    earnedAt: Date;
    totalDonations?: number;
    totalEvents?: number;
    totalPosts?: number;
    isActive: boolean;
    metadata?: {
        lastDonationAmount?: number;
        lastDonationDate?: Date;
        consecutiveMonths?: number;
        specialAchievements?: string[];
    };
    _id: unknown;
    $locals: Record<string, unknown>;
    $op: "save" | "validate" | "remove" | null;
    $where: Record<string, unknown>;
    baseModelName?: string;
    collection: mongoose.Collection;
    db: mongoose.Connection;
    errors?: mongoose.Error.ValidationError;
    id?: any;
    isNew: boolean;
    schema: mongoose.Schema;
    __v: number;
} | null>;
export declare const checkAllBadges: (userId: mongoose.Types.ObjectId) => Promise<BadgeEarnedResult[]>;
//# sourceMappingURL=badge.service.d.ts.map