"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserStats = void 0;
const Post_1 = require("../models/Post");
const Donation_1 = require("../models/Donation");
const EventRegistration_1 = __importDefault(require("../models/EventRegistration"));
const user_model_1 = require("../models/user.model");
const mongoose_1 = __importDefault(require("mongoose"));
// GET /users/:userId/stats - Lấy thống kê của user
const getUserStats = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        const { userId } = req.params;
        console.log('📊 Getting stats for user:', userId);
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            return res.status(400).json({
                success: false,
                message: 'ID người dùng không hợp lệ'
            });
        }
        // Check if user exists
        const user = yield user_model_1.User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy người dùng'
            });
        }
        // Parallel queries for better performance
        const [totalPosts, totalDonations, totalEvents, donationStats, postStats] = yield Promise.all([
            // Count total posts
            Post_1.Post.countDocuments({ user: userId }).catch(() => 0),
            // Count total donations
            Donation_1.Donation.countDocuments({ userId, status: 'success' }).catch(() => 0),
            // Count total events registered
            EventRegistration_1.default.countDocuments({ userId, status: 'registered' }).catch(() => 0),
            // Get donation amount stats
            Donation_1.Donation.aggregate([
                {
                    $match: {
                        userId: new mongoose_1.default.Types.ObjectId(userId),
                        status: 'success'
                    }
                },
                {
                    $group: {
                        _id: null,
                        totalAmount: { $sum: '$amount' }
                    }
                }
            ]).catch(() => []),
            // Get post engagement stats
            Post_1.Post.aggregate([
                {
                    $match: {
                        user: new mongoose_1.default.Types.ObjectId(userId)
                    }
                },
                {
                    $group: {
                        _id: null,
                        totalReactions: { $sum: { $size: '$reactions' } },
                        totalComments: { $sum: { $size: '$comments' } }
                    }
                }
            ]).catch(() => [])
        ]);
        const totalDonationAmount = ((_a = donationStats[0]) === null || _a === void 0 ? void 0 : _a.totalAmount) || 0;
        const totalReactions = ((_b = postStats[0]) === null || _b === void 0 ? void 0 : _b.totalReactions) || 0;
        const totalComments = ((_c = postStats[0]) === null || _c === void 0 ? void 0 : _c.totalComments) || 0;
        const stats = {
            totalPosts: totalPosts || 0,
            totalReactions: totalReactions || 0,
            totalComments: totalComments || 0,
            totalDonations: totalDonations || 0,
            totalEvents: totalEvents || 0,
            totalDonationAmount: totalDonationAmount || 0
        };
        console.log(`📊 User stats for ${userId}:`, stats);
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        console.error('❌ Error getting user stats:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi lấy thống kê người dùng',
            error: error.message
        });
    }
});
exports.getUserStats = getUserStats;
