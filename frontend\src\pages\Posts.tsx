import React, { useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '../contexts/AuthContext';
import { usePostsContext } from '../contexts/PostsContext';
import { useNavigate } from 'react-router-dom';
import PostForm from '../components/posts/PostForm';
import PostCard from '../components/posts/PostCard';
import ReportModal from '../components/posts/ReportModal';
import ShareModal from '../components/posts/ShareModal';
import ServerStatus from '../components/ServerStatus';
import api from '../services/api';
// import các component phụ trợ nếu cần
// import PostCard from '../components/posts/PostCard';
// import PostForm from '../components/posts/PostForm';
// import ReportModal from '../components/posts/ReportModal';
// import Notification from '../components/ui/Notification';

// TODO: import socket.io-client nếu dùng realtime
// import { io } from 'socket.io-client';

const API_URL = import.meta.env.VITE_API_URL || '';

const Posts: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const { posts, updatePost, loading: postsLoading, createPost, fetchPosts } = usePostsContext();
  const navigate = useNavigate();
  const [showReportModal, setShowReportModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [selectedPost, setSelectedPost] = useState<any>(null);



  // Auth check is now handled in render logic below





  // Đăng bài mới
  const handleCreatePost = async (postData: { content: string; media: File[] }) => {
    try {
      console.log('🚀 Đang đăng bài...', postData);
      console.log('👤 Current user:', user);
      console.log('🆔 User ID:', user?.id);

      // Convert files to base64 for storage
      const mediaUrls: string[] = [];
      if (postData.media.length > 0) {
        console.log('📸 Processing', postData.media.length, 'images...');
        for (const file of postData.media) {
          console.log('📸 Processing file:', file.name, 'size:', file.size);
          const reader = new FileReader();
          const base64 = await new Promise<string>((resolve) => {
            reader.onload = () => resolve(reader.result as string);
            reader.readAsDataURL(file);
          });
          mediaUrls.push(base64);
          console.log('✅ Converted to base64:', base64.substring(0, 50) + '...');
        }
      }

      console.log('📤 Sending post with', mediaUrls.length, 'media items');

      // Use API to create post
      await createPost(postData.content, mediaUrls);

    } catch (error: any) {
      console.error('❌ Lỗi đăng bài:', error);
      toast.error('Không thể đăng bài. Vui lòng thử lại.');
    }
  };

  // Thả cảm xúc
  const handleReact = async (postId: string, reaction: string) => {
    try {
      console.log('🎯 Reacting to post:', postId, 'with reaction:', reaction);

      const requestBody = {
        type: reaction || null
      };
      console.log('📤 Request body:', requestBody);
      console.log('📤 Request URL:', `${API_URL}/api/posts/${postId}/react`);

      // Call API first
      const response = await api.post(`/api/posts/${postId}/react`, requestBody);

      console.log('🔍 Full reaction response:', response.data);

      if (response.data.success) {
        console.log('✅ Reaction API success:', response.data);
        console.log('✅ User reaction returned:', response.data.userReaction);
        console.log('✅ Updated reactions:', response.data.reactions);

        // Update post state immediately with new reaction data
        updatePost(postId, {
          reactions: response.data.reactions,
          userReaction: response.data.userReaction
        });

        // Also refresh from server to ensure consistency
        setTimeout(() => {
          console.log('🔄 Refreshing posts after reaction...');
          fetchPosts();
        }, 500);

        // Broadcast to other tabs that reactions were updated
        localStorage.setItem('lastPostUpdate', Date.now().toString());

        // Also trigger immediate refresh for other tabs
        window.dispatchEvent(new CustomEvent('postsUpdated', {
          detail: { type: 'refresh', message: 'Reaction updated' }
        }));

        toast.success(reaction ? 'Đã thả cảm xúc!' : 'Đã bỏ cảm xúc!');
      } else {
        throw new Error(response.data.message || 'Failed to react');
      }

    } catch (error: any) {
      console.error('❌ Lỗi thả cảm xúc:', error);
      console.error('❌ Error details:', error.response?.data);
      console.error('❌ Validation errors:', error.response?.data?.errors);
      toast.error(`Không thể thả cảm xúc: ${error.response?.data?.message || error.message}`);
    }
  };

  // Báo cáo bài viết
  const handleReport = async (reason: string) => {
    if (!selectedPost) return;

    try {
      await api.post(`/api/posts/${selectedPost._id}/report`, { reason });

      setShowReportModal(false);
      setSelectedPost(null);
      toast.success('Đã báo cáo bài viết thành công');
    } catch (error: any) {
      console.error('Error reporting post:', error);
      if (error.response?.status === 400) {
        const message = error.response?.data?.message;
        if (message === 'Bạn đã báo cáo bài viết này rồi' || message === 'Already reported') {
          toast.error('Bạn đã báo cáo bài viết này rồi');
        } else {
          toast.error(message || 'Không thể báo cáo bài viết');
        }
      } else {
        toast.error('Không thể báo cáo bài viết');
      }
    }
  };

  // Bình luận bài viết
  const handleComment = async (postId: string, content: string) => {
    try {
      console.log('💬 Adding comment to post:', postId, 'content:', content);

      // Call API first
      const response = await api.post(`/api/posts/${postId}/comment`, {
        content
      });

      if (response.data.success) {
        console.log('✅ Comment API success:', response.data);

        // Refresh posts from server to get updated comments for all users
        setTimeout(() => {
          console.log('🔄 Refreshing posts after comment...');
          fetchPosts();
        }, 200);

        // Broadcast to other tabs that comments were updated
        localStorage.setItem('lastPostUpdate', Date.now().toString());

        // Also trigger immediate refresh for other tabs
        window.dispatchEvent(new CustomEvent('postsUpdated', {
          detail: { type: 'refresh', message: 'Comment added' }
        }));

        toast.success('Đã thêm bình luận!');
      } else {
        throw new Error(response.data.message || 'Failed to add comment');
      }

    } catch (error: any) {
      console.error('Error adding comment:', error);
      toast.error('Không thể thêm bình luận');
    }
  };

  // handleDeletePost function removed - not used in current implementation

  // TODO: Lắng nghe socket để nhận thông báo realtime khi có comment/cảm xúc mới

  // Show loading while checking auth
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang kiểm tra đăng nhập...</p>
        </div>
      </div>
    );
  }

  // Debug function to check auth status
  const debugAuth = () => {
    const token = localStorage.getItem('token');
    console.log('🔍 Debug Auth Status:');
    console.log('- Token in localStorage:', token ? 'Present' : 'Missing');
    console.log('- Token value:', token?.substring(0, 20) + '...');
    console.log('- User from context:', user);
    console.log('- Auth loading:', authLoading);
  };

  // Show login prompt if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Vui lòng đăng nhập để xem bài viết</p>
          <button
            onClick={() => navigate('/login')}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mr-4"
          >
            Đăng nhập
          </button>
          <button
            onClick={debugAuth}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Debug Auth
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <ServerStatus />
      <div className="max-w-2xl mx-auto py-8 px-4">



        {/* Post Form */}
        <PostForm onPost={handleCreatePost} />

        {/* Posts Feed */}
        <div className="space-y-6">
          {posts.length > 0 ? (
            posts.map(post => (
              <PostCard
                key={`${post._id}-${post.createdAt}`}
                post={post as any}
                onLike={handleReact}
                onComment={content => handleComment(post._id, content)}
                onShare={() => {
                  setSelectedPost(post);
                  setShowShareModal(true);
                }}
                onReport={() => {
                  setSelectedPost(post);
                  setShowReportModal(true);
                }}
                currentUserId={user?.id || 'current-user'}
                isAdmin={user?.role === 'admin'}
              />
            ))
          ) : (
            !postsLoading && (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">Chưa có bài viết nào</p>
                <p className="text-gray-400">Hãy tạo bài viết đầu tiên của bạn!</p>
              </div>
            )
          )}
        </div>

        {/* Loading */}
        {postsLoading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Đang tải bài viết...</p>
          </div>
        )}



        {/* Report Modal */}
        <ReportModal
          open={showReportModal}
          onClose={() => setShowReportModal(false)}
          onSubmit={handleReport}
        />



        {/* Share Modal */}
        <ShareModal
          isOpen={showShareModal}
          onClose={() => setShowShareModal(false)}
          postUrl={selectedPost ? `${window.location.origin}/posts/${selectedPost._id}` : ''}
          postTitle={selectedPost ? selectedPost.content.substring(0, 100) + '...' : ''}
        />
      </div>
    </div>
  );
};

export default Posts;