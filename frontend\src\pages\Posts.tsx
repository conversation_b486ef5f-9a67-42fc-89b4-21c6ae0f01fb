import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useAuth } from '../contexts/AuthContext';
import { usePostsContext } from '../contexts/PostsContext';
import { useNavigate } from 'react-router-dom';
import { io, Socket } from 'socket.io-client';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';
import PostForm from '../components/posts/PostForm';
import PostCard from '../components/posts/PostCard';
import ReportModal from '../components/posts/ReportModal';
import ShareModal from '../components/posts/ShareModal';
import ServerStatus from '../components/ServerStatus';
import api from '../services/api';
// import các component phụ trợ nếu cần
// import PostCard from '../components/posts/PostCard';
// import PostForm from '../components/posts/PostForm';
// import ReportModal from '../components/posts/ReportModal';
// import Notification from '../components/ui/Notification';

// TODO: import socket.io-client nếu dùng realtime
// import { io } from 'socket.io-client';

const Posts: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const { posts, updatePost, loading: postsLoading, createPost, fetchPosts, deletePost } = usePostsContext();
  const navigate = useNavigate();
  const [showReportModal, setShowReportModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [selectedPost, setSelectedPost] = useState<any>(null);
  const [socket, setSocket] = useState<Socket | null>(null);

  // Setup socket connection và listeners
  useEffect(() => {
    if (!user) return;

    console.log('🔌 Setting up socket connection...');
    const newSocket = io(API_URL, {
      auth: {
        token: localStorage.getItem('token')
      }
    });

    setSocket(newSocket);

    // Join user room for personal notifications
    newSocket.on('connect', () => {
      console.log('✅ Socket connected, joining user room...');
      newSocket.emit('join-user', user.id);

      // Also authenticate with token
      const token = localStorage.getItem('token');
      if (token) {
        newSocket.emit('authenticate', token);
      }
    });

    // Listen for room join confirmation
    newSocket.on('joined-room', (data) => {
      console.log('✅ Joined room:', data);
    });

    // Listen for authentication confirmation
    newSocket.on('authenticated', (data) => {
      console.log('✅ Socket authenticated:', data);
    });

    // Listen for new comments on posts
    newSocket.on('new-comment', (data) => {
      console.log('💬 Received new comment:', data);

      // Show toast notification if it's not the current user's comment
      if (data.comment?.user?._id !== user.id) {
        const commenterName = data.comment?.user?.name || 'Ai đó';
        toast.info(`${commenterName} đã bình luận về bài viết`, {
          description: data.comment?.content?.substring(0, 100) + (data.comment?.content?.length > 100 ? '...' : ''),
          duration: 5000,
        });
      }

      // Refresh posts to show new comment
      fetchPosts();
    });

    // Listen for new reactions
    newSocket.on('post-reaction', (data) => {
      console.log('❤️ Received new reaction:', data);

      // Show toast notification if it's not the current user's reaction
      if (data.userId !== user.id) {
        const reactionEmoji = data.type === 'like' ? '👍' :
                             data.type === 'love' ? '❤️' :
                             data.type === 'haha' ? '😂' :
                             data.type === 'sad' ? '😢' :
                             data.type === 'angry' ? '😡' :
                             data.type === 'wow' ? '😮' : '👍';

        toast.info(`Ai đó đã thả cảm xúc ${reactionEmoji}`, {
          duration: 3000,
        });
      }

      // Refresh posts to get updated reactions
      fetchPosts();
    });

    // Listen for post deletions
    newSocket.on('post-deleted', (data) => {
      console.log('🗑️ Post deleted:', data);

      // Remove post from local state
      deletePost(data.postId);

      // Show toast notification
      toast.info('Một bài viết đã bị xóa', {
        duration: 3000,
      });
    });

    // Listen for notifications
    newSocket.on('new_notification', (notification) => {
      console.log('🔔 Received notification:', notification);

      // Show toast for new notifications
      toast.info(notification.title, {
        description: notification.message,
        duration: 5000,
      });
    });

    // Cleanup on unmount
    return () => {
      console.log('🔌 Cleaning up socket connection...');
      newSocket.disconnect();
    };
  }, [user, API_URL, fetchPosts, updatePost]);

  // Auth check is now handled in render logic below





  // Đăng bài mới
  const handleCreatePost = async (postData: { content: string; media: File[] }) => {
    try {
      console.log('🚀 Đang đăng bài...', postData);
      console.log('👤 Current user:', user);
      console.log('🆔 User ID:', user?.id);

      // Convert files to base64 for storage
      const mediaUrls: string[] = [];
      if (postData.media.length > 0) {
        console.log('📸 Processing', postData.media.length, 'images...');
        for (const file of postData.media) {
          console.log('📸 Processing file:', file.name, 'size:', file.size);
          const reader = new FileReader();
          const base64 = await new Promise<string>((resolve) => {
            reader.onload = () => resolve(reader.result as string);
            reader.readAsDataURL(file);
          });
          mediaUrls.push(base64);
          console.log('✅ Converted to base64:', base64.substring(0, 50) + '...');
        }
      }

      console.log('📤 Sending post with', mediaUrls.length, 'media items');

      // Use API to create post
      await createPost(postData.content, mediaUrls);

    } catch (error: any) {
      console.error('❌ Lỗi đăng bài:', error);
      toast.error('Không thể đăng bài. Vui lòng thử lại.');
    }
  };

  // Thả cảm xúc
  const handleReact = async (postId: string, reaction: string) => {
    try {
      console.log('🎯 Reacting to post:', postId, 'with reaction:', reaction);

      const requestBody = {
        type: reaction || null
      };
      console.log('📤 Request body:', requestBody);
      console.log('📤 Request URL:', `${API_URL}/api/posts/${postId}/react`);

      // Call API first
      const response = await api.post(`/api/posts/${postId}/react`, requestBody);

      console.log('🔍 Full reaction response:', response.data);

      if (response.data.success) {
        console.log('✅ Reaction API success:', response.data);
        console.log('✅ User reaction returned:', response.data.userReaction);
        console.log('✅ Updated reactions:', response.data.reactions);

        // Update post state immediately with new reaction data
        updatePost(postId, {
          reactions: response.data.reactions,
          userReaction: response.data.userReaction
        });

        // Also refresh from server to ensure consistency
        setTimeout(() => {
          console.log('🔄 Refreshing posts after reaction...');
          fetchPosts();
        }, 500);

        // Broadcast to other tabs that reactions were updated
        localStorage.setItem('lastPostUpdate', Date.now().toString());

        // Also trigger immediate refresh for other tabs
        window.dispatchEvent(new CustomEvent('postsUpdated', {
          detail: { type: 'refresh', message: 'Reaction updated' }
        }));

        toast.success(reaction ? 'Đã thả cảm xúc!' : 'Đã bỏ cảm xúc!');
      } else {
        throw new Error(response.data.message || 'Failed to react');
      }

    } catch (error: any) {
      console.error('❌ Lỗi thả cảm xúc:', error);
      console.error('❌ Error details:', error.response?.data);
      console.error('❌ Validation errors:', error.response?.data?.errors);
      toast.error(`Không thể thả cảm xúc: ${error.response?.data?.message || error.message}`);
    }
  };

  // Báo cáo bài viết
  const handleReport = async (reason: string) => {
    if (!selectedPost) return;

    try {
      await api.post(`/api/posts/${selectedPost._id}/report`, { reason });

      setShowReportModal(false);
      setSelectedPost(null);
      toast.success('Đã báo cáo bài viết thành công');
    } catch (error: any) {
      console.error('Error reporting post:', error);
      if (error.response?.status === 400) {
        const message = error.response?.data?.message;
        if (message === 'Bạn đã báo cáo bài viết này rồi' || message === 'Already reported') {
          toast.error('Bạn đã báo cáo bài viết này rồi');
        } else {
          toast.error(message || 'Không thể báo cáo bài viết');
        }
      } else {
        toast.error('Không thể báo cáo bài viết');
      }
    }
  };

  // Bình luận bài viết
  const handleComment = async (postId: string, content: string) => {
    try {
      console.log('💬 Adding comment to post:', postId, 'content:', content);

      // Call API first
      const response = await api.post(`/api/posts/${postId}/comment`, {
        content
      });

      if (response.data.success) {
        console.log('✅ Comment API success:', response.data);

        // Refresh posts from server to get updated comments for all users
        setTimeout(() => {
          console.log('🔄 Refreshing posts after comment...');
          fetchPosts();
        }, 200);

        // Broadcast to other tabs that comments were updated
        localStorage.setItem('lastPostUpdate', Date.now().toString());

        // Also trigger immediate refresh for other tabs
        window.dispatchEvent(new CustomEvent('postsUpdated', {
          detail: { type: 'refresh', message: 'Comment added' }
        }));

        toast.success('Đã thêm bình luận!');
      } else {
        throw new Error(response.data.message || 'Failed to add comment');
      }

    } catch (error: any) {
      console.error('Error adding comment:', error);
      toast.error('Không thể thêm bình luận');
    }
  };

  // Xóa bài viết
  const handleDeletePost = async (postId: string) => {
    try {
      console.log('🗑️ Deleting post:', postId);

      const response = await api.delete(`/api/posts/${postId}`);

      if (response.data.success) {
        // Remove from local state immediately
        deletePost(postId);
        toast.success('Đã xóa bài viết thành công!');
      } else {
        throw new Error(response.data.message || 'Failed to delete post');
      }
    } catch (error: any) {
      console.error('❌ Error deleting post:', error);
      toast.error('Không thể xóa bài viết. Vui lòng thử lại.');
    }
  };

  // TODO: Lắng nghe socket để nhận thông báo realtime khi có comment/cảm xúc mới

  // Show loading while checking auth
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang kiểm tra đăng nhập...</p>
        </div>
      </div>
    );
  }

  // Debug function to check auth status
  const debugAuth = () => {
    const token = localStorage.getItem('token');
    console.log('🔍 Debug Auth Status:');
    console.log('- Token in localStorage:', token ? 'Present' : 'Missing');
    console.log('- Token value:', token?.substring(0, 20) + '...');
    console.log('- User from context:', user);
    console.log('- Auth loading:', authLoading);
  };

  // Show login prompt if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Vui lòng đăng nhập để xem bài viết</p>
          <button
            onClick={() => navigate('/login')}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mr-4"
          >
            Đăng nhập
          </button>
          <button
            onClick={debugAuth}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Debug Auth
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <ServerStatus />

      {/* Header Section */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-6xl mx-auto py-6 px-4">
          <div className="text-center">
            <div className="inline-flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Cộng đồng thiện nguyện
                </h1>
              </div>
            </div>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              Chia sẻ câu chuyện, lan tỏa yêu thương và kết nối với cộng đồng những người có tấm lòng hảo tâm
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto py-8 px-4">
        {/* Post Form */}
        <div className="mb-8">
          <PostForm onPost={handleCreatePost} />
        </div>

        {/* Posts Feed */}
        <div className="space-y-6">
          {posts.length > 0 ? (
            posts.map(post => (
              <PostCard
                key={`${post._id}-${post.createdAt}`}
                post={post as any}
                onLike={handleReact}
                onComment={content => handleComment(post._id, content)}
                onShare={() => {
                  setSelectedPost(post);
                  setShowShareModal(true);
                }}
                onReport={() => {
                  setSelectedPost(post);
                  setShowReportModal(true);
                }}
                onDelete={handleDeletePost}
                currentUserId={user?.id || 'current-user'}
                isAdmin={user?.role === 'admin'}
              />
            ))
          ) : (
            !postsLoading && (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">Chưa có bài viết nào</p>
                <p className="text-gray-400">Hãy tạo bài viết đầu tiên của bạn!</p>
              </div>
            )
          )}
        </div>

        {/* Loading */}
        {postsLoading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Đang tải bài viết...</p>
          </div>
        )}



        {/* Report Modal */}
        <ReportModal
          open={showReportModal}
          onClose={() => setShowReportModal(false)}
          onSubmit={handleReport}
        />



        {/* Share Modal */}
        <ShareModal
          isOpen={showShareModal}
          onClose={() => setShowShareModal(false)}
          postUrl={selectedPost ? `${window.location.origin}/posts/${selectedPost._id}` : ''}
          postTitle={selectedPost ? selectedPost.content.substring(0, 100) + '...' : ''}
        />
      </div>
    </div>
  );
};

export default Posts;