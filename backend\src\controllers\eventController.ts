import { Request, Response } from 'express';
import Event from '../models/Event';
import EventRegistration from '../models/EventRegistration';
import { User } from '../models/user.model';
import { notifyNewEvent } from '../services/notification.service';
import { uploadToCloudinary } from '../services/cloudinary';
import { sendEventRegistrationEmail } from '../services/eventEmailService';
import { generateCheckInQRCode, verifyCheckInQRCode, generateEventShareQRCode } from '../services/qrCodeService';
import mongoose from 'mongoose';

// L<PERSON>y danh sách sự kiện (public)
export const getEvents = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 12,
      status,
      search,
      sortBy = 'eventDate',
      sortOrder = 'asc'
    } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build filter
    const filter: any = {};
    
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const events = await Event.find(filter)
      .populate('createdBy', 'name email')
      .sort(sortOptions)
      .skip(skip)
      .limit(limitNum)
      .lean();

    const total = await Event.countDocuments(filter);

    res.json({
      success: true,
      data: events,
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(total / limitNum),
        totalEvents: total,
        hasNext: pageNum < Math.ceil(total / limitNum),
        hasPrev: pageNum > 1
      }
    });
  } catch (error: any) {
    console.error('Error fetching events:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải danh sách sự kiện',
      error: error.message
    });
  }
};

// Lấy chi tiết sự kiện
export const getEventById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const event = await Event.findById(id)
      .populate('createdBy', 'name email')
      .lean();

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy sự kiện'
      });
    }

    // Lấy danh sách người đăng ký (chỉ số lượng cho public)
    const registrationCount = await EventRegistration.countDocuments({
      eventId: event._id,
      status: 'registered'
    });

    res.json({
      success: true,
      event: {
        ...event,
        currentParticipants: registrationCount
      }
    });
  } catch (error: any) {
    console.error('Error fetching event:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải thông tin sự kiện',
      error: error.message
    });
  }
};

// Đăng ký tham gia sự kiện
export const registerForEvent = async (req: Request, res: Response) => {
  try {
    console.log('🎯 [REGISTER EVENT] Starting registration process');
    console.log('🎯 [REGISTER EVENT] Event ID:', req.params.id);
    console.log('🎯 [REGISTER EVENT] User from req:', req.user);
    console.log('🎯 [REGISTER EVENT] Request body:', req.body);

    const { id } = req.params;
    const userId = req.user?._id;

    console.log('🎯 [REGISTER EVENT] Extracted user ID:', userId);

    if (!userId) {
      console.log('❌ [REGISTER EVENT] No user ID found');
      return res.status(401).json({
        success: false,
        message: 'Vui lòng đăng nhập để đăng ký sự kiện'
      });
    }

    // Kiểm tra sự kiện tồn tại
    console.log('🔍 [REGISTER EVENT] Looking for event with ID:', id);
    const event = await Event.findById(id);
    if (!event) {
      console.log('❌ [REGISTER EVENT] Event not found');
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy sự kiện'
      });
    }
    console.log('✅ [REGISTER EVENT] Event found:', event.title);

    // Kiểm tra điều kiện đăng ký
    const now = new Date();
    console.log('⏰ [REGISTER EVENT] Current time:', now);
    console.log('⏰ [REGISTER EVENT] Registration deadline:', event.registrationDeadline);
    console.log('⏰ [REGISTER EVENT] Event status:', event.status);

    if (now >= event.registrationDeadline) {
      console.log('❌ [REGISTER EVENT] Registration deadline passed');
      return res.status(400).json({
        success: false,
        message: 'Đã hết hạn đăng ký cho sự kiện này'
      });
    }

    if (event.status !== 'upcoming') {
      console.log('❌ [REGISTER EVENT] Event status not upcoming:', event.status);
      return res.status(400).json({
        success: false,
        message: 'Sự kiện này không còn nhận đăng ký'
      });
    }

    // Kiểm tra số lượng
    const currentRegistrations = await EventRegistration.countDocuments({
      eventId: event._id,
      status: 'registered'
    });

    if (currentRegistrations >= event.maxParticipants) {
      return res.status(400).json({
        success: false,
        message: 'Sự kiện đã đủ số lượng người tham gia'
      });
    }

    // Kiểm tra đã đăng ký chưa (chỉ kiểm tra status 'registered')
    const existingRegistration = await EventRegistration.findOne({
      eventId: event._id,
      userId: userId,
      status: 'registered'
    });

    if (existingRegistration) {
      console.log('❌ [REGISTER EVENT] User already registered with status:', existingRegistration.status);
      return res.status(400).json({
        success: false,
        message: 'Bạn đã đăng ký sự kiện này rồi'
      });
    }

    // Kiểm tra xem có registration cũ bị hủy không
    const cancelledRegistration = await EventRegistration.findOne({
      eventId: event._id,
      userId: userId,
      status: 'cancelled'
    });

    if (cancelledRegistration) {
      console.log('🔄 [REGISTER EVENT] Found cancelled registration, will reactivate it');
      // Reactivate the cancelled registration instead of creating new one
      cancelledRegistration.status = 'registered';
      cancelledRegistration.registrationDate = new Date();
      await cancelledRegistration.save();

      // Cập nhật số lượng người tham gia
      await Event.findByIdAndUpdate(event._id, {
        $inc: { currentParticipants: 1 }
      });

      return res.status(201).json({
        success: true,
        message: 'Đăng ký sự kiện thành công',
        registration: {
          _id: cancelledRegistration._id,
          eventId: cancelledRegistration.eventId,
          userId: cancelledRegistration.userId,
          status: cancelledRegistration.status,
          registrationDate: cancelledRegistration.registrationDate,
          participantInfo: cancelledRegistration.participantInfo
        }
      });
    }

    // Get user info
    const userDoc = await User.findById(userId);
    if (!userDoc) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy thông tin người dùng'
      });
    }

    // Tạo đăng ký mới với thông tin từ user
    const registration = new EventRegistration({
      eventId: event._id,
      userId: userId,
      notes: '',
      participantInfo: {
        fullName: userDoc.name || userDoc.email || 'Người dùng',
        phone: userDoc.phone || '',
        email: userDoc.email || ''
      }
    });

    await registration.save();

    // Cập nhật số lượng người tham gia
    await Event.findByIdAndUpdate(event._id, {
      $inc: { currentParticipants: 1 }
    });

    // Gửi email xác nhận đăng ký (không bắt buộc)
    try {
      if (userDoc && process.env.SMTP_USER && process.env.SMTP_PASS) {
        await sendEventRegistrationEmail(userDoc, event);
        console.log('✅ Registration email sent successfully');
      } else {
        console.log('⚠️ Email service not configured, skipping email');
      }
    } catch (emailError) {
      console.error('❌ Failed to send registration email:', emailError);
      // Không throw error để không ảnh hưởng đến quá trình đăng ký
    }

    res.status(201).json({
      success: true,
      message: 'Đăng ký sự kiện thành công',
      registration: {
        _id: registration._id,
        eventId: registration.eventId,
        userId: registration.userId,
        status: registration.status,
        registrationDate: registration.registrationDate,
        participantInfo: registration.participantInfo
      }
    });
  } catch (error: any) {
    console.error('Error registering for event:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi đăng ký sự kiện',
      error: error.message
    });
  }
};

// Hủy đăng ký sự kiện
export const cancelEventRegistration = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Vui lòng đăng nhập'
      });
    }

    const registration = await EventRegistration.findOne({
      eventId: id,
      userId: userId,
      status: 'registered'
    });

    if (!registration) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy đăng ký của bạn cho sự kiện này'
      });
    }

    // Kiểm tra thời gian hủy (có thể hủy trước 24h)
    const event = await Event.findById(id);
    if (event) {
      const cancelDeadline = new Date(event.eventDate);
      cancelDeadline.setHours(cancelDeadline.getHours() - 24);
      
      if (new Date() >= cancelDeadline) {
        return res.status(400).json({
          success: false,
          message: 'Không thể hủy đăng ký trong vòng 24h trước sự kiện'
        });
      }
    }

    registration.status = 'cancelled';
    await registration.save();

    // Giảm số lượng người tham gia
    await Event.findByIdAndUpdate(id, {
      $inc: { currentParticipants: -1 }
    });

    res.json({
      success: true,
      message: 'Hủy đăng ký thành công'
    });
  } catch (error: any) {
    console.error('Error cancelling registration:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi hủy đăng ký',
      error: error.message
    });
  }
};

// Lấy danh sách sự kiện đã đăng ký của user
export const getUserRegistrations = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Vui lòng đăng nhập'
      });
    }

    const registrations = await EventRegistration.find({
      userId: userId,
      status: { $in: ['registered', 'attended'] }
    })
    .populate({
      path: 'eventId',
      select: 'title description eventDate location status images'
    })
    .sort({ registrationDate: -1 });

    res.json({
      success: true,
      registrations
    });
  } catch (error: any) {
    console.error('Error fetching user registrations:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải danh sách đăng ký',
      error: error.message
    });
  }
};

// ==================== ADMIN FUNCTIONS ====================

// Lấy danh sách đăng ký của một sự kiện (admin)
export const getEventRegistrations = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      page = 1,
      limit = 20,
      status,
      search
    } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build filter
    const filter: any = { eventId: id };

    if (status && status !== 'all') {
      filter.status = status;
    }

    // Build aggregation pipeline
    const pipeline: any[] = [
      { $match: filter },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $lookup: {
          from: 'events',
          localField: 'eventId',
          foreignField: '_id',
          as: 'event'
        }
      },
      { $unwind: '$event' }
    ];

    // Add search filter if provided
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { 'participantInfo.fullName': { $regex: search, $options: 'i' } },
            { 'participantInfo.email': { $regex: search, $options: 'i' } },
            { 'participantInfo.phone': { $regex: search, $options: 'i' } },
            { 'user.name': { $regex: search, $options: 'i' } },
            { 'user.email': { $regex: search, $options: 'i' } }
          ]
        }
      });
    }

    // Add sorting
    pipeline.push({ $sort: { registrationDate: -1 } });

    // Get total count
    const totalPipeline = [...pipeline, { $count: 'total' }];
    const totalResult = await EventRegistration.aggregate(totalPipeline);
    const total = totalResult[0]?.total || 0;

    // Add pagination
    pipeline.push({ $skip: skip }, { $limit: limitNum });

    // Execute aggregation
    const registrations = await EventRegistration.aggregate(pipeline);

    res.json({
      success: true,
      registrations,
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(total / limitNum),
        totalRegistrations: total,
        hasNext: pageNum < Math.ceil(total / limitNum),
        hasPrev: pageNum > 1
      }
    });
  } catch (error: any) {
    console.error('Error fetching event registrations:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải danh sách đăng ký',
      error: error.message
    });
  }
};

// Lấy tất cả sự kiện (admin)
export const getAllEventsAdmin = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build filter
    const filter: any = {};

    if (status && status !== 'all') {
      filter.status = status;
    }

    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sortOptions: any = {};
    sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const events = await Event.find(filter)
      .populate('createdBy', 'name email')
      .sort(sortOptions)
      .skip(skip)
      .limit(limitNum);

    // Lấy số lượng đăng ký cho mỗi sự kiện
    const eventsWithRegistrations = await Promise.all(
      events.map(async (event) => {
        const registrationCount = await EventRegistration.countDocuments({
          eventId: event._id,
          status: 'registered'
        });

        return {
          ...event.toObject(),
          currentParticipants: registrationCount
        };
      })
    );

    const total = await Event.countDocuments(filter);

    res.json({
      success: true,
      events: eventsWithRegistrations, // Keep for backward compatibility
      data: eventsWithRegistrations,   // Add for frontend consistency
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(total / limitNum),
        totalEvents: total,
        hasNext: pageNum < Math.ceil(total / limitNum),
        hasPrev: pageNum > 1
      }
    });
  } catch (error: any) {
    console.error('Error fetching events (admin):', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải danh sách sự kiện',
      error: error.message
    });
  }
};

// Tạo sự kiện mới (admin)
export const createEventAdmin = async (req: Request, res: Response) => {
  try {
    const {
      title,
      description,
      eventDate,
      registrationDeadline,
      location,
      maxParticipants,
      requirements,
      benefits
    } = req.body;

    const createdBy = req.user?._id;

    if (!createdBy) {
      return res.status(401).json({
        success: false,
        message: 'Vui lòng đăng nhập'
      });
    }

    // Use images from request body (empty array if no images)
    const imageUrls: string[] = req.body.images || [];
    console.log('🖼️ [CREATE EVENT] Images from request:', imageUrls.length);

    // Parse requirements and benefits safely
    let parsedRequirements: string[] = [];
    let parsedBenefits: string[] = [];

    try {
      parsedRequirements = requirements ? JSON.parse(requirements) : [];
    } catch (e) {
      console.warn('Failed to parse requirements:', e);
      parsedRequirements = [];
    }

    try {
      parsedBenefits = benefits ? JSON.parse(benefits) : [];
    } catch (e) {
      console.warn('Failed to parse benefits:', e);
      parsedBenefits = [];
    }

    const event = new Event({
      title,
      description,
      eventDate: new Date(eventDate),
      registrationDeadline: new Date(registrationDeadline),
      location,
      maxParticipants: parseInt(maxParticipants),
      requirements: parsedRequirements,
      benefits: parsedBenefits,
      images: imageUrls,
      createdBy
    });

    await event.save();

    // Send notification to all users about new event
    try {
      const allUsers = await User.find({ role: 'user' }).select('_id');
      const userIds = allUsers.map(user => user._id);

      if (userIds.length > 0) {
        await notifyNewEvent(userIds, event.title, new mongoose.Types.ObjectId(event._id));
        console.log('📧 [Event] Notification sent to', userIds.length, 'users');
      }
    } catch (notificationError) {
      console.error('❌ [Event] Error sending notification:', notificationError);
      // Don't fail the event creation if notification fails
    }

    res.status(201).json({
      success: true,
      message: 'Tạo sự kiện thành công',
      event
    });
  } catch (error: any) {
    console.error('Error creating event:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tạo sự kiện',
      error: error.message
    });
  }
};

// ==================== QR CODE FUNCTIONS ====================

// Tạo QR code cho check-in
export const generateUserQRCode = async (req: Request, res: Response) => {
  try {
    const { id } = req.params; // event ID
    const userId = req.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Vui lòng đăng nhập'
      });
    }

    // Kiểm tra đăng ký
    const registration = await EventRegistration.findOne({
      eventId: id,
      userId: userId,
      status: 'registered'
    });

    if (!registration) {
      return res.status(404).json({
        success: false,
        message: 'Bạn chưa đăng ký sự kiện này'
      });
    }

    // Tạo QR code
    const qrCode = await generateCheckInQRCode(id, userId, registration._id.toString());

    res.json({
      success: true,
      qrCode,
      registration: {
        id: registration._id,
        eventId: registration.eventId,
        status: registration.status,
        registrationDate: registration.registrationDate
      }
    });
  } catch (error: any) {
    console.error('Error generating QR code:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tạo QR code',
      error: error.message
    });
  }
};

// Check-in bằng QR code (admin)
export const checkInWithQRCode = async (req: Request, res: Response) => {
  try {
    const { qrToken } = req.body;

    if (!qrToken) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu QR code token'
      });
    }

    // Xác thực QR code
    const qrData = verifyCheckInQRCode(qrToken);
    if (!qrData) {
      return res.status(400).json({
        success: false,
        message: 'QR code không hợp lệ hoặc đã hết hạn'
      });
    }

    // Tìm đăng ký
    const registration = await EventRegistration.findById(qrData.registrationId)
      .populate('eventId', 'title eventDate location')
      .populate('userId', 'name email');

    if (!registration) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy đăng ký'
      });
    }

    if (registration.status === 'attended') {
      return res.status(400).json({
        success: false,
        message: 'Người dùng đã check-in trước đó',
        registration
      });
    }

    // Cập nhật trạng thái check-in
    registration.status = 'attended';
    registration.checkInTime = new Date();
    await registration.save();

    res.json({
      success: true,
      message: 'Check-in thành công',
      registration: {
        id: registration._id,
        user: registration.userId,
        event: registration.eventId,
        status: registration.status,
        checkInTime: registration.checkInTime,
        registrationDate: registration.registrationDate
      }
    });
  } catch (error: any) {
    console.error('Error checking in with QR code:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi check-in',
      error: error.message
    });
  }
};

// Tạo QR code chia sẻ sự kiện
export const generateShareQRCode = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Kiểm tra sự kiện tồn tại
    const event = await Event.findById(id);
    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy sự kiện'
      });
    }

    const qrCode = await generateEventShareQRCode(id);

    res.json({
      success: true,
      qrCode,
      shareUrl: `${process.env.FRONTEND_URL}/events/${id}`
    });
  } catch (error: any) {
    console.error('Error generating share QR code:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tạo QR code chia sẻ',
      error: error.message
    });
  }
};
