import { Request, Response } from 'express';
import { Post } from '../models/Post';
import { Comment } from '../models/Comment';
import { CommentReport } from '../models/CommentReport';
import { Report } from '../models/Report';
import { Donation } from '../models/Donation';
import { Campaign } from '../models/Campaign';
import { User } from '../models/user.model';
import mongoose from 'mongoose';
import {
  updateUserActivity,
  isUserCurrentlyOnline,
  getUserLastSeen,
  getOnlineUserIds,
  getOnlineUsersInfo
} from '../utils/activity-tracker';
import {
  getUserRank,
  getUserBadges,
  calculateActivityScore,
  getPointsToNextRank,
  getRankingStats
} from '../utils/user-ranking';

export const getAllPosts = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Query filters
    const search = req.query.search as string;
    const status = req.query.status as string; // 'all', 'reported', 'recent'

    // Build query
    const query: any = {};

    if (search) {
      query.$or = [
        { content: { $regex: search, $options: 'i' } },
        { 'user.name': { $regex: search, $options: 'i' } }
      ];
    }

    if (status === 'reported') {
      query.reports = { $exists: true, $not: { $size: 0 } };
    } else if (status === 'recent') {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      query.createdAt = { $gte: oneDayAgo };
    }

    console.log('🔍 [Admin] Query:', query);

    // Use populate instead of aggregation for better reliability
    const posts = await Post.find(query)
      .populate('user', 'name email avatar')
      .populate({
        path: 'reports',
        select: 'reason user createdAt status',
        populate: {
          path: 'user',
          select: 'name email'
        }
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    console.log('✅ [Admin] Found posts:', posts.length);

    // Transform posts to include report count and status
    const transformedPosts = posts.map(post => {
      const reports = post.reports || [];
      const pendingReports = reports.filter((r: any) => r.status !== 'resolved');

      return {
        ...post,
        reportCount: reports.length,
        pendingReportCount: pendingReports.length,
        hasReports: reports.length > 0,
        hasPendingReports: pendingReports.length > 0,
        reportDetails: reports,
        latestReport: reports.length > 0 ? reports[reports.length - 1] : null
      };
    });

    console.log('✅ [Admin] Sample post with reports:', transformedPosts[0]);
    console.log('✅ [Admin] Posts with reports:', transformedPosts.filter(p => p.hasReports).length);

    const total = await Post.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        posts: transformedPosts,
        pagination: {
          currentPage: page,
          totalPages,
          totalPosts: total,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (err: any) {
    console.error('Error fetching admin posts:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải danh sách bài viết',
      error: err.message
    });
  }
};

export const deletePost = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        success: false,
        message: 'ID bài viết không hợp lệ'
      });
    }

    const post = await Post.findById(id).populate('user', 'name email');
    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy bài viết'
      });
    }

    // Delete the post
    await Post.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Đã xóa bài viết thành công',
      data: {
        deletedPost: {
          _id: post._id,
          content: post.content.substring(0, 50) + '...',
          author: post.user?.name
        }
      }
    });
  } catch (err: any) {
    console.error('Error deleting post:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi xóa bài viết',
      error: err.message
    });
  }
};

export const getAllDonations = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const skip = (page - 1) * limit;

    const donations = await Donation.find()
      .populate('campaignId', 'title')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Transform data to match frontend interface
    const transformedDonations = donations.map(donation => ({
      _id: donation._id,
      name: donation.name,
      email: donation.email,
      amount: donation.amount,
      message: donation.message || '',
      paymentMethod: donation.paymentMethod.toLowerCase(),
      status: donation.status,
      transactionId: donation.transactionId,
      createdAt: donation.createdAt,
      campaign: {
        _id: donation.campaignId._id,
        title: donation.campaignId.title
      },
      isAnonymous: donation.isAnonymous || false
    }));

    const total = await Donation.countDocuments();

    res.json({
      success: true,
      donations: transformedDonations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err: any) {
    console.error('Error fetching donations:', err);
    res.status(500).json({ success: false, message: err.message });
  }
};

export const getAllCampaigns = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const skip = (page - 1) * limit;

    const campaigns = await Campaign.find()
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Campaign.countDocuments();

    res.json({
      success: true,
      campaigns: campaigns,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err: any) {
    console.error('Error fetching campaigns:', err);
    res.status(500).json({ success: false, message: err.message });
  }
};

export const deleteComment = async (req: Request, res: Response) => {
  try {
    const { postId, commentId } = req.params;

    const post = await Post.findById(postId);

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Bài viết không tồn tại'
      });
    }

    // Remove comment from post
    const commentIndex = post.comments.findIndex((comment: any) => comment._id.toString() === commentId);

    if (commentIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Bình luận không tồn tại'
      });
    }

    post.comments.splice(commentIndex, 1);
    await post.save();

    res.json({
      success: true,
      message: 'Đã xóa bình luận thành công'
    });
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi xóa bình luận'
    });
  }
};

export const resolveReport = async (req: Request, res: Response) => {
  try {
    const postId = req.params.id;
    const { action } = req.body; // 'dismiss' or 'remove'

    const post = await Post.findById(postId);

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Bài viết không tồn tại'
      });
    }

    if (action === 'remove') {
      // Delete the post
      await Post.findByIdAndDelete(postId);
      res.json({
        success: true,
        message: 'Đã xóa bài viết và xử lý báo cáo'
      });
    } else if (action === 'dismiss') {
      // Clear all reports
      post.reports = [];
      await post.save();
      res.json({
        success: true,
        message: 'Đã bỏ qua báo cáo'
      });
    } else {
      return res.status(400).json({
        success: false,
        message: 'Hành động không hợp lệ'
      });
    }
  } catch (error) {
    console.error('Error resolving report:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi xử lý báo cáo'
    });
  }
};

// GET /admin/comment-reports - Lấy danh sách báo cáo comment
export const getCommentReports = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status as string || 'pending'; // pending, resolved, dismissed

    const query: any = {};
    if (status !== 'all') {
      query.status = status;
    }

    const reports = await CommentReport.find(query)
      .populate('user', 'name email')
      .populate('comment', 'content')
      .populate('post', 'content')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await CommentReport.countDocuments(query);

    res.json({
      success: true,
      data: {
        reports,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalReports: total,
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      }
    });
  } catch (err: any) {
    console.error('Error fetching comment reports:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải danh sách báo cáo comment',
      error: err.message
    });
  }
};

// POST /admin/comment-reports/:reportId/resolve - Xử lý báo cáo comment
export const resolveCommentReport = async (req: Request, res: Response) => {
  try {
    const { reportId } = req.params;
    const { action } = req.body; // 'dismiss', 'remove_comment'
    const adminId = req.user?._id;

    console.log('🔍 [Admin] Resolving comment report:', { reportId, action, adminId });

    // Validate input
    if (!reportId || !action) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin reportId hoặc action'
      });
    }

    if (!['dismiss', 'remove_comment'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Action không hợp lệ. Chỉ chấp nhận dismiss hoặc remove_comment'
      });
    }

    const report = await CommentReport.findById(reportId)
      .populate('comment')
      .populate('post');

    console.log('📋 [Admin] Report found:', !!report);
    console.log('📋 [Admin] Report status:', report?.status);

    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy báo cáo'
      });
    }

    if (report.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: `Báo cáo đã được xử lý với trạng thái: ${report.status}`
      });
    }

    if (action === 'remove_comment') {
      console.log('🗑️ [Admin] Removing comment:', report.comment);

      try {
        // Kiểm tra xem comment có tồn tại không
        const commentToDelete = await Comment.findById(report.comment);
        console.log('📝 [Admin] Comment found:', !!commentToDelete);

        if (commentToDelete) {
          // Xóa comment và tất cả replies từ Comment collection
          const deleteResult = await Comment.deleteMany({
            $or: [
              { _id: report.comment },
              { parentComment: report.comment }
            ]
          });
          console.log('🗑️ [Admin] Deleted comments from collection:', deleteResult.deletedCount);

          // Cập nhật post để xóa comment khỏi danh sách
          const updateResult = await Post.updateOne(
            { comments: report.comment },
            { $pull: { comments: report.comment } }
          );
          console.log('📝 [Admin] Updated post:', updateResult.modifiedCount);
        } else {
          // Nếu không tìm thấy trong Comment collection, có thể là embedded comment
          console.log('⚠️ [Admin] Comment not found in Comment collection, checking embedded comments');

          // Tìm post chứa comment này
          const postWithComment = await Post.findOne({ 'comments._id': report.comment });
          if (postWithComment) {
            // Xóa embedded comment
            postWithComment.comments = postWithComment.comments.filter(
              (comment: any) => comment._id.toString() !== report.comment.toString()
            );
            await postWithComment.save();
            console.log('📝 [Admin] Removed embedded comment from post');
          }
        }

        // Cập nhật trạng thái báo cáo
        report.status = 'resolved';
        report.resolvedAt = new Date();
        report.resolvedBy = adminId;
        await report.save();

        console.log('✅ [Admin] Comment report resolved successfully');

        res.json({
          success: true,
          message: 'Đã xóa comment và xử lý báo cáo'
        });
      } catch (deleteError) {
        console.error('❌ [Admin] Error deleting comment:', deleteError);
        throw deleteError;
      }
    } else if (action === 'dismiss') {
      // Bỏ qua báo cáo
      report.status = 'dismissed';
      report.resolvedAt = new Date();
      report.resolvedBy = adminId;
      await report.save();

      res.json({
        success: true,
        message: 'Đã bỏ qua báo cáo'
      });
    } else {
      return res.status(400).json({
        success: false,
        message: 'Hành động không hợp lệ'
      });
    }
  } catch (error: any) {
    console.error('❌ [Admin] Error resolving comment report:', error);
    console.error('❌ [Admin] Error stack:', error.stack);

    // Provide more specific error messages
    let errorMessage = 'Lỗi server khi xử lý báo cáo comment';

    if (error.name === 'ValidationError') {
      errorMessage = 'Dữ liệu không hợp lệ';
    } else if (error.name === 'CastError') {
      errorMessage = 'ID không hợp lệ';
    } else if (error.code === 11000) {
      errorMessage = 'Dữ liệu bị trùng lặp';
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Test endpoint to debug comment report resolution
export const testCommentReportResolution = async (req: Request, res: Response) => {
  try {
    const { reportId } = req.params;

    console.log('🧪 [Test] Testing comment report resolution for:', reportId);

    // Check if report exists
    const report = await CommentReport.findById(reportId);
    console.log('📋 [Test] Report found:', !!report);
    console.log('📋 [Test] Report data:', report);

    if (!report) {
      return res.json({
        success: false,
        message: 'Report not found',
        data: { reportId }
      });
    }

    // Check if comment exists in Comment collection
    const comment = await Comment.findById(report.comment);
    console.log('💬 [Test] Comment found in Comment collection:', !!comment);
    console.log('💬 [Test] Comment data:', comment);

    // Check if comment exists in Post as embedded
    const postWithEmbeddedComment = await Post.findOne({ 'comments._id': report.comment });
    console.log('📝 [Test] Post with embedded comment found:', !!postWithEmbeddedComment);

    // Check if comment exists in Post.comments array (as ObjectId)
    const postWithCommentRef = await Post.findOne({ comments: report.comment });
    console.log('📝 [Test] Post with comment reference found:', !!postWithCommentRef);

    return res.json({
      success: true,
      data: {
        reportExists: !!report,
        commentInCollection: !!comment,
        postWithEmbeddedComment: !!postWithEmbeddedComment,
        postWithCommentRef: !!postWithCommentRef,
        reportData: report,
        commentData: comment
      }
    });
  } catch (error: any) {
    console.error('❌ [Test] Error in test endpoint:', error);
    return res.status(500).json({
      success: false,
      message: error.message,
      stack: error.stack
    });
  }
};

// GET /admin/posts/:postId/comments - Lấy danh sách comment của bài viết với báo cáo
export const getPostComments = async (req: Request, res: Response) => {
  try {
    const { postId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Lấy comments với thông tin báo cáo
    const comments = await Comment.find({ post: postId })
      .populate('user', 'name email avatar')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Lấy số lượng báo cáo cho mỗi comment
    const commentsWithReports = await Promise.all(
      comments.map(async (comment) => {
        const reportCount = await CommentReport.countDocuments({
          comment: comment._id,
          status: 'pending'
        });

        const reports = await CommentReport.find({
          comment: comment._id,
          status: 'pending'
        })
          .populate('user', 'name')
          .select('reason createdAt user')
          .sort({ createdAt: -1 });

        return {
          ...comment.toObject(),
          reportCount,
          reports
        };
      })
    );

    const total = await Comment.countDocuments({ post: postId });

    res.json({
      success: true,
      data: {
        comments: commentsWithReports,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalComments: total,
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      }
    });
  } catch (err: any) {
    console.error('Error fetching post comments:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải danh sách comment',
      error: err.message
    });
  }
};

// GET /admin/dashboard/stats - Lấy thống kê tổng quan cho dashboard
export const getDashboardStats = async (req: Request, res: Response) => {
  try {


    // Import Event model
    const Event = require('../models/Event').default;

    // Parallel queries for better performance
    const [
      totalUsers,
      totalPosts,
      totalCampaigns,
      totalEvents,
      totalDonationAmount,
      pendingReports,
      recentUsers,
      recentPosts,
      recentDonations
    ] = await Promise.all([
      User.countDocuments(),
      Post.countDocuments(),
      Campaign.countDocuments(),
      Event.countDocuments(),
      Donation.aggregate([
        { $match: { status: 'success' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]),
      Report.countDocuments(), // All post reports
      User.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }),
      Post.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }),
      Donation.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      })
    ]);

    // Simplified monthly data for charts (last 6 months)
    const monthlyData = [];
    const now = new Date();

    for (let i = 5; i >= 0; i--) {
      const month = now.getMonth() - i;
      const year = now.getFullYear();

      // Handle year rollover
      let targetMonth = month;
      let targetYear = year;
      if (month < 0) {
        targetMonth = 12 + month;
        targetYear = year - 1;
      }

      const startOfMonth = new Date(targetYear, targetMonth, 1);
      const endOfMonth = new Date(targetYear, targetMonth + 1, 0);

      console.log(`📅 Processing ${targetMonth + 1}/${targetYear}`);

      const [monthUsers, monthPosts, monthDonations, cumulativeUsers] = await Promise.all([
        User.countDocuments({
          createdAt: { $gte: startOfMonth, $lte: endOfMonth }
        }),
        Post.countDocuments({
          createdAt: { $gte: startOfMonth, $lte: endOfMonth }
        }),
        Donation.aggregate([
          {
            $match: {
              createdAt: { $gte: startOfMonth, $lte: endOfMonth },
              status: 'success'
            }
          },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]),
        User.countDocuments({
          createdAt: { $lte: endOfMonth }
        })
      ]);

      monthlyData.push({
        month: `T${targetMonth + 1}`,
        users: monthUsers,
        posts: monthPosts,
        donations: monthDonations[0]?.total || 0,
        cumulativeUsers: cumulativeUsers
      });
    }

    // Campaign status distribution
    const campaignStats = await Campaign.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const campaignData = [
      { name: 'Hoàn thành', value: 0, color: '#10B981' },
      { name: 'Đang diễn ra', value: 0, color: '#3B82F6' },
      { name: 'Sắp kết thúc', value: 0, color: '#F59E0B' },
      { name: 'Tạm dừng', value: 0, color: '#EF4444' }
    ];

    campaignStats.forEach(stat => {
      const index = campaignData.findIndex(item => {
        if (stat._id === 'completed') return item.name === 'Hoàn thành';
        if (stat._id === 'active') return item.name === 'Đang diễn ra';
        if (stat._id === 'ending_soon') return item.name === 'Sắp kết thúc';
        if (stat._id === 'paused') return item.name === 'Tạm dừng';
        return false;
      });
      if (index !== -1) {
        campaignData[index].value = stat.count;
      }
    });

    // Event status distribution
    const eventStats = await Event.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const eventData = [
      { name: 'Sắp diễn ra', value: 0 },
      { name: 'Đang diễn ra', value: 0 },
      { name: 'Đã hoàn thành', value: 0 },
      { name: 'Đã hủy', value: 0 }
    ];

    eventStats.forEach((stat: any) => {
      const index = eventData.findIndex(item => {
        if (stat._id === 'upcoming' || stat._id === 'pending' || stat._id === 'approved') return item.name === 'Sắp diễn ra';
        if (stat._id === 'ongoing') return item.name === 'Đang diễn ra';
        if (stat._id === 'completed') return item.name === 'Đã hoàn thành';
        if (stat._id === 'cancelled') return item.name === 'Đã hủy';
        return false;
      });
      if (index !== -1) {
        eventData[index].value = stat.count;
      }
    });


    // Recent activity
    const recentActivity = [
      {
        time: '2 phút trước',
        action: 'Người dùng mới đăng ký',
        user: 'Hệ thống',
        count: recentUsers
      },
      {
        time: '5 phút trước',
        action: 'Bài viết mới được đăng',
        user: 'Cộng đồng',
        count: recentPosts
      },
      {
        time: '10 phút trước',
        action: 'Quyên góp mới',
        user: 'Nhà hảo tâm',
        count: recentDonations
      }
    ];

    // Calculate monthly growth based on cumulative users
    let monthlyGrowth = 0;
    if (monthlyData.length >= 2) {
      const currentMonth = monthlyData[monthlyData.length - 1];
      const previousMonth = monthlyData[monthlyData.length - 2];

      if (previousMonth.cumulativeUsers > 0) {
        monthlyGrowth = ((currentMonth.cumulativeUsers - previousMonth.cumulativeUsers) / previousMonth.cumulativeUsers) * 100;
        monthlyGrowth = Math.round(monthlyGrowth * 10) / 10; // Round to 1 decimal place
      }
    }

    const stats = {
      totalUsers,
      totalPosts,
      totalEvents,
      totalDonations: totalDonationAmount[0]?.total || 0,
      totalCampaigns,
      activeReports: pendingReports,
      monthlyGrowth
    };



    res.json({
      success: true,
      data: {
        stats,
        monthlyData,
        campaignData,
        eventData,
        recentActivity
      }
    });
  } catch (err: any) {
    console.error('❌ [Admin] Error fetching dashboard stats:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải thống kê dashboard',
      error: err.message
    });
  }
};

// Test endpoint to get posts without authentication
export const getPostsTest = async (req: Request, res: Response) => {
  try {


    const posts = await Post.find()
      .populate('user', 'name email')
      .sort({ createdAt: -1 })
      .limit(20);

    console.log(`✅ [Admin Test] Found ${posts.length} posts`);

    res.json({
      success: true,
      message: 'Lấy danh sách bài viết thành công',
      data: {
        posts,
        total: posts.length
      }
    });

  } catch (error) {
    console.error('❌ [Admin Test] Error getting posts:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy danh sách bài viết',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Test endpoint to delete post without authentication
export const deletePostTest = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    console.log('🗑️ [Admin Test] Deleting post:', id);

    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        success: false,
        message: 'ID bài viết không hợp lệ'
      });
    }

    // Find the post first
    const post = await Post.findById(id).populate('user', 'name email');
    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy bài viết'
      });
    }

    // Delete the post
    await Post.findByIdAndDelete(id);

    console.log('✅ [Admin Test] Post deleted successfully');

    res.json({
      success: true,
      message: 'Đã xóa bài viết thành công (test)',
      data: {
        deletedPost: {
          _id: post._id,
          content: post.content.substring(0, 50) + '...',
          author: post.user?.name
        }
      }
    });

  } catch (error) {
    console.error('❌ [Admin Test] Error deleting post:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi xóa bài viết',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Activity tracking functions are now imported from utils/activity-tracker.ts

// Helper functions for user activity
const getLastSeenText = (lastActive?: Date, userId?: string) => {
  // Check real-time activity first
  if (userId && isUserCurrentlyOnline(userId)) {
    return 'Đang hoạt động';
  }

  // Check real-time last seen
  if (userId) {
    const realTimeLastSeen = getUserLastSeen(userId);
    if (realTimeLastSeen) {
      const now = new Date();
      const diffMinutes = (now.getTime() - realTimeLastSeen.getTime()) / (1000 * 60);

      if (diffMinutes < 1) return 'Vừa xem';
      if (diffMinutes < 5) return 'Vừa rời đi';
      if (diffMinutes < 60) return `${Math.floor(diffMinutes)} phút trước`;

      const diffHours = diffMinutes / 60;
      if (diffHours < 24) return `${Math.floor(diffHours)} giờ trước`;
    }
  }

  // Fallback to database lastActive
  if (!lastActive) return 'Chưa có thông tin';
  try {
    const now = new Date();
    const lastActiveDate = new Date(lastActive);
    const diffMinutes = (now.getTime() - lastActiveDate.getTime()) / (1000 * 60);

    if (diffMinutes < 60) return `${Math.floor(diffMinutes)} phút trước`;

    const diffHours = diffMinutes / 60;
    if (diffHours < 24) return `${Math.floor(diffHours)} giờ trước`;

    const diffDays = diffHours / 24;
    if (diffDays < 7) return `${Math.floor(diffDays)} ngày trước`;

    return formatDate(lastActive);
  } catch (error) {
    console.warn('Error getting last seen text:', error);
    return 'Chưa có thông tin';
  }
};

const formatDate = (dateString?: Date | string) => {
  if (!dateString) return 'Chưa có thông tin';
  try {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return 'Chưa có thông tin';
  }
};

// User Management Functions

// GET /admin/users - Lấy danh sách tất cả người dùng
export const getAllUsers = async (req: Request, res: Response) => {
  try {
    console.log('👥 [TypeScript Admin] ===== STARTING getAllUsers =====');

    // Validate and sanitize input parameters (same as professional server)
    const page = Math.max(1, parseInt(req.query.page as string) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 20));
    const search = (req.query.search as string || '').trim();
    const role = req.query.role as string || 'all';
    const status = req.query.status as string || 'all';

    console.log('👥 [TypeScript Admin] Parameters:', { page, limit, search, role, status });

    // Validate role parameter
    if (role !== 'all' && !['user', 'admin'].includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Tham số role không hợp lệ'
      });
    }

    // Validate status parameter
    if (status !== 'all' && !['active', 'inactive'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Tham số status không hợp lệ'
      });
    }

    // Build query (same as professional server)
    let query: any = {};

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    if (role !== 'all') {
      query.role = role;
    }

    if (status !== 'all') {
      query.isActive = status === 'active';
    }

    const skip = (page - 1) * limit;

    console.log('👥 [TypeScript Admin] Query:', query);
    console.log('👥 [TypeScript Admin] Skip:', skip, 'Limit:', limit);

    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const totalUsers = await User.countDocuments(query);
    const totalPages = Math.ceil(totalUsers / limit);



    // Add activity status and real statistics with real-time online tracking
    const usersWithActivity = await Promise.all(users.map(async (user: any) => {
      // Get real statistics for each user - Fix donation counting by email
      const [postCount, donationCount] = await Promise.all([
        Post.countDocuments({ user: user._id }),
        Donation.countDocuments({ email: user.email, status: 'success' })
      ]);

      // Calculate activity status
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const userId = user._id.toString();

      // Use the actual isActive field from database, fallback to true
      const isActiveFromDB = user.hasOwnProperty('isActive') ? user.isActive : true;
      const isRecentlyActive = user.updatedAt > thirtyDaysAgo;

      // Real-time online status
      const isOnline = isUserCurrentlyOnline(userId);
      const realTimeLastSeen = getUserLastSeen(userId);

      // Calculate comprehensive stats - Fix donation counting by email
      const donationCountByEmail = await Donation.countDocuments({
        email: user.email,
        status: 'success'
      });

      const stats = {
        totalPosts: postCount,
        totalDonations: donationCountByEmail,
        totalEventRegistrations: 0 // Will be implemented when EventRegistration is available
      };

      // Calculate activity score using standardized formula
      const activityScore = calculateActivityScore(stats);

      // Get user rank and badges
      const userRank = getUserRank(activityScore);
      const userBadges = getUserBadges({ ...stats, activityScore, isVerified: user.role === 'admin' || postCount > 5 });
      const pointsToNext = getPointsToNextRank(activityScore);

      return {
        ...user.toObject(),
        isActive: isActiveFromDB, // Use real database value
        isOnline: isOnline, // Real-time online status
        isRecentlyActive: isRecentlyActive, // Recently active (30 days)
        lastActive: realTimeLastSeen || user.updatedAt, // Real-time or database time
        joinDate: user.createdAt,
        lastSeenText: getLastSeenText(user.updatedAt, userId), // Pass userId for real-time check
        totalNotifications: user.notifications?.length || 0,
        unreadNotifications: user.notifications?.filter((n: any) => !n.read).length || 0,
        activityScore: activityScore, // Standardized activity score
        isVerified: user.role === 'admin' || postCount > 5, // Verified if admin or active user
        stats: stats,
        // New ranking system fields
        rank: userRank,
        badges: userBadges,
        pointsToNextRank: pointsToNext.needed,
        nextRank: pointsToNext.nextRank
      };
    }));

    console.log(`✅ [TypeScript Admin] Processed ${usersWithActivity.length} users with activity data`);

    // Get online users info for debugging
    const onlineInfo = getOnlineUsersInfo();
    console.log(`🟢 [Activity] Currently online: ${onlineInfo.count} users`);

    // Get ranking statistics
    const rankingStats = getRankingStats(usersWithActivity);

    res.json({
      success: true,
      data: {
        users: usersWithActivity,
        pagination: {
          currentPage: page,
          totalPages,
          totalUsers,
          hasNext: page < totalPages,
          hasPrev: page > 1,
          limit
        },
        onlineStats: {
          totalOnline: onlineInfo.count,
          onlineUserIds: getOnlineUserIds()
        },
        rankingStats: rankingStats
      },
      message: `Đã tải ${users.length} người dùng thành công`
    });

  } catch (err: any) {
    console.error('❌ [TypeScript Admin] Error fetching users:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải danh sách người dùng',
      error: err.message
    });
  }
};

// GET /admin/users/:id - Lấy thông tin chi tiết một người dùng
export const getUserById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    console.log(`👤 [Admin] Fetching user details for ID: ${id}`);

    const user = await User.findById(id).select('-password').lean();

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng'
      });
    }

    // Get additional statistics - Fix donation counting by email
    const [postCount, donationCount] = await Promise.all([
      Post.countDocuments({ user: id }),
      Donation.countDocuments({ email: (user as any).email, status: 'success' })
    ]);

    // Calculate activity status (same logic as getAllUsers)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

    const isActiveFromDB = (user as any).hasOwnProperty('isActive') ? (user as any).isActive : true;
    const isRecentlyActive = (user as any).updatedAt > thirtyDaysAgo;
    const isOnline = (user as any).updatedAt > fiveMinutesAgo;

    const userWithStats = {
      ...user,
      isActive: isActiveFromDB, // Use real database value
      isOnline: isOnline, // Online status (5 minutes)
      isRecentlyActive: isRecentlyActive, // Recently active (30 days)
      lastActive: (user as any).updatedAt,
      joinDate: (user as any).createdAt,
      lastSeenText: getLastSeenText((user as any).updatedAt),
      totalNotifications: (user as any).notifications?.length || 0,
      unreadNotifications: (user as any).notifications?.filter((n: any) => !n.read).length || 0,
      activityScore: postCount * 10 + donationCount * 20,
      isVerified: (user as any).role === 'admin' || postCount > 5,
      stats: {
        totalPosts: postCount,
        totalDonations: donationCount,
        totalEventRegistrations: 0 // Will be implemented when EventRegistration is available
      }
    };

    console.log(`✅ [Admin] User details loaded for: ${(user as any).name}`);

    res.json({
      success: true,
      data: userWithStats
    });
  } catch (err: any) {
    console.error('❌ [Admin] Error fetching user details:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải thông tin người dùng',
      error: err.message
    });
  }
};

// PUT /admin/users/:id/status - Cập nhật trạng thái người dùng
export const updateUserStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    console.log(`🔄 [Admin] Updating user status for ID: ${id}, active: ${isActive}`);

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng'
      });
    }

    // Update the isActive field properly using findByIdAndUpdate
    const updatedUser = await User.findByIdAndUpdate(
      id,
      { isActive },
      { new: true }
    ).select('-password');

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng sau khi cập nhật'
      });
    }

    console.log(`✅ [Admin] User status updated for: ${user.name}`);

    res.json({
      success: true,
      message: `Đã ${isActive ? 'kích hoạt' : 'vô hiệu hóa'} tài khoản người dùng`,
      data: {
        userId: id,
        isActive,
        updatedAt: user.updatedAt
      }
    });
  } catch (err: any) {
    console.error('❌ [Admin] Error updating user status:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi cập nhật trạng thái người dùng',
      error: err.message
    });
  }
};

// PUT /admin/users/:id/role - Cập nhật vai trò người dùng
export const updateUserRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    console.log(`🔄 [Admin] Updating user role for ID: ${id}, role: ${role}`);

    if (!['user', 'admin'].includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Vai trò không hợp lệ'
      });
    }

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng'
      });
    }

    user.role = role;
    user.updatedAt = new Date();
    await user.save();

    console.log(`✅ [Admin] User role updated for: ${user.name} to ${role}`);

    res.json({
      success: true,
      message: `Đã cập nhật vai trò người dùng thành ${role === 'admin' ? 'Quản trị viên' : 'Người dùng'}`,
      data: {
        userId: id,
        role,
        updatedAt: user.updatedAt
      }
    });
  } catch (err: any) {
    console.error('❌ [Admin] Error updating user role:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi cập nhật vai trò người dùng',
      error: err.message
    });
  }
};

// DELETE /admin/users/:id - Xóa người dùng
export const deleteUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    console.log(`🗑️ [Admin] Deleting user ID: ${id}`);

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng'
      });
    }

    // Prevent deleting admin users
    if (user.role === 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Không thể xóa tài khoản quản trị viên'
      });
    }

    const userName = user.name;
    await User.findByIdAndDelete(id);

    console.log(`✅ [Admin] User deleted: ${userName}`);

    res.json({
      success: true,
      message: `Đã xóa người dùng: ${userName}`,
      data: {
        deletedUserId: id,
        deletedUserName: userName
      }
    });
  } catch (err: any) {
    console.error('❌ [Admin] Error deleting user:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi xóa người dùng',
      error: err.message
    });
  }
};

// GET /admin/online-status - Lấy thông tin trạng thái online của tất cả users
export const getOnlineStatus = async (req: Request, res: Response) => {
  try {
    console.log('🟢 [Activity] Getting online status...');

    const onlineInfo = getOnlineUsersInfo();
    const onlineUserIds = getOnlineUserIds();

    // Get user details for online users
    const onlineUsers = await User.find({
      _id: { $in: onlineUserIds }
    }).select('name email role').lean();

    console.log(`🟢 [Activity] Online users: ${onlineInfo.count}`);
    console.log(`🟢 [Activity] Online user IDs: ${onlineUserIds.join(', ')}`);

    res.json({
      success: true,
      data: {
        totalOnline: onlineInfo.count,
        onlineUserIds,
        onlineUsers,
        detailedInfo: onlineInfo.users
      },
      message: `Hiện có ${onlineInfo.count} người dùng đang online`
    });
  } catch (err: any) {
    console.error('❌ [Activity] Error getting online status:', err);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy thông tin trạng thái online',
      error: err.message
    });
  }
};