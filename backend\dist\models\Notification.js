"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notification = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const notificationSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    type: {
        type: String,
        enum: ['donation', 'campaign', 'event', 'post', 'comment', 'system', 'report', 'admin'],
        required: true
    },
    title: {
        type: String,
        required: true
    },
    message: {
        type: String,
        required: true
    },
    data: {
        campaignId: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Campaign'
        },
        donationId: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Donation'
        },
        eventId: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Event'
        },
        postId: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Post'
        },
        commentId: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Comment'
        },
        reportId: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Report'
        },
        amount: Number,
        status: String,
        reason: String,
        actionType: {
            type: String,
            enum: ['created', 'updated', 'deleted', 'cancelled', 'completed', 'reminder']
        },
        relatedUserId: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'User'
        },
        relatedUserName: String
    },
    isRead: {
        type: Boolean,
        default: false
    },
    emailSent: {
        type: Boolean,
        default: false
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium'
    }
}, {
    timestamps: true
});
// Add indexes for better query performance
notificationSchema.index({ userId: 1, createdAt: -1 });
notificationSchema.index({ userId: 1, isRead: 1 });
// Check if model exists before creating
exports.Notification = mongoose_1.default.models.Notification || mongoose_1.default.model('Notification', notificationSchema);
