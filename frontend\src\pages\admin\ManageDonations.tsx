import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import {
  Download,
  Search,
  Filter,
  Calendar,
  DollarSign,
  Users,
  FileText,
  Eye,
  RefreshCw
} from 'lucide-react';
// import { jsPDF } from 'jspdf';
// import autoTable from 'jspdf-autotable';

const API_URL = import.meta.env.REACT_APP_API_URL || 'http://localhost:5001';

interface Donation {
  _id: string;
  name: string;
  email: string;
  amount: number;
  message?: string;
  paymentMethod: string;
  status: string;
  transactionId: string;
  createdAt: string;
  campaign: {
    _id: string;
    title: string;
  };
  isAnonymous: boolean;
}

interface DonationStats {
  totalAmount: number;
  totalDonations: number;
  totalDonors: number;
  averageAmount: number;
}

const ManageDonations: React.FC = () => {
  const [donations, setDonations] = useState<Donation[]>([]);
  const [filteredDonations, setFilteredDonations] = useState<Donation[]>([]);
  const [stats, setStats] = useState<DonationStats>({
    totalAmount: 0,
    totalDonations: 0,
    totalDonors: 0,
    averageAmount: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState('all');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [selectedCampaign, setSelectedCampaign] = useState('all');
  const [campaigns, setCampaigns] = useState<any[]>([]);

  useEffect(() => {
    fetchDonations();
    fetchCampaigns();
  }, []);

  useEffect(() => {
    filterDonations();
  }, [donations, searchTerm, statusFilter, paymentMethodFilter, dateRange, selectedCampaign]);

  const fetchDonations = async () => {
    try {
      setLoading(true);
      console.log('Fetching donations...');

      // Sử dụng endpoint donations public (tạm thời)
      const response = await axios.get(`${API_URL}/api/donations`);

      console.log('Donations response:', response.data);
      const donationsData = response.data.donations || response.data || [];
      console.log('Donations data sample:', donationsData.slice(0, 2));
      setDonations(donationsData);
      calculateStats(donationsData);
    } catch (error) {
      console.error('Error fetching donations:', error);
      toast.error('Không thể tải danh sách quyên góp');
    } finally {
      setLoading(false);
    }
  };

  const fetchCampaigns = async () => {
    try {
      // Sử dụng endpoint public campaigns
      const response = await axios.get(`${API_URL}/api/campaigns`);
      console.log('Campaigns response:', response.data);

      let campaignsData = response.data.campaigns || response.data || [];

      // Đảm bảo campaignsData là array
      if (!Array.isArray(campaignsData)) {
        console.warn('Campaigns data is not an array:', campaignsData);
        campaignsData = [];
      }

      console.log('Setting campaigns:', campaignsData.length, 'campaigns');
      setCampaigns(campaignsData);
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      setCampaigns([]); // Set empty array on error
    }
  };

  const calculateStats = (donationList: Donation[]) => {
    const successfulDonations = donationList.filter(d => d.status === 'success');
    const totalAmount = successfulDonations.reduce((sum, d) => sum + d.amount, 0);
    const totalDonations = successfulDonations.length;
    const uniqueDonors = new Set(successfulDonations.map(d => d.email)).size;
    const averageAmount = totalDonations > 0 ? totalAmount / totalDonations : 0;

    setStats({
      totalAmount,
      totalDonations,
      totalDonors: uniqueDonors,
      averageAmount
    });
  };

  const filterDonations = () => {
    let filtered = [...donations];
    console.log('Filtering donations:', {
      total: donations.length,
      searchTerm,
      statusFilter,
      paymentMethodFilter,
      selectedCampaign,
      dateRange
    });

    // Search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(donation =>
        donation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        donation.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        donation.transactionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        donation.campaign.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
      console.log('After search filter:', filtered.length);
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(donation => donation.status === statusFilter);
      console.log('After status filter:', filtered.length);
    }

    // Payment method filter
    if (paymentMethodFilter !== 'all') {
      filtered = filtered.filter(donation =>
        donation.paymentMethod.toLowerCase() === paymentMethodFilter.toLowerCase()
      );
      console.log('After payment method filter:', filtered.length);
    }

    // Campaign filter
    if (selectedCampaign !== 'all') {
      filtered = filtered.filter(donation => donation.campaign._id === selectedCampaign);
      console.log('After campaign filter:', filtered.length);
    }

    // Date range filter
    if (dateRange.start && dateRange.end) {
      const startDate = new Date(dateRange.start);
      const endDate = new Date(dateRange.end);
      endDate.setHours(23, 59, 59, 999); // Include full end date

      filtered = filtered.filter(donation => {
        const donationDate = new Date(donation.createdAt);
        return donationDate >= startDate && donationDate <= endDate;
      });
      console.log('After date range filter:', filtered.length);
    }

    console.log('Final filtered donations:', filtered.length);
    setFilteredDonations(filtered);
    calculateStats(filtered);
  };

  const exportToCSV = () => {
    // Create CSV content
    const headers = ['STT', 'Tên', 'Email', 'Số tiền (VNĐ)', 'Phương thức', 'Trạng thái', 'Thời gian', 'Chiến dịch', 'Mã GD'];

    const csvContent = [
      // Header info
      ['BÁO CÁO QUYÊN GÓP'],
      [`Ngày xuất: ${format(new Date(), 'dd/MM/yyyy HH:mm', { locale: vi })}`],
      [`Tổng số quyên góp: ${stats.totalDonations}`],
      [`Tổng số tiền: ${stats.totalAmount.toLocaleString('vi-VN')} VNĐ`],
      [`Số người quyên góp: ${stats.totalDonors}`],
      [`Trung bình mỗi lần: ${stats.averageAmount.toLocaleString('vi-VN')} VNĐ`],
      [], // Empty row
      headers, // Column headers
      // Data rows
      ...filteredDonations.map((donation, index) => [
        index + 1,
        donation.isAnonymous ? 'Ẩn danh' : donation.name,
        donation.email,
        donation.amount.toLocaleString('vi-VN'),
        donation.paymentMethod.toUpperCase(),
        donation.status === 'success' ? 'Thành công' : 'Thất bại',
        format(new Date(donation.createdAt), 'dd/MM/yyyy HH:mm', { locale: vi }),
        donation.campaign.title,
        donation.transactionId
      ])
    ].map(row => row.join(',')).join('\n');

    // Create and download file
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `bao-cao-quyen-gop-${format(new Date(), 'dd-MM-yyyy')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Đã xuất báo cáo CSV thành công!');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'momo': return 'bg-pink-100 text-pink-800';
      case 'payos': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="animate-spin h-8 w-8 text-blue-500" />
        <span className="ml-2 text-gray-600">Đang tải dữ liệu...</span>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Quản lý Quyên góp</h1>
        <p className="text-gray-600">Theo dõi và quản lý tất cả các khoản quyên góp</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Tổng tiền</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalAmount.toLocaleString('vi-VN')} ₫</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <FileText className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Số lượt quyên góp</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalDonations}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Số người quyên góp</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalDonors}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Calendar className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Trung bình/lần</p>
              <p className="text-2xl font-bold text-gray-900">{stats.averageAmount.toLocaleString('vi-VN')} ₫</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              id="donations-search"
              name="search"
              type="text"
              placeholder="Tìm kiếm theo tên, email, mã GD, chiến dịch..."
              value={searchTerm}
              onChange={(e) => {
                console.log('Search term changed to:', e.target.value);
                setSearchTerm(e.target.value);
              }}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full"
              autoComplete="off"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => {
              console.log('Status filter changed to:', e.target.value);
              setStatusFilter(e.target.value);
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tất cả trạng thái</option>
            <option value="success">Thành công</option>
            <option value="pending">Đang xử lý</option>
            <option value="failed">Thất bại</option>
            <option value="cancelled">Đã hủy</option>
          </select>

          {/* Payment Method Filter */}
          <select
            value={paymentMethodFilter}
            onChange={(e) => {
              console.log('Payment method filter changed to:', e.target.value);
              setPaymentMethodFilter(e.target.value);
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tất cả phương thức</option>
            <option value="momo">MoMo</option>
            <option value="payos">PayOS</option>
            <option value="MOMO">MOMO</option>
            <option value="PAYOS">PAYOS</option>
          </select>

          {/* Campaign Filter */}
          <select
            id="donations-campaign-filter"
            name="campaignFilter"
            value={selectedCampaign}
            onChange={(e) => {
              console.log('Campaign filter changed to:', e.target.value);
              setSelectedCampaign(e.target.value);
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tất cả chiến dịch</option>
            {Array.isArray(campaigns) && campaigns.map(campaign => (
              <option key={campaign._id} value={campaign._id}>{campaign.title}</option>
            ))}
          </select>

          {/* Date Range */}
          <input
            id="donations-start-date"
            name="startDate"
            type="date"
            placeholder="Từ ngày"
            value={dateRange.start}
            onChange={(e) => {
              console.log('Start date changed to:', e.target.value);
              setDateRange(prev => ({ ...prev, start: e.target.value }));
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />

          <input
            id="donations-end-date"
            name="endDate"
            type="date"
            placeholder="Đến ngày"
            value={dateRange.end}
            onChange={(e) => {
              console.log('End date changed to:', e.target.value);
              setDateRange(prev => ({ ...prev, end: e.target.value }));
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-600">
              Hiển thị {filteredDonations.length} / {donations.length} quyên góp
            </div>
            <button
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setPaymentMethodFilter('all');
                setSelectedCampaign('all');
                setDateRange({ start: '', end: '' });
                console.log('Filters reset');
              }}
              className="flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Đặt lại bộ lọc
            </button>
          </div>
          <button
            onClick={exportToCSV}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Download className="h-4 w-4 mr-2" />
            Xuất CSV
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STT</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Người quyên góp</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số tiền</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phương thức</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chiến dịch</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã GD</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredDonations.map((donation, index) => (
                <tr key={donation._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{index + 1}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {donation.isAnonymous ? 'Ẩn danh' : donation.name}
                      </div>
                      <div className="text-sm text-gray-500">{donation.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {donation.amount.toLocaleString('vi-VN')} ₫
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPaymentMethodColor(donation.paymentMethod)}`}>
                      {donation.paymentMethod.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(donation.status)}`}>
                      {donation.status === 'success' ? 'Thành công' : donation.status === 'pending' ? 'Đang xử lý' : 'Thất bại'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {format(new Date(donation.createdAt), 'dd/MM/yyyy HH:mm', { locale: vi })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 max-w-xs truncate">
                    {donation.campaign.title}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                    {donation.transactionId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      Chi tiết
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredDonations.length === 0 && (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Không có dữ liệu</h3>
            <p className="mt-1 text-sm text-gray-500">Không tìm thấy quyên góp nào phù hợp với bộ lọc.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManageDonations;
