"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDonationsByCampaignId = exports.getDonationStatus = exports.handleMomoReturn = exports.handleMomoIPN = exports.handlePayOSReturn = exports.createDonation = void 0;
const Donation_1 = require("../models/Donation");
const Campaign_1 = require("../models/Campaign");
const payosService_1 = require("../services/payosService");
const momoService_1 = require("../services/momoService");
const notification_service_1 = require("../services/notification.service");
const mongoose_1 = __importDefault(require("mongoose"));
const index_1 = require("../index");
const idGenerator_1 = require("../utils/idGenerator");
// Lazy initialization to ensure environment variables are loaded
let payosService;
let momoService;
const getPayOSService = () => {
    if (!payosService) {
        payosService = new payosService_1.PayOSService();
    }
    return payosService;
};
const getMomoService = () => {
    if (!momoService) {
        momoService = new momoService_1.MomoService();
    }
    return momoService;
};
// Helper function to check if value is ObjectId
const isObjectId = (value) => {
    return value instanceof mongoose_1.default.Types.ObjectId;
};
const createDonation = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { campaignId, amount, name, email, phone, address, message, paymentMethod, isAnonymous } = req.body;
        // Validate required fields
        if (!campaignId || !amount || !name || !email) {
            return res.status(400).json({ message: 'Missing required fields' });
        }
        // Validate minimum amount
        if (amount < 1000) {
            return res.status(400).json({ message: 'Minimum donation amount is 1,000 VNĐ' });
        }
        // Check if campaign exists and is active
        const campaign = yield Campaign_1.Campaign.findById(campaignId);
        if (!campaign) {
            return res.status(404).json({ message: 'Campaign not found' });
        }
        if (campaign.status !== 'active') {
            return res.status(400).json({ message: 'Campaign is not active' });
        }
        // Generate transaction ID
        const transactionId = (0, idGenerator_1.generateTransactionId)();
        // Get user ID if authenticated
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        // Create donation record
        const donation = new Donation_1.Donation({
            userId: userId || undefined, // Save userId if user is logged in
            campaignId,
            amount,
            name,
            email,
            phone: phone || undefined,
            address,
            message,
            paymentMethod,
            isAnonymous,
            status: 'pending',
            transactionId
        });
        yield donation.save();
        // Create payment URL based on payment method
        try {
            let paymentUrl;
            if (paymentMethod === 'MOMO') {
                console.log('Creating MoMo payment URL for donation:', donation._id);
                paymentUrl = yield getMomoService().createPaymentUrl(donation);
                console.log('MoMo payment URL created:', paymentUrl);
            }
            else if (paymentMethod === 'PAYOS') {
                console.log('Creating PayOS payment URL for donation:', donation._id);
                const payosResponse = yield getPayOSService().createPaymentUrl(donation);
                if (!payosResponse.success) {
                    throw new Error(payosResponse.message);
                }
                paymentUrl = payosResponse.paymentUrl;
                console.log('PayOS payment URL created:', paymentUrl);
            }
            else {
                return res.status(400).json({ message: 'Invalid payment method' });
            }
            if (!paymentUrl) {
                throw new Error('Failed to create payment URL');
            }
            return res.json({ paymentUrl });
        }
        catch (paymentError) {
            console.error('Payment error:', paymentError);
            // Update donation status to failed
            donation.status = 'failed';
            donation.error = paymentError.message;
            yield donation.save();
            return res.status(500).json({
                message: 'Failed to create payment URL',
                error: paymentError.message
            });
        }
    }
    catch (error) {
        console.error('Error creating donation:', error);
        res.status(500).json({
            message: 'Failed to create donation',
            error: error.message
        });
    }
});
exports.createDonation = createDonation;
const handlePayOSReturn = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const session = yield mongoose_1.default.startSession();
    try {
        console.log('=== PayOS Return Handler Called ===');
        console.log('[PayOS Return] Timestamp:', new Date().toISOString());
        console.log('[PayOS Return] Query params:', req.query);
        console.log('[PayOS Return] Body:', req.body);
        console.log('[PayOS Return] Headers:', req.headers);
        // PayOS sends data via query parameters, not body
        const callbackData = {
            orderCode: req.query.orderCode || req.body.orderCode,
            code: req.query.code || req.body.code,
            id: req.query.id || req.body.id,
            cancel: req.query.cancel || req.body.cancel,
            status: req.query.status || req.body.status
        };
        let result;
        yield session.withTransaction(() => __awaiter(void 0, void 0, void 0, function* () {
            result = yield getPayOSService().handlePaymentCallback(callbackData);
            if (result.success && 'orderCode' in result) {
                const donation = yield Donation_1.Donation.findOne({ transactionId: result.orderCode }).session(session);
                if (!donation) {
                    console.error('[PayOS] Donation not found', result.orderCode);
                    throw new Error('Donation not found');
                }
                // Update donation status
                donation.status = 'success';
                donation.paymentTime = new Date();
                console.log('[PayOS] Saving donation...');
                yield donation.save({ session });
                console.log('[PayOS] Donation updated', donation);
                // Update campaign using findByIdAndUpdate to avoid validation issues
                const campaign = yield Campaign_1.Campaign.findById(donation.campaignId).session(session);
                if (!campaign) {
                    console.error('[PayOS] Campaign not found', donation.campaignId);
                    throw new Error('Campaign not found');
                }
                // Cập nhật số liệu campaign
                const oldAmount = campaign.currentAmount || 0;
                const newAmount = oldAmount + donation.amount;
                const newTotalDonations = (campaign.totalDonations || 0) + 1;
                // Mỗi lần thanh toán thành công sẽ tăng 1 lượt ủng hộ (không kiểm tra email trùng)
                const newTotalDonors = (campaign.totalDonors || 0) + 1;
                // Tạo donor object mới
                const newDonor = {
                    transactionId: donation.transactionId,
                    amount: donation.amount,
                    name: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name,
                    email: donation.email,
                    phone: donation.phone,
                    address: donation.address,
                    isAnonymous: donation.isAnonymous,
                    paymentMethod: donation.paymentMethod,
                    status: 'success',
                    createdAt: new Date()
                };
                // Cập nhật tiến độ
                const newProgress = Math.min(100, (newAmount / campaign.targetAmount) * 100);
                console.log('[PayOS] Campaign update details:', {
                    oldAmount,
                    newAmount,
                    oldProgress: (oldAmount / campaign.targetAmount) * 100,
                    newProgress,
                    totalDonations: newTotalDonations,
                    totalDonors: newTotalDonors
                });
                // Update campaign using updateOne to avoid validation issues
                yield Campaign_1.Campaign.updateOne({ _id: donation.campaignId }, {
                    $inc: {
                        currentAmount: donation.amount,
                        totalDonations: 1,
                        totalDonors: 1
                    },
                    $push: {
                        donors: newDonor
                    },
                    $set: {
                        progress: newProgress
                    }
                }, {
                    session,
                    runValidators: false // Skip validation for partial updates
                });
                // Get updated campaign data for socket event
                const updatedCampaign = yield Campaign_1.Campaign.findById(donation.campaignId).session(session);
                console.log('[PayOS] Campaign updated successfully:', {
                    campaignId: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign._id,
                    currentAmount: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign.currentAmount,
                    totalDonations: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign.totalDonations,
                    totalDonors: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign.totalDonors,
                    progress: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign.progress
                });
                // Emit socket event với đầy đủ thông tin
                if (updatedCampaign) {
                    index_1.io.emit('campaign_updated', {
                        campaignId: updatedCampaign._id.toString(),
                        currentAmount: updatedCampaign.currentAmount,
                        totalDonations: updatedCampaign.totalDonations,
                        totalDonors: updatedCampaign.totalDonors,
                        progress: updatedCampaign.progress,
                        lastDonation: {
                            amount: donation.amount,
                            donorName: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name,
                            paymentMethod: donation.paymentMethod,
                            createdAt: new Date(),
                            transactionId: donation.transactionId
                        }
                    });
                }
                // Send thank you notification
                try {
                    yield (0, notification_service_1.notifyDonationSuccess)(donation.email, donation.name, donation.amount, campaign.title, campaign._id, donation.transactionId, donation.isAnonymous, donation.userId);
                    console.log('📧 [PayOS] Thank you notification sent successfully');
                }
                catch (notificationError) {
                    console.error('❌ [PayOS] Error sending thank you notification:', notificationError);
                    // Don't fail the payment processing if notification fails
                }
                // Check and award badges for logged-in users
                if (donation.userId) {
                    try {
                        const { checkSupporterBadge } = yield Promise.resolve().then(() => __importStar(require('../services/badge.service')));
                        const badgeResult = yield checkSupporterBadge(donation.userId);
                        if (badgeResult.earned && badgeResult.levelUp) {
                            console.log('🏆 [PayOS] Badge awarded/upgraded for user:', donation.userId);
                        }
                    }
                    catch (badgeError) {
                        console.error('❌ [PayOS] Error checking badges:', badgeError);
                        // Don't fail the payment processing if badge check fails
                    }
                }
                // Check for milestone achievements
                try {
                    const oldProgress = (oldAmount / campaign.targetAmount) * 100;
                    const newProgress = (newAmount / campaign.targetAmount) * 100;
                    // Check if we crossed any milestone (25%, 50%, 75%, 100%)
                    const milestones = [25, 50, 75, 100];
                    for (const milestone of milestones) {
                        if (oldProgress < milestone && newProgress >= milestone) {
                            // Get all donors for this campaign
                            const allDonations = yield Donation_1.Donation.find({
                                campaignId: campaign._id,
                                status: 'success',
                                userId: { $exists: true, $ne: null }
                            }).distinct('userId');
                            if (allDonations.length > 0) {
                                yield (0, notification_service_1.notifyCampaignMilestone)(allDonations, campaign.title, campaign._id, milestone, newAmount, campaign.targetAmount);
                                console.log(`🎉 [PayOS] Milestone ${milestone}% notification sent to ${allDonations.length} donors`);
                            }
                            break; // Only notify for the first milestone reached
                        }
                    }
                }
                catch (milestoneError) {
                    console.error('❌ [PayOS] Error sending milestone notification:', milestoneError);
                    // Don't fail the payment processing if milestone notification fails
                }
            }
        }));
        res.json(result);
    }
    catch (error) {
        console.error('[PayOS] Error:', error);
        res.status(500).json({
            success: false,
            message: 'Error processing payment',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
    finally {
        session.endSession();
    }
});
exports.handlePayOSReturn = handlePayOSReturn;
const handleMomoIPN = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const session = yield mongoose_1.default.startSession();
    try {
        console.log('=== MoMo IPN Handler Called ===');
        console.log('[MoMo IPN] Timestamp:', new Date().toISOString());
        console.log('[MoMo IPN] Received:', req.body);
        console.log('[MoMo IPN] Headers:', req.headers);
        yield session.withTransaction(() => __awaiter(void 0, void 0, void 0, function* () {
            // 1. Xác thực signature
            const isValid = yield getMomoService().verifyCallback(Object.assign({}, req.body));
            if (!isValid) {
                console.error('[MoMo IPN] Invalid signature');
                throw new Error('Invalid signature');
            }
            // 2. Tìm donation
            const donation = yield Donation_1.Donation.findOne({ transactionId: req.body.orderId }).session(session);
            if (!donation) {
                console.error('[MoMo IPN] Donation not found', req.body.orderId);
                throw new Error('Donation not found');
            }
            // 3. Kiểm tra trạng thái
            if (donation.status !== 'pending') {
                console.log('[MoMo IPN] Donation already processed', donation.status);
                return;
            }
            // 4. Kiểm tra số tiền
            if (Number(req.body.amount) !== donation.amount) {
                console.error(`[MoMo IPN] Amount mismatch: received ${req.body.amount}, expected ${donation.amount}`);
                throw new Error('Amount mismatch');
            }
            // 5. Cập nhật donation
            donation.status = req.body.resultCode === '0' ? 'success' : 'failed';
            donation.paymentTime = new Date();
            donation.paymentDetails = {
                momoTransactionId: req.body.transId,
                momoResponseTime: Date.now(),
                momoPayType: req.body.payType,
                momoErrorCode: req.body.resultCode,
                momoErrorMessage: req.body.message
            };
            console.log('[MoMo IPN] Saving donation...');
            yield donation.save({ session });
            console.log('[MoMo IPN] Donation updated', donation);
            // 6. Nếu thành công, cập nhật campaign
            if (req.body.resultCode === '0') {
                const campaign = yield Campaign_1.Campaign.findById(donation.campaignId).session(session);
                if (!campaign) {
                    console.error('[MoMo IPN] Campaign not found', donation.campaignId);
                    throw new Error('Campaign not found');
                }
                // Cập nhật số liệu campaign
                const oldAmount = campaign.currentAmount || 0;
                const newAmount = oldAmount + donation.amount;
                const newTotalDonations = (campaign.totalDonations || 0) + 1;
                // Mỗi lần thanh toán thành công sẽ tăng 1 lượt ủng hộ (không kiểm tra email trùng)
                const newTotalDonors = (campaign.totalDonors || 0) + 1;
                // Tạo donor object mới
                const newDonor = {
                    transactionId: donation.transactionId,
                    amount: donation.amount,
                    name: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name,
                    email: donation.email,
                    phone: donation.phone,
                    address: donation.address,
                    isAnonymous: donation.isAnonymous,
                    paymentMethod: donation.paymentMethod,
                    status: 'success',
                    createdAt: new Date()
                };
                // Cập nhật tiến độ
                const newProgress = Math.min(100, (newAmount / campaign.targetAmount) * 100);
                console.log('[MoMo IPN] Campaign update details:', {
                    oldAmount,
                    newAmount,
                    oldProgress: (oldAmount / campaign.targetAmount) * 100,
                    newProgress,
                    totalDonations: newTotalDonations,
                    totalDonors: newTotalDonors
                });
                // Update campaign using updateOne to avoid validation issues
                yield Campaign_1.Campaign.updateOne({ _id: donation.campaignId }, {
                    $inc: {
                        currentAmount: donation.amount,
                        totalDonations: 1,
                        totalDonors: 1
                    },
                    $push: {
                        donors: newDonor
                    },
                    $set: {
                        progress: newProgress
                    }
                }, {
                    session,
                    runValidators: false // Skip validation for partial updates
                });
                // Get updated campaign data for socket event
                const updatedCampaign = yield Campaign_1.Campaign.findById(donation.campaignId).session(session);
                console.log('[MoMo IPN] Campaign updated successfully:', {
                    campaignId: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign._id,
                    currentAmount: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign.currentAmount,
                    totalDonations: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign.totalDonations,
                    totalDonors: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign.totalDonors,
                    progress: updatedCampaign === null || updatedCampaign === void 0 ? void 0 : updatedCampaign.progress
                });
                // Emit socket event với đầy đủ thông tin
                if (updatedCampaign) {
                    index_1.io.emit('campaign_updated', {
                        campaignId: updatedCampaign._id.toString(),
                        currentAmount: updatedCampaign.currentAmount,
                        totalDonations: updatedCampaign.totalDonations,
                        totalDonors: updatedCampaign.totalDonors,
                        progress: updatedCampaign.progress,
                        lastDonation: {
                            amount: donation.amount,
                            donorName: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name,
                            paymentMethod: donation.paymentMethod,
                            createdAt: new Date(),
                            transactionId: donation.transactionId
                        }
                    });
                }
                // Send thank you notification
                try {
                    yield (0, notification_service_1.notifyDonationSuccess)(donation.email, donation.name, donation.amount, campaign.title, campaign._id, donation.transactionId, donation.isAnonymous, donation.userId);
                    console.log('📧 [MoMo] Thank you notification sent successfully');
                }
                catch (notificationError) {
                    console.error('❌ [MoMo] Error sending thank you notification:', notificationError);
                    // Don't fail the payment processing if notification fails
                }
                // Check and award badges for logged-in users
                if (donation.userId) {
                    try {
                        const { checkSupporterBadge } = yield Promise.resolve().then(() => __importStar(require('../services/badge.service')));
                        const badgeResult = yield checkSupporterBadge(donation.userId);
                        if (badgeResult.earned && badgeResult.levelUp) {
                            console.log('🏆 [MoMo] Badge awarded/upgraded for user:', donation.userId);
                        }
                    }
                    catch (badgeError) {
                        console.error('❌ [MoMo] Error checking badges:', badgeError);
                        // Don't fail the payment processing if badge check fails
                    }
                }
                // Check for milestone achievements
                try {
                    const oldProgress = (oldAmount / campaign.targetAmount) * 100;
                    const newProgress = (newAmount / campaign.targetAmount) * 100;
                    // Check if we crossed any milestone (25%, 50%, 75%, 100%)
                    const milestones = [25, 50, 75, 100];
                    for (const milestone of milestones) {
                        if (oldProgress < milestone && newProgress >= milestone) {
                            // Get all donors for this campaign
                            const allDonations = yield Donation_1.Donation.find({
                                campaignId: campaign._id,
                                status: 'success',
                                userId: { $exists: true, $ne: null }
                            }).distinct('userId');
                            if (allDonations.length > 0) {
                                yield (0, notification_service_1.notifyCampaignMilestone)(allDonations, campaign.title, campaign._id, milestone, newAmount, campaign.targetAmount);
                                console.log(`🎉 [MoMo] Milestone ${milestone}% notification sent to ${allDonations.length} donors`);
                            }
                            break; // Only notify for the first milestone reached
                        }
                    }
                }
                catch (milestoneError) {
                    console.error('❌ [MoMo] Error sending milestone notification:', milestoneError);
                    // Don't fail the payment processing if milestone notification fails
                }
            }
        }));
        res.json({ success: true, message: 'Payment processed' });
    }
    catch (error) {
        console.error('[MoMo IPN] Error:', error);
        res.status(500).json({
            success: false,
            message: 'Error processing payment',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
    finally {
        session.endSession();
    }
});
exports.handleMomoIPN = handleMomoIPN;
const handleMomoReturn = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { resultCode, orderId } = req.query;
        const donation = yield Donation_1.Donation.findOne({ transactionId: orderId });
        if (!donation) {
            return res.status(404).json({ message: 'Donation not found' });
        }
        if (resultCode === '0') {
            res.redirect(`/campaigns/${donation.campaignId}?status=success`);
        }
        else {
            res.redirect(`/campaigns/${donation.campaignId}?status=failed`);
        }
    }
    catch (error) {
        console.error('Error handling MoMo return:', error);
        res.status(500).json({
            message: 'Error processing payment return',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.handleMomoReturn = handleMomoReturn;
const getDonationStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { transactionId } = req.params;
        const donation = yield Donation_1.Donation.findOne({ transactionId }).populate('campaignId', 'title');
        if (!donation) {
            return res.status(404).json({
                success: false,
                message: 'Donation not found'
            });
        }
        // Map donation status to frontend expected format
        const paymentStatus = donation.status === 'success' ? 'completed' :
            donation.status === 'failed' ? 'failed' : 'pending';
        res.json({
            success: true,
            data: {
                transactionId: donation.transactionId,
                amount: donation.amount,
                name: donation.name,
                isAnonymous: donation.isAnonymous,
                createdAt: donation.createdAt,
                paymentStatus,
                campaign: {
                    campaignId: donation.campaignId._id || donation.campaignId,
                    title: donation.campaignId.title || 'Unknown Campaign'
                }
            }
        });
    }
    catch (error) {
        console.error('Error getting donation status:', error);
        res.status(500).json({
            success: false,
            message: 'Error getting donation status',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getDonationStatus = getDonationStatus;
const getDonationsByCampaignId = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { campaignId } = req.params;
        const donations = yield Donation_1.Donation.find({
            campaignId,
            status: 'success' // Only get successful donations
        })
            .select('name amount message createdAt isAnonymous') // Select only necessary fields
            .sort({ createdAt: -1 }); // Sort by newest first
        res.json(donations);
    }
    catch (error) {
        console.error('Error getting donations by campaign ID:', error);
        res.status(500).json({
            message: 'Error getting donations',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getDonationsByCampaignId = getDonationsByCampaignId;
