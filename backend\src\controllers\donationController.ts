import { Request, Response } from 'express';
import { Donation, IDonation } from '../models/Donation';
import { Campaign, ICampaign } from '../models/Campaign';
import { config } from '../config';
import { PayOSService } from '../services/payosService';
import { MomoService } from '../services/momoService';
import { notifyDonationSuccess, notifyCampaignMilestone } from '../services/notification.service';
import mongoose, { Types } from 'mongoose';
import { io } from '../index';
import { sendEmail } from '../services/emailService';
import { createNotification } from './notificationController';
import { IUser, PayOSPaymentResponse, MomoPaymentResponse, NotificationData, EmailData } from '../types';
import { generateTransactionId } from '../utils/idGenerator';

// Lazy initialization to ensure environment variables are loaded
let payosService: PayOSService;
let momoService: MomoService;

const getPayOSService = () => {
  if (!payosService) {
    payosService = new PayOSService();
  }
  return payosService;
};

const getMomoService = () => {
  if (!momoService) {
    momoService = new MomoService();
  }
  return momoService;
};

interface DonationRequest {
  campaignId: string;
  amount: number;
  donorName: string;
  donorEmail: string;
  donorPhone: string;
  message?: string;
  paymentMethod: string;
}

// Helper function to check if value is ObjectId
const isObjectId = (value: unknown): value is mongoose.Types.ObjectId => {
  return value instanceof mongoose.Types.ObjectId;
};

export const createDonation = async (req: Request, res: Response) => {
  try {
    const { campaignId, amount, name, email, phone, address, message, paymentMethod, isAnonymous } = req.body;

    // Validate required fields
    if (!campaignId || !amount || !name || !email) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Validate minimum amount
    if (amount < 1000) {
      return res.status(400).json({ message: 'Minimum donation amount is 1,000 VNĐ' });
    }

    // Check if campaign exists and is active
    const campaign = await Campaign.findById(campaignId);
    if (!campaign) {
      return res.status(404).json({ message: 'Campaign not found' });
    }

    if (campaign.status !== 'active') {
      return res.status(400).json({ message: 'Campaign is not active' });
    }

    // Generate transaction ID
    const transactionId = generateTransactionId();

    // Get user ID if authenticated
    const userId = (req as any).user?._id;

    // Create donation record
    const donation = new Donation({
      userId: userId || undefined, // Save userId if user is logged in
      campaignId,
      amount,
      name,
      email,
      phone: phone || undefined,
      address,
      message,
      paymentMethod,
      isAnonymous,
      status: 'pending',
      transactionId
    });

    await donation.save();

    // Create payment URL based on payment method
    try {
      let paymentUrl;
      if (paymentMethod === 'MOMO') {
        console.log('Creating MoMo payment URL for donation:', donation._id);
        paymentUrl = await getMomoService().createPaymentUrl(donation);
        console.log('MoMo payment URL created:', paymentUrl);
      } else if (paymentMethod === 'PAYOS') {
        console.log('Creating PayOS payment URL for donation:', donation._id);
        const payosResponse = await getPayOSService().createPaymentUrl(donation);
        if (!payosResponse.success) {
          throw new Error(payosResponse.message);
        }
        paymentUrl = payosResponse.paymentUrl;
        console.log('PayOS payment URL created:', paymentUrl);
      } else {
        return res.status(400).json({ message: 'Invalid payment method' });
      }

      if (!paymentUrl) {
        throw new Error('Failed to create payment URL');
      }

      return res.json({ paymentUrl });
    } catch (paymentError: any) {
      console.error('Payment error:', paymentError);

      // Update donation status to failed
      donation.status = 'failed';
      donation.error = paymentError.message;
      await donation.save();

      return res.status(500).json({
        message: 'Failed to create payment URL',
        error: paymentError.message
      });
    }
  } catch (error: any) {
    console.error('Error creating donation:', error);
    res.status(500).json({
      message: 'Failed to create donation',
      error: error.message
    });
  }
};

export const handlePayOSReturn = async (req: Request, res: Response) => {
  const session = await mongoose.startSession();
  try {
    console.log('=== PayOS Return Handler Called ===');
    console.log('[PayOS Return] Timestamp:', new Date().toISOString());
    console.log('[PayOS Return] Query params:', req.query);
    console.log('[PayOS Return] Body:', req.body);
    console.log('[PayOS Return] Headers:', req.headers);

    // PayOS sends data via query parameters, not body
    const callbackData = {
      orderCode: req.query.orderCode || req.body.orderCode,
      code: req.query.code || req.body.code,
      id: req.query.id || req.body.id,
      cancel: req.query.cancel || req.body.cancel,
      status: req.query.status || req.body.status
    };

    let result;
    await session.withTransaction(async () => {
      result = await getPayOSService().handlePaymentCallback(callbackData);
      if (result.success && 'orderCode' in result) {
        const donation = await Donation.findOne({ transactionId: result.orderCode }).session(session);
        if (!donation) {
          console.error('[PayOS] Donation not found', result.orderCode);
          throw new Error('Donation not found');
        }

        // Update donation status
        donation.status = 'success';
        donation.paymentTime = new Date();
        console.log('[PayOS] Saving donation...');
        await donation.save({ session });
        console.log('[PayOS] Donation updated', donation);

        // Update campaign using findByIdAndUpdate to avoid validation issues
        const campaign = await Campaign.findById(donation.campaignId).session(session);
        if (!campaign) {
          console.error('[PayOS] Campaign not found', donation.campaignId);
          throw new Error('Campaign not found');
        }

        // Cập nhật số liệu campaign
        const oldAmount = campaign.currentAmount || 0;
        const newAmount = oldAmount + donation.amount;
        const newTotalDonations = (campaign.totalDonations || 0) + 1;

        // Mỗi lần thanh toán thành công sẽ tăng 1 lượt ủng hộ (không kiểm tra email trùng)
        const newTotalDonors = (campaign.totalDonors || 0) + 1;

        // Tạo donor object mới
        const newDonor = {
          transactionId: donation.transactionId,
          amount: donation.amount,
          name: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name,
          email: donation.email,
          phone: donation.phone,
          address: donation.address,
          isAnonymous: donation.isAnonymous,
          paymentMethod: donation.paymentMethod,
          status: 'success',
          createdAt: new Date()
        };

        // Cập nhật tiến độ
        const newProgress = Math.min(100, (newAmount / campaign.targetAmount) * 100);

        console.log('[PayOS] Campaign update details:', {
          oldAmount,
          newAmount,
          oldProgress: (oldAmount / campaign.targetAmount) * 100,
          newProgress,
          totalDonations: newTotalDonations,
          totalDonors: newTotalDonors
        });

        // Update campaign using updateOne to avoid validation issues
        await Campaign.updateOne(
          { _id: donation.campaignId },
          {
            $inc: {
              currentAmount: donation.amount,
              totalDonations: 1,
              totalDonors: 1
            },
            $push: {
              donors: newDonor
            },
            $set: {
              progress: newProgress
            }
          },
          {
            session,
            runValidators: false // Skip validation for partial updates
          }
        );

        // Get updated campaign data for socket event
        const updatedCampaign = await Campaign.findById(donation.campaignId).session(session);

        console.log('[PayOS] Campaign updated successfully:', {
          campaignId: updatedCampaign?._id,
          currentAmount: updatedCampaign?.currentAmount,
          totalDonations: updatedCampaign?.totalDonations,
          totalDonors: updatedCampaign?.totalDonors,
          progress: updatedCampaign?.progress
        });

        // Emit socket event với đầy đủ thông tin
        if (updatedCampaign) {
          io.emit('campaign_updated', {
            campaignId: (updatedCampaign._id as mongoose.Types.ObjectId).toString(),
            currentAmount: updatedCampaign.currentAmount,
            totalDonations: updatedCampaign.totalDonations,
            totalDonors: updatedCampaign.totalDonors,
            progress: updatedCampaign.progress,
            lastDonation: {
              amount: donation.amount,
              donorName: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name,
              paymentMethod: donation.paymentMethod,
              createdAt: new Date(),
              transactionId: donation.transactionId
            }
          });
        }

        // Send thank you notification
        try {
          await notifyDonationSuccess(
            donation.email,
            donation.name,
            donation.amount,
            campaign.title,
            campaign._id,
            donation.transactionId,
            donation.isAnonymous,
            donation.userId
          );
          console.log('📧 [PayOS] Thank you notification sent successfully');
        } catch (notificationError) {
          console.error('❌ [PayOS] Error sending thank you notification:', notificationError);
          // Don't fail the payment processing if notification fails
        }

        // Check and award badges for logged-in users
        if (donation.userId) {
          try {
            const { checkSupporterBadge } = await import('../services/badge.service');
            const badgeResult = await checkSupporterBadge(donation.userId);
            if (badgeResult.earned && badgeResult.levelUp) {
              console.log('🏆 [PayOS] Badge awarded/upgraded for user:', donation.userId);
            }
          } catch (badgeError) {
            console.error('❌ [PayOS] Error checking badges:', badgeError);
            // Don't fail the payment processing if badge check fails
          }
        }

        // Check for milestone achievements
        try {
          const oldProgress = (oldAmount / campaign.targetAmount) * 100;
          const newProgress = (newAmount / campaign.targetAmount) * 100;

          // Check if we crossed any milestone (25%, 50%, 75%, 100%)
          const milestones = [25, 50, 75, 100];
          for (const milestone of milestones) {
            if (oldProgress < milestone && newProgress >= milestone) {
              // Get all donors for this campaign
              const allDonations = await Donation.find({
                campaignId: campaign._id,
                status: 'success',
                userId: { $exists: true, $ne: null }
              }).distinct('userId');

              if (allDonations.length > 0) {
                await notifyCampaignMilestone(
                  allDonations,
                  campaign.title,
                  campaign._id,
                  milestone,
                  newAmount,
                  campaign.targetAmount
                );
                console.log(`🎉 [PayOS] Milestone ${milestone}% notification sent to ${allDonations.length} donors`);
              }
              break; // Only notify for the first milestone reached
            }
          }
        } catch (milestoneError) {
          console.error('❌ [PayOS] Error sending milestone notification:', milestoneError);
          // Don't fail the payment processing if milestone notification fails
        }
      }
    });

    res.json(result);
  } catch (error) {
    console.error('[PayOS] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error processing payment',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  } finally {
    session.endSession();
  }
};

export const handleMomoIPN = async (req: Request, res: Response) => {
  const session = await mongoose.startSession();
  try {
    console.log('=== MoMo IPN Handler Called ===');
    console.log('[MoMo IPN] Timestamp:', new Date().toISOString());
    console.log('[MoMo IPN] Received:', req.body);
    console.log('[MoMo IPN] Headers:', req.headers);

    await session.withTransaction(async () => {
      // 1. Xác thực signature
      const isValid = await getMomoService().verifyCallback({ ...req.body });
      if (!isValid) {
        console.error('[MoMo IPN] Invalid signature');
        throw new Error('Invalid signature');
      }

      // 2. Tìm donation
      const donation = await Donation.findOne({ transactionId: req.body.orderId }).session(session);
      if (!donation) {
        console.error('[MoMo IPN] Donation not found', req.body.orderId);
        throw new Error('Donation not found');
      }

      // 3. Kiểm tra trạng thái
      if (donation.status !== 'pending') {
        console.log('[MoMo IPN] Donation already processed', donation.status);
        return;
      }

      // 4. Kiểm tra số tiền
      if (Number(req.body.amount) !== donation.amount) {
        console.error(`[MoMo IPN] Amount mismatch: received ${req.body.amount}, expected ${donation.amount}`);
        throw new Error('Amount mismatch');
      }

      // 5. Cập nhật donation
      donation.status = req.body.resultCode === '0' ? 'success' : 'failed';
      donation.paymentTime = new Date();
      donation.paymentDetails = {
        momoTransactionId: req.body.transId,
        momoResponseTime: Date.now(),
        momoPayType: req.body.payType,
        momoErrorCode: req.body.resultCode,
        momoErrorMessage: req.body.message
      };
      console.log('[MoMo IPN] Saving donation...');
      await donation.save({ session });
      console.log('[MoMo IPN] Donation updated', donation);

      // 6. Nếu thành công, cập nhật campaign
      if (req.body.resultCode === '0') {
        const campaign = await Campaign.findById(donation.campaignId).session(session);
        if (!campaign) {
          console.error('[MoMo IPN] Campaign not found', donation.campaignId);
          throw new Error('Campaign not found');
        }

        // Cập nhật số liệu campaign
        const oldAmount = campaign.currentAmount || 0;
        const newAmount = oldAmount + donation.amount;
        const newTotalDonations = (campaign.totalDonations || 0) + 1;

        // Mỗi lần thanh toán thành công sẽ tăng 1 lượt ủng hộ (không kiểm tra email trùng)
        const newTotalDonors = (campaign.totalDonors || 0) + 1;

        // Tạo donor object mới
        const newDonor = {
          transactionId: donation.transactionId,
          amount: donation.amount,
          name: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name,
          email: donation.email,
          phone: donation.phone,
          address: donation.address,
          isAnonymous: donation.isAnonymous,
          paymentMethod: donation.paymentMethod,
          status: 'success',
          createdAt: new Date()
        };

        // Cập nhật tiến độ
        const newProgress = Math.min(100, (newAmount / campaign.targetAmount) * 100);

        console.log('[MoMo IPN] Campaign update details:', {
          oldAmount,
          newAmount,
          oldProgress: (oldAmount / campaign.targetAmount) * 100,
          newProgress,
          totalDonations: newTotalDonations,
          totalDonors: newTotalDonors
        });

        // Update campaign using updateOne to avoid validation issues
        await Campaign.updateOne(
          { _id: donation.campaignId },
          {
            $inc: {
              currentAmount: donation.amount,
              totalDonations: 1,
              totalDonors: 1
            },
            $push: {
              donors: newDonor
            },
            $set: {
              progress: newProgress
            }
          },
          {
            session,
            runValidators: false // Skip validation for partial updates
          }
        );

        // Recalculate and fix stats to ensure accuracy
        const allDonations = await Donation.find({
          campaignId: donation.campaignId,
          status: 'success'
        }).session(session);

        const actualTotalAmount = allDonations.reduce((sum, d) => sum + d.amount, 0);
        const actualTotalCount = allDonations.length;
        const actualProgress = Math.min(100, (actualTotalAmount / campaign.targetAmount) * 100);

        // Fix any discrepancies
        await Campaign.updateOne(
          { _id: donation.campaignId },
          {
            $set: {
              currentAmount: actualTotalAmount,
              totalDonations: actualTotalCount,
              totalDonors: actualTotalCount,
              progress: actualProgress
            }
          },
          {
            session,
            runValidators: false
          }
        );

        // Get updated campaign data for socket event
        const updatedCampaign = await Campaign.findById(donation.campaignId).session(session);

        console.log('[MoMo IPN] Campaign updated successfully:', {
          campaignId: updatedCampaign?._id,
          currentAmount: updatedCampaign?.currentAmount,
          totalDonations: updatedCampaign?.totalDonations,
          totalDonors: updatedCampaign?.totalDonors,
          progress: updatedCampaign?.progress
        });

        // Emit socket event với đầy đủ thông tin
        if (updatedCampaign) {
          io.emit('campaign_updated', {
            campaignId: (updatedCampaign._id as mongoose.Types.ObjectId).toString(),
            currentAmount: updatedCampaign.currentAmount,
            totalDonations: updatedCampaign.totalDonations,
            totalDonors: updatedCampaign.totalDonors,
            progress: updatedCampaign.progress,
            lastDonation: {
              amount: donation.amount,
              donorName: donation.isAnonymous ? 'Người ủng hộ ẩn danh' : donation.name,
              paymentMethod: donation.paymentMethod,
              createdAt: new Date(),
              transactionId: donation.transactionId
            }
          });
        }

        // Send thank you notification
        try {
          await notifyDonationSuccess(
            donation.email,
            donation.name,
            donation.amount,
            campaign.title,
            campaign._id,
            donation.transactionId,
            donation.isAnonymous,
            donation.userId
          );
          console.log('📧 [MoMo] Thank you notification sent successfully');
        } catch (notificationError) {
          console.error('❌ [MoMo] Error sending thank you notification:', notificationError);
          // Don't fail the payment processing if notification fails
        }

        // Check and award badges for logged-in users
        if (donation.userId) {
          try {
            const { checkSupporterBadge } = await import('../services/badge.service');
            const badgeResult = await checkSupporterBadge(donation.userId);
            if (badgeResult.earned && badgeResult.levelUp) {
              console.log('🏆 [MoMo] Badge awarded/upgraded for user:', donation.userId);
            }
          } catch (badgeError) {
            console.error('❌ [MoMo] Error checking badges:', badgeError);
            // Don't fail the payment processing if badge check fails
          }
        }

        // Check for milestone achievements
        try {
          const oldProgress = (oldAmount / campaign.targetAmount) * 100;
          const newProgress = (newAmount / campaign.targetAmount) * 100;

          // Check if we crossed any milestone (25%, 50%, 75%, 100%)
          const milestones = [25, 50, 75, 100];
          for (const milestone of milestones) {
            if (oldProgress < milestone && newProgress >= milestone) {
              // Get all donors for this campaign
              const allDonations = await Donation.find({
                campaignId: campaign._id,
                status: 'success',
                userId: { $exists: true, $ne: null }
              }).distinct('userId');

              if (allDonations.length > 0) {
                await notifyCampaignMilestone(
                  allDonations,
                  campaign.title,
                  campaign._id,
                  milestone,
                  newAmount,
                  campaign.targetAmount
                );
                console.log(`🎉 [MoMo] Milestone ${milestone}% notification sent to ${allDonations.length} donors`);
              }
              break; // Only notify for the first milestone reached
            }
          }
        } catch (milestoneError) {
          console.error('❌ [MoMo] Error sending milestone notification:', milestoneError);
          // Don't fail the payment processing if milestone notification fails
        }
      }
    });

    res.json({ success: true, message: 'Payment processed' });
  } catch (error) {
    console.error('[MoMo IPN] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Error processing payment',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  } finally {
    session.endSession();
  }
};

export const handleMomoReturn = async (req: Request, res: Response) => {
  try {
    const { resultCode, orderId } = req.query;
    const donation = await Donation.findOne({ transactionId: orderId });

    if (!donation) {
      return res.status(404).json({ message: 'Donation not found' });
    }

    if (resultCode === '0') {
      res.redirect(`/campaigns/${donation.campaignId}?status=success`);
    } else {
      res.redirect(`/campaigns/${donation.campaignId}?status=failed`);
    }
  } catch (error) {
    console.error('Error handling MoMo return:', error);
    res.status(500).json({
      message: 'Error processing payment return',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const getDonationStatus = async (req: Request, res: Response) => {
  try {
    const { transactionId } = req.params;
    const donation = await Donation.findOne({ transactionId }).populate('campaignId', 'title');

    if (!donation) {
      return res.status(404).json({
        success: false,
        message: 'Donation not found'
      });
    }

    // Map donation status to frontend expected format
    const paymentStatus = donation.status === 'success' ? 'completed' :
                         donation.status === 'failed' ? 'failed' : 'pending';

    res.json({
      success: true,
      data: {
        transactionId: donation.transactionId,
        amount: donation.amount,
        name: donation.name,
        isAnonymous: donation.isAnonymous,
        createdAt: donation.createdAt,
        paymentStatus,
        campaign: {
          campaignId: donation.campaignId._id || donation.campaignId,
          title: donation.campaignId.title || 'Unknown Campaign'
        }
      }
    });
  } catch (error) {
    console.error('Error getting donation status:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting donation status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const getDonationsByCampaignId = async (req: Request, res: Response) => {
  try {
    const { campaignId } = req.params;

    const donations = await Donation.find({
      campaignId,
      status: 'success' // Only get successful donations
    })
    .select('name amount message createdAt isAnonymous') // Select only necessary fields
    .sort({ createdAt: -1 }); // Sort by newest first

    res.json(donations);
  } catch (error) {
    console.error('Error getting donations by campaign ID:', error);
    res.status(500).json({
      message: 'Error getting donations',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};