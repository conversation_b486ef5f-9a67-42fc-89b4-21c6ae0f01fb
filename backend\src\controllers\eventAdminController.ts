import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Event from '../models/Event';
import EventRegistration from '../models/EventRegistration';
import { User } from '../models/user.model';
import { notifyEventDeleted } from '../services/notification.service';
import { uploadToCloudinary } from '../services/cloudinary';
import ExcelJS from 'exceljs';

// Cập nhật sự kiện (admin)
export const updateEventAdmin = async (req: Request, res: Response) => {
  try {
    console.log('🚀 [UPDATE EVENT] Controller called');
    console.log('🚀 [UPDATE EVENT] Method:', req.method);
    console.log('🚀 [UPDATE EVENT] URL:', req.url);
    console.log('🚀 [UPDATE EVENT] Content-Type:', req.headers['content-type']);

    const { id } = req.params;
    console.log('🚀 [UPDATE EVENT] Event ID:', id);
    console.log('🚀 [UPDATE EVENT] Request body keys:', Object.keys(req.body || {}));
    console.log('🚀 [UPDATE EVENT] Request body:', req.body);
    console.log('🚀 [UPDATE EVENT] Files:', req.files);

    // Handle JSON data only (no images)
    const {
      title,
      description,
      eventDate,
      registrationDeadline,
      location,
      maxParticipants,
      status,
      requirements,
      benefits,
      images
    } = req.body;

    console.log('🔄 [UPDATE EVENT] Processing JSON data');

    // Validate required fields
    if (!title || !description || !eventDate || !registrationDeadline || !location || !maxParticipants) {
      console.log('Missing required fields:', { title, description, eventDate, registrationDeadline, location, maxParticipants });
      return res.status(400).json({
        success: false,
        message: 'Vui lòng điền đầy đủ thông tin bắt buộc'
      });
    }

    console.log('🔍 [UPDATE EVENT] Looking for event with ID:', id);
    const event = await Event.findById(id);
    if (!event) {
      console.log('❌ [UPDATE EVENT] Event not found with ID:', id);
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy sự kiện'
      });
    }
    console.log('✅ [UPDATE EVENT] Found existing event:', event?.title);

    // Use images from request body (empty array if no images)
    const imageUrls: string[] = images || [];
    console.log('🖼️ [UPDATE EVENT] Images from request:', imageUrls.length);

    // Parse requirements and benefits safely
    let parsedRequirements: string[] = [];
    let parsedBenefits: string[] = [];

    console.log('📋 [UPDATE EVENT] Processing requirements and benefits...');
    console.log('📋 [UPDATE EVENT] Requirements raw:', requirements);
    console.log('📋 [UPDATE EVENT] Benefits raw:', benefits);

    try {
      parsedRequirements = requirements ? JSON.parse(requirements) : [];
      console.log('✅ [UPDATE EVENT] Parsed requirements:', parsedRequirements.length);
    } catch (error) {
      console.log('❌ [UPDATE EVENT] Error parsing requirements:', error);
      parsedRequirements = [];
    }

    try {
      parsedBenefits = benefits ? JSON.parse(benefits) : [];
      console.log('✅ [UPDATE EVENT] Parsed benefits:', parsedBenefits.length);
    } catch (error) {
      console.log('❌ [UPDATE EVENT] Error parsing benefits:', error);
      parsedBenefits = [];
    }

    // Validate dates
    const eventDateObj = new Date(eventDate);
    const registrationDeadlineObj = new Date(registrationDeadline);

    console.log('Date validation:', { eventDate, registrationDeadline, eventDateObj, registrationDeadlineObj });

    if (isNaN(eventDateObj.getTime()) || isNaN(registrationDeadlineObj.getTime())) {
      console.log('Invalid dates detected');
      return res.status(400).json({
        success: false,
        message: 'Ngày tháng không hợp lệ'
      });
    }

    if (registrationDeadlineObj >= eventDateObj) {
      console.log('Registration deadline is after event date');
      return res.status(400).json({
        success: false,
        message: 'Hạn đăng ký phải trước ngày diễn ra sự kiện'
      });
    }

    // Update event
    console.log('💾 [UPDATE EVENT] Updating database...');
    const updateData = {
      title,
      description,
      eventDate: eventDateObj,
      registrationDeadline: registrationDeadlineObj,
      location,
      maxParticipants: parseInt(maxParticipants),
      status,
      requirements: parsedRequirements,
      benefits: parsedBenefits,
      images: imageUrls
    };

    console.log('💾 [UPDATE EVENT] Update data:', JSON.stringify(updateData, null, 2));

    const updatedEvent = await Event.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: false } // Disable validators to avoid issues
    );

    if (!updatedEvent) {
      console.log('❌ [UPDATE EVENT] Failed to update event');
      return res.status(500).json({
        success: false,
        message: 'Không thể cập nhật sự kiện'
      });
    }

    console.log('✅ [UPDATE EVENT] Event updated successfully:', updatedEvent?.title);

    res.json({
      success: true,
      message: 'Cập nhật sự kiện thành công',
      event: updatedEvent
    });
  } catch (error: any) {
    console.error('❌ [UPDATE EVENT] Error updating event:', error);
    console.error('❌ [UPDATE EVENT] Error stack:', error.stack);
    console.error('❌ [UPDATE EVENT] Error name:', error.name);
    console.error('❌ [UPDATE EVENT] Error message:', error.message);

    // Send detailed error response
    res.status(500).json({
      success: false,
      message: 'Lỗi khi cập nhật sự kiện',
      error: error.message,
      errorName: error.name,
      details: process.env.NODE_ENV === 'development' ? {
        stack: error.stack,
        fullError: error
      } : undefined
    });
  }
};

// Xóa sự kiện (admin)
export const deleteEventAdmin = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { reason } = req.body; // Get deletion reason from request body

    const event = await Event.findById(id);
    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy sự kiện'
      });
    }

    // Get all registered participants to notify them
    const registrations = await EventRegistration.find({
      eventId: id,
      status: 'registered'
    }).distinct('userId');

    // Send notification to registered participants
    if (registrations.length > 0) {
      try {
        await notifyEventDeleted(
          registrations,
          event.title,
          new mongoose.Types.ObjectId(event._id),
          reason || 'Sự kiện đã bị hủy bởi quản trị viên'
        );
        console.log('📧 [Event] Deletion notification sent to', registrations.length, 'participants');
      } catch (notificationError) {
        console.error('❌ [Event] Error sending deletion notification:', notificationError);
        // Don't fail the deletion if notification fails
      }
    }

    // TODO: Delete images from storage
    if (event.images.length > 0) {
      console.log('TODO: Delete images:', event.images);
    }

    // Delete all registrations
    await EventRegistration.deleteMany({ eventId: id });

    // Delete event
    await Event.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Xóa sự kiện thành công'
    });
  } catch (error: any) {
    console.error('Error deleting event:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi xóa sự kiện',
      error: error.message
    });
  }
};

// Lấy danh sách người đăng ký sự kiện (admin)
export const getEventRegistrations = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status = 'all', page = 1, limit = 50 } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build filter
    const filter: any = { eventId: id };
    if (status !== 'all') {
      filter.status = status;
    }

    const registrations = await EventRegistration.find(filter)
      .populate({
        path: 'userId',
        select: 'name email phone address dateOfBirth'
      })
      .sort({ registrationDate: -1 })
      .skip(skip)
      .limit(limitNum);

    const total = await EventRegistration.countDocuments(filter);

    // Get event info
    const event = await Event.findById(id).select('title eventDate location');

    res.json({
      success: true,
      event,
      registrations,
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(total / limitNum),
        totalRegistrations: total,
        hasNext: pageNum < Math.ceil(total / limitNum),
        hasPrev: pageNum > 1
      }
    });
  } catch (error: any) {
    console.error('Error fetching event registrations:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tải danh sách đăng ký',
      error: error.message
    });
  }
};

// Xuất danh sách đăng ký ra Excel (admin)
export const exportEventRegistrations = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status = 'registered' } = req.query;

    // Get event info
    const event = await Event.findById(id);
    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy sự kiện'
      });
    }

    // Get registrations
    const filter: any = { eventId: id };
    if (status !== 'all') {
      filter.status = status;
    }

    const registrations = await EventRegistration.find(filter)
      .populate({
        path: 'userId',
        select: 'name email'
      })
      .sort({ registrationDate: -1 });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Danh sách đăng ký');

    // Set column headers
    worksheet.columns = [
      { header: 'STT', key: 'stt', width: 8 },
      { header: 'Họ và tên', key: 'name', width: 25 },
      { header: 'Email', key: 'email', width: 30 },
      { header: 'Số điện thoại', key: 'phone', width: 15 },
      { header: 'Ngày sinh', key: 'dateOfBirth', width: 12 },
      { header: 'Giới tính', key: 'gender', width: 10 },
      { header: 'Địa chỉ', key: 'address', width: 35 },
      { header: 'Nghề nghiệp', key: 'occupation', width: 20 },
      { header: 'Tổ chức', key: 'organization', width: 20 },
      { header: 'Liên hệ khẩn cấp', key: 'emergencyContact', width: 25 },
      { header: 'Hạn chế ăn uống', key: 'dietaryRestrictions', width: 20 },
      { header: 'Tình trạng sức khỏe', key: 'medicalConditions', width: 20 },
      { header: 'Lý do tham gia', key: 'motivation', width: 30 },
      { header: 'Ngày đăng ký', key: 'registrationDate', width: 15 },
      { header: 'Trạng thái', key: 'status', width: 12 },
      { header: 'Check-in', key: 'checkInTime', width: 15 },
      { header: 'Ghi chú', key: 'notes', width: 25 }
    ];

    // Style header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    };

    // Add event info at the top
    worksheet.insertRow(1, ['DANH SÁCH ĐĂNG KÝ SỰ KIỆN']);
    worksheet.insertRow(2, [`Tên sự kiện: ${event.title}`]);
    worksheet.insertRow(3, [`Ngày diễn ra: ${event.eventDate.toLocaleDateString('vi-VN')}`]);
    worksheet.insertRow(4, [`Địa điểm: ${event.location}`]);
    worksheet.insertRow(5, [`Tổng số đăng ký: ${registrations.length}`]);
    worksheet.insertRow(6, []); // Empty row

    // Style event info
    for (let i = 1; i <= 5; i++) {
      worksheet.getRow(i).font = { bold: true };
    }

    // Add data rows
    registrations.forEach((registration, index) => {
      const user = registration.userId as any;
      const participant = registration.participantInfo;

      // Format emergency contact
      const emergencyContact = participant?.emergencyContact ?
        `${participant.emergencyContact.name || ''} - ${participant.emergencyContact.phone || ''} (${participant.emergencyContact.relationship || ''})`.trim() : '';

      // Format gender
      const genderText = participant?.gender === 'male' ? 'Nam' :
                        participant?.gender === 'female' ? 'Nữ' :
                        participant?.gender === 'other' ? 'Khác' : '';

      worksheet.addRow({
        stt: index + 1,
        name: participant?.fullName || user?.name || 'N/A',
        email: participant?.email || user?.email || 'N/A',
        phone: participant?.phone || 'N/A',
        dateOfBirth: participant?.dateOfBirth ? new Date(participant.dateOfBirth).toLocaleDateString('vi-VN') : '',
        gender: genderText,
        address: participant?.address || '',
        occupation: participant?.occupation || '',
        organization: participant?.organization || '',
        emergencyContact: emergencyContact,
        dietaryRestrictions: participant?.dietaryRestrictions || '',
        medicalConditions: participant?.medicalConditions || '',
        motivation: participant?.motivation || '',
        registrationDate: registration.registrationDate.toLocaleDateString('vi-VN'),
        status: registration.status === 'registered' ? 'Đã đăng ký' :
                registration.status === 'attended' ? 'Đã tham gia' : 'Đã hủy',
        checkInTime: registration.checkInTime ? registration.checkInTime.toLocaleDateString('vi-VN') : '',
        notes: registration.notes || ''
      });
    });

    // Set response headers
    const fileName = `DanhSachDangKy_${event.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

    // Write to response
    await workbook.xlsx.write(res);
    res.end();
  } catch (error: any) {
    console.error('Error exporting registrations:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi xuất danh sách đăng ký',
      error: error.message
    });
  }
};

// Cập nhật trạng thái đăng ký (admin)
export const updateRegistrationStatus = async (req: Request, res: Response) => {
  try {
    const { eventId, userId } = req.params;
    const { status } = req.body;

    const registration = await EventRegistration.findOne({
      eventId,
      userId
    });

    if (!registration) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy đăng ký'
      });
    }

    registration.status = status;
    await registration.save();

    res.json({
      success: true,
      message: 'Cập nhật trạng thái thành công',
      registration
    });
  } catch (error: any) {
    console.error('Error updating registration status:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi cập nhật trạng thái',
      error: error.message
    });
  }
};
