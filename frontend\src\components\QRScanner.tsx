import React, { useState, useRef, useEffect } from 'react';
import { Camera, X, CheckCircle, AlertCircle, Scan } from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface QRScannerProps {
  isOpen: boolean;
  onClose: () => void;
  onCheckInSuccess?: (data: any) => void;
}

const QRScanner: React.FC<QRScannerProps> = ({
  isOpen,
  onClose,
  onCheckInSuccess
}) => {
  const [scanning, setScanning] = useState(false);
  const [hasCamera, setHasCamera] = useState(false);
  const [manualCode, setManualCode] = useState('');
  const [processing, setProcessing] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  useEffect(() => {
    if (isOpen) {
      checkCameraPermission();
    } else {
      stopCamera();
    }

    return () => {
      stopCamera();
    };
  }, [isOpen]);

  const checkCameraPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          facingMode: 'environment' // Use back camera if available
        } 
      });
      setHasCamera(true);
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
      }
    } catch (error) {
      console.error('Camera access denied:', error);
      setHasCamera(false);
      toast.error('Không thể truy cập camera. Vui lòng nhập mã QR thủ công.');
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setScanning(false);
  };

  const startScanning = () => {
    setScanning(true);
    // In a real implementation, you would use a QR code scanning library
    // like jsQR or qr-scanner here
    toast.info('Tính năng quét QR code đang được phát triển. Vui lòng nhập mã thủ công.');
  };

  const handleManualSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!manualCode.trim()) {
      toast.error('Vui lòng nhập mã QR');
      return;
    }

    await processQRCode(manualCode.trim());
  };

  const processQRCode = async (qrToken: string) => {
    try {
      setProcessing(true);
      const token = localStorage.getItem('token');
      
      const response = await axios.post(`${API_URL}/api/events/checkin`, {
        qrToken
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        toast.success('Check-in thành công!');
        onCheckInSuccess?.(response.data.registration);
        setManualCode('');
        onClose();
      }
    } catch (error: any) {
      console.error('Error processing QR code:', error);
      toast.error(error.response?.data?.message || 'Mã QR không hợp lệ');
    } finally {
      setProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Quét QR Code Check-in
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Camera Section */}
          {hasCamera ? (
            <div className="mb-6">
              <div className="relative bg-black rounded-lg overflow-hidden">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  className="w-full h-64 object-cover"
                />
                
                {/* Scanning Overlay */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-48 h-48 border-2 border-white rounded-lg relative">
                    <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-blue-500"></div>
                    <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-blue-500"></div>
                    <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-blue-500"></div>
                    <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-blue-500"></div>
                    
                    {scanning && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-full h-1 bg-red-500 animate-pulse"></div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-4 text-center">
                {!scanning ? (
                  <button
                    onClick={startScanning}
                    className="flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
                  >
                    <Camera className="h-5 w-5 mr-2" />
                    Bắt đầu quét
                  </button>
                ) : (
                  <div className="flex items-center justify-center text-blue-600">
                    <Scan className="h-5 w-5 mr-2 animate-pulse" />
                    <span>Đang quét QR code...</span>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="mb-6 text-center py-8">
              <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-600">
                Không thể truy cập camera. Vui lòng nhập mã QR thủ công.
              </p>
            </div>
          )}

          {/* Manual Input Section */}
          <div className="border-t border-gray-200 pt-6">
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              Hoặc nhập mã QR thủ công:
            </h4>
            
            <form onSubmit={handleManualSubmit} className="space-y-4">
              <div>
                <label htmlFor="manual-qr-code" className="block text-sm font-medium text-gray-700 mb-2">
                  Mã QR thủ công
                </label>
                <textarea
                  id="manual-qr-code"
                  name="manualCode"
                  value={manualCode}
                  onChange={(e) => setManualCode(e.target.value)}
                  placeholder="Dán mã QR token vào đây..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <button
                type="submit"
                disabled={processing || !manualCode.trim()}
                className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {processing ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                {processing ? 'Đang xử lý...' : 'Check-in'}
              </button>
            </form>
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h5 className="text-sm font-medium text-blue-900 mb-2">
              Hướng dẫn sử dụng:
            </h5>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• Yêu cầu người tham gia hiển thị QR code check-in</li>
              <li>• Quét QR code bằng camera hoặc nhập mã thủ công</li>
              <li>• Hệ thống sẽ xác nhận check-in tự động</li>
              <li>• Mỗi QR code chỉ có thể sử dụng một lần</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRScanner;
