import express, { Router, Request, Response, RequestHandler, NextFunction } from 'express'
import { User, IUser } from '../models/user.model'
import { Notification } from '../models/Notification'
import { createNotification, getUnreadNotifications, markNotificationAsRead, markAllNotificationsAsRead, notifySecurityAlert } from '../services/notification.service'
import nodemailer from 'nodemailer'
import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import multer from 'multer'
import { authMiddleware } from '../middlewares/auth.middleware'
import { body, validationResult } from 'express-validator'
import { sendOTP, verifyOTP } from '../services/emailService'
import passport from 'passport'
import { Strategy as GoogleStrategy } from 'passport-google-oauth20'
import { Types } from 'mongoose'
import crypto from 'crypto'

const router: any = Router()

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/temp/') // Temporary directory
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + Math.round(Math.random() * 1E9) + '.' + file.originalname.split('.').pop())
  }
})

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true)
    } else {
      cb(new Error('Only image files are allowed'))
    }
  }
})

// Passport Google Strategy
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: 'http://localhost:5001/api/auth/google/callback'
  },
  async (accessToken, refreshToken, profile, done) => {
    try {
      console.log('Google Strategy callback triggered for profile:', profile.id, profile.emails?.[0].value);
      // Check if user exists by email
      let user = await User.findOne({ email: profile.emails?.[0].value });
      if (user) {
        // Nếu user đã có, nhưng chưa có googleId, thì cập nhật googleId
        if (!user.googleId) {
          user.googleId = profile.id;
          await user.save();
        }
        return done(null, user);
      } else {
        // Nếu chưa có user, tạo mới
        user = await User.create({
          email: profile.emails?.[0].value,
          name: profile.displayName,
          googleId: profile.id,
          avatar: profile.photos?.[0].value,
          password: crypto.randomBytes(32).toString('hex')
        });
        return done(null, user);
      }
    } catch (error) {
      return done(error as Error)
    }
  }
  ));
}

// Validation middleware
const validateRegistration = [
  body('name').trim().notEmpty().withMessage('Tên không được để trống'),
  body('email').isEmail().withMessage('Email không hợp lệ'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Mật khẩu phải có ít nhất 6 ký tự')
    .matches(/\d/)
    .withMessage('Mật khẩu phải chứa ít nhất một số'),
  body('phone')
    .optional()
    .matches(/^[0-9]{10}$/)
    .withMessage('Số điện thoại không hợp lệ')
]

// Validation middleware for login
const validateLogin = [
  body('email').isEmail().withMessage('Email không hợp lệ'),
  body('password').notEmpty().withMessage('Mật khẩu không được để trống')
]

// Kiểm tra email đã tồn tại
const checkEmail: RequestHandler = async (req, res) => {
  try {
    const { email } = req.body
    const user = await User.findOne({ email })
    res.json({ available: !user })
  } catch (error) {
    console.error('Error checking email:', error)
    res.status(500).json({ message: 'Lỗi khi kiểm tra email' })
  }
}

// Gửi OTP về email
const otpStore = new Map() // { email: { otp, expiresAt } }
const sendOtp: RequestHandler = async (req, res) => {
  try {
    const { email } = req.body
    const user = await User.findOne({ email })

    if (user) {
      res.status(400).json({ message: 'Email đã được sử dụng' })
      return
    }

    const otp = await sendOTP(email)
    res.json({ message: 'OTP đã được gửi' })
  } catch (error) {
    console.error('Error sending OTP:', error)
    res.status(500).json({ message: 'Lỗi khi gửi OTP' })
  }
}

// Xác thực OTP
const verifyOtp: RequestHandler = async (req, res) => {
  try {
    const { email, otp } = req.body
    const isValid = await verifyOTP(email, otp)

    if (!isValid) {
      res.status(400).json({ message: 'OTP không hợp lệ' })
      return
    }

    res.json({ message: 'Xác thực OTP thành công' })
  } catch (error) {
    console.error('Error verifying OTP:', error)
    res.status(500).json({ message: 'Lỗi khi xác thực OTP' })
  }
}

// Đăng ký tài khoản
const register: RequestHandler = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() })
      return
    }

    const { name, email, password, phone, role } = req.body

    const existingUser = await User.findOne({ email })
    if (existingUser) {
      res.status(400).json({ message: 'Email đã được sử dụng' })
      return
    }

    const user = new User({
      name,
      email,
      password,
      phone,
      role: role || 'user' // Allow setting role, default to 'user'
    })

    await user.save()

    const token = jwt.sign(
      { _id: user._id },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    )

    await createNotification({
      userId: user._id,
      type: 'system',
      title: 'Welcome',
      message: 'Welcome to our platform!'
    });

    res.status(201).json({
      message: 'Đăng ký thành công',
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone
      }
    })
  } catch (error) {
    console.error('Registration error:', error)
    res.status(500).json({ message: 'Lỗi khi đăng ký' })
  }
}

// Đăng nhập
const login: RequestHandler = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Dữ liệu không hợp lệ',
        errors: errors.array()
      });
    }

    const { email, password } = req.body

    const user = await User.findOne({ email }).select('+password') as IUser
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Email hoặc mật khẩu không đúng'
      });
    }

    const isMatch = await user.comparePassword(password)
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Email hoặc mật khẩu không đúng'
      });
    }

    const token = jwt.sign(
      { _id: user._id },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    )

    // Send security alert for login
    try {
      const userAgent = req.get('User-Agent') || 'Unknown device';
      const ip = req.ip || req.connection.remoteAddress || 'Unknown IP';
      await notifySecurityAlert(
        user._id,
        'login',
        `${userAgent} (IP: ${ip}) lúc ${new Date().toLocaleString('vi-VN')}`
      );
    } catch (notificationError) {
      console.error('Error sending login notification:', notificationError);
      // Don't fail login if notification fails
    }

    res.json({
      success: true,
      data: {
        token,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar,
          role: user.role,
          preferences: user.preferences
        }
      }
    });
  } catch (error) {
    console.error('Login error:', error)
    res.status(500).json({
      success: false,
      message: 'Lỗi khi đăng nhập'
    });
  }
}

// Lấy thông tin user hiện tại
const getCurrentUser = async (req: Request, res: Response) => {
  try {
    console.log('🔍 getCurrentUser called, authUserId:', req.authUserId);

    if (!req.authUserId) {
      console.log('❌ No authUserId found');
      return res.status(401).json({
        success: false,
        message: 'Không có quyền truy cập'
      });
    }

    console.log('🔍 Looking for user with ID:', req.authUserId);
    const user = await User.findById(new Types.ObjectId(req.authUserId)).select('-password');

    if (!user) {
      console.log('❌ User not found in database');
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng'
      });
    }

    console.log('✅ User found:', user.name, user.email);
    const responseData = {
      success: true,
      data: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        role: user.role,
        preferences: user.preferences
      }
    };

    console.log('📤 Sending response:', responseData);
    res.json(responseData);
  } catch (error) {
    console.error('❌ Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy thông tin người dùng'
    });
  }
};

// Cập nhật thông tin người dùng
const updateProfile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.authUserId) {
      return res.status(401).json({ message: 'Không có quyền truy cập' });
    }
    const { name, phone, address, bio } = req.body;
    const user = await User.findById(new Types.ObjectId(req.authUserId));
    if (!user) {
      return res.status(404).json({ message: 'Không tìm thấy người dùng.' });
    }
    if (name) user.name = name;
    if (phone) user.phone = phone;
    if (address) user.address = address;
    if (bio) user.bio = bio;
    await user.save();
    res.json({ message: 'Cập nhật thông tin thành công.', user });
  } catch (error) {
    next(error);
  }
};

// Cập nhật avatar
const updateAvatar = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.authUserId) {
      return res.status(401).json({
        success: false,
        message: 'Không có quyền truy cập'
      });
    }

    // Handle both FormData and JSON
    let avatarData;

    if (req.file) {
      // If file uploaded via FormData
      const fs = require('fs');
      const avatarBuffer = fs.readFileSync(req.file.path);
      avatarData = `data:${req.file.mimetype};base64,${avatarBuffer.toString('base64')}`;

      // Clean up uploaded file
      fs.unlinkSync(req.file.path);
    } else if (req.body.avatar) {
      // If base64 data sent via JSON
      avatarData = req.body.avatar;
    } else {
      return res.status(400).json({
        success: false,
        message: 'Dữ liệu avatar không hợp lệ'
      });
    }

    const user = await User.findById(new Types.ObjectId(req.authUserId));
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy người dùng.'
      });
    }

    user.avatar = avatarData;
    await user.save();

    console.log('✅ Avatar updated successfully for user:', user.email);

    res.json({
      success: true,
      message: 'Cập nhật avatar thành công.',
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          avatar: user.avatar
        }
      }
    });
  } catch (error) {
    console.error('❌ Error updating avatar:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi cập nhật avatar'
    });
  }
};

// Đổi mật khẩu
const changePassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.authUserId) {
      return res.status(401).json({ message: 'Không có quyền truy cập' });
    }
    const { currentPassword, newPassword } = req.body;
    const user = await User.findById(new Types.ObjectId(req.authUserId)).select('+password');
    if (!user) {
      return res.status(404).json({ message: 'Không tìm thấy người dùng.' });
    }
    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({ message: 'Mật khẩu hiện tại không đúng.' });
    }
    user.password = newPassword;
    await user.save();
    res.json({ message: 'Đổi mật khẩu thành công.' });
  } catch (error) {
    next(error);
  }
};

// Cập nhật cài đặt người dùng
const updatePreferences = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.authUserId) {
      return res.status(401).json({ message: 'Không có quyền truy cập' });
    }
    const { theme, language, emailNotifications, pushNotifications } = req.body;
    const user = await User.findById(new Types.ObjectId(req.authUserId));
    if (!user) {
      return res.status(404).json({ message: 'Không tìm thấy người dùng.' });
    }
    if (theme) user.preferences.theme = theme;
    if (language) user.preferences.language = language;
    if (emailNotifications !== undefined) user.preferences.emailNotifications = emailNotifications;
    if (pushNotifications !== undefined) user.preferences.pushNotifications = pushNotifications;
    await user.save();
    res.json({ message: 'Cập nhật cài đặt thành công.', preferences: user.preferences });
  } catch (error) {
    next(error);
  }
};

// Lấy danh sách thông báo
const getNotifications: any = async (req: any, res: any, next: any): Promise<void> => {
  try {
    const userId = req.authUserId
    const notifications = await getUnreadNotifications(userId)
    res.json({ notifications })
  } catch (error) {
    next(error)
  }
}

// Đánh dấu thông báo đã đọc
const markNotificationRead: any = async (req: any, res: any, next: any): Promise<void> => {
  try {
    const { notificationId } = req.params
    const userId = req.authUserId
    const notification = await markNotificationAsRead(notificationId, userId)
    if (!notification) {
      res.status(404).json({ message: 'Không tìm thấy thông báo.' })
      return
    }
    res.json({ message: 'Đã đánh dấu thông báo đã đọc.' })
  } catch (error) {
    next(error)
  }
}

// Đánh dấu tất cả thông báo đã đọc
const markAllNotificationsRead: any = async (req: any, res: any, next: any): Promise<void> => {
  try {
    const userId = req.authUserId
    await markAllNotificationsAsRead(userId)
    res.json({ message: 'Đã đánh dấu tất cả thông báo đã đọc.' })
  } catch (error) {
    next(error)
  }
}

// Google OAuth routes
router.get('/google',
  passport.authenticate('google', { scope: ['profile', 'email'] })
)

router.get('/google/callback',
  passport.authenticate('google', { session: false }),
  (req: Request, res: Response): void => {
    try {
      console.log('Google callback route reached.');
      const user = req.user as any
      console.log('User authenticated successfully in callback:', user.email);
      const token = jwt.sign(
        { _id: user._id },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '7d' }
      )
      // Redirect về FE, truyền token và user qua query string
      const redirectUrl = `http://localhost:5173/login?token=${token}&name=${encodeURIComponent(user.name)}&email=${encodeURIComponent(user.email)}`
      console.log('Redirecting to frontend URL:', redirectUrl);
      res.redirect(redirectUrl)
    } catch (error) {
      console.error('Error in Google callback:', error)
      res.status(500).json({ message: 'Lỗi khi xử lý callback Google' })
    }
  }
)

// Debug endpoint to check users
router.get('/debug/users', async (req: Request, res: Response) => {
  try {
    const users = await User.find({}, 'name email role createdAt').lean();

    // Find users with similar names
    const nameGroups: { [key: string]: any[] } = {};
    users.forEach(user => {
      const name = user.name.toLowerCase().trim();
      if (!nameGroups[name]) nameGroups[name] = [];
      nameGroups[name].push(user);
    });

    const duplicateNames = Object.keys(nameGroups).filter(name => nameGroups[name].length > 1);

    // Find Khanh Duy users
    const khanhDuyUsers = users.filter(user =>
      user.name.toLowerCase().includes('khanh') &&
      user.name.toLowerCase().includes('duy')
    );

    res.json({
      success: true,
      data: {
        totalUsers: users.length,
        allUsers: users,
        duplicateNames: duplicateNames.map(name => ({
          name,
          users: nameGroups[name]
        })),
        khanhDuyUsers
      }
    });
  } catch (error) {
    console.error('Debug users error:', error);
    res.status(500).json({ success: false, message: 'Error fetching users' });
  }
});

// Fix user email - update user email
router.post('/debug/fix-user-email', async (req: Request, res: Response) => {
  try {
    const { userId, newEmail } = req.body;

    if (!userId || !newEmail) {
      return res.status(400).json({
        success: false,
        message: 'userId and newEmail are required'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const oldEmail = user.email;
    user.email = newEmail;
    await user.save();

    res.json({
      success: true,
      message: `Email updated from ${oldEmail} to ${newEmail}`,
      data: {
        userId,
        oldEmail,
        newEmail,
        userName: user.name
      }
    });
  } catch (error) {
    console.error('Fix user email error:', error);
    res.status(500).json({ success: false, message: 'Error updating user email' });
  }
});

// Routes
router.post('/check-email', checkEmail)
router.post('/send-otp', sendOtp)
router.post('/verify-otp', verifyOtp)
router.post('/register', validateRegistration, register)
router.post('/login', validateLogin, login)
router.get('/me', authMiddleware as any, getCurrentUser as any)
router.put('/profile', authMiddleware as any, updateProfile as any)
router.put('/avatar', authMiddleware as any, upload.single('avatar'), updateAvatar as any)
router.put('/change-password', authMiddleware as any, changePassword as any)
router.put('/preferences', authMiddleware as any, updatePreferences as any)
router.get('/notifications', authMiddleware as any, getNotifications as any)
router.put('/notifications/:notificationId/read', authMiddleware as any, markNotificationRead as any)
router.put('/notifications/read-all', authMiddleware as any, markAllNotificationsRead as any)

export default router