import axios from 'axios'

// Cấu hình API URL
const API_URL = process.env.NODE_ENV === 'development'
  ? (import.meta.env.VITE_API_URL || 'http://localhost:5001')
  : '';

console.log('🔧 API Configuration:', {
  baseURL: API_URL,
  environment: process.env.NODE_ENV,
  timeout: 30000
});

const api = axios.create({
  baseURL: API_URL,
  timeout: 60000, // 60 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      // Check token size to prevent 431 errors
      const tokenSize = new Blob([token]).size;
      console.log(`🔑 Token size: ${tokenSize} bytes`);

      if (tokenSize > 8000) { // 8KB limit
        console.warn('⚠️ Token is too large, might cause 431 error');
        // Optionally remove token if too large
        localStorage.removeItem('token');
        window.location.href = '/login';
        return config;
      }

      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
)

// Response interceptor with retry logic
api.interceptors.response.use(
  (response) => {
    // Chỉ reject nếu success = false, không reject nếu success = true
    if (response.data && response.data.success === false) {
      console.log('🔍 [API] Rejecting response with success=false:', response.data);
      return Promise.reject({
        response: {
          data: response.data
        }
      });
    }
    console.log('🔍 [API] Response passed through:', response.data);
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Log error for debugging
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });

    // Retry logic for network errors and timeouts
    if (
      (error.code === 'ECONNABORTED' || error.code === 'NETWORK_ERROR' || !error.response) &&
      originalRequest &&
      !originalRequest._retry &&
      originalRequest._retryCount < 3
    ) {
      originalRequest._retry = true;
      originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;

      console.log(`🔄 Retrying request (${originalRequest._retryCount}/3): ${originalRequest.url}`);

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * originalRequest._retryCount));

      return api(originalRequest);
    }

    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      // Only redirect if not already on login page
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
)

// API endpoints
export const endpoints = {
  // Auth
  auth: {
    login: '/api/auth/login',
    register: '/api/auth/register',
    logout: '/api/auth/logout',
    refreshToken: '/api/auth/refresh-token',
    checkEmail: '/api/auth/check-email',
    sendOtp: '/api/auth/send-otp',
    verifyOtp: '/api/auth/verify-otp'
  },
  // Events
  events: {
    list: '/api/events',
    detail: (id: string) => `/api/events/${id}`,
    create: '/api/events',
    update: (id: string) => `/api/events/${id}`,
    delete: (id: string) => `/api/events/${id}`,
    join: (id: string) => `/api/events/${id}/join`,
    leave: (id: string) => `/api/events/${id}/leave`,
  },
  // Campaigns
  campaigns: {
    list: '/api/campaigns',
    detail: (id: string) => `/api/campaigns/${id}`,
    create: '/api/campaigns',
    update: (id: string) => `/api/campaigns/${id}`,
    delete: (id: string) => `/api/campaigns/${id}`,
    donate: (id: string) => `/api/campaigns/${id}/donate`,
  },
  // Donations
  donations: {
    list: '/api/donations',
    detail: (id: string) => `/api/donations/${id}`,
    create: '/api/donations',
    update: (id: string) => `/api/donations/${id}`,
    delete: (id: string) => `/api/donations/${id}`,
  },
  // Posts
  posts: {
    list: '/api/posts',
    detail: (id: string) => `/api/posts/${id}`,
    create: '/api/posts',
    update: (id: string) => `/api/posts/${id}`,
    delete: (id: string) => `/api/posts/${id}`,
    like: (id: string) => `/api/posts/${id}/like`,
    unlike: (id: string) => `/api/posts/${id}/unlike`,
    comment: (id: string) => `/api/posts/${id}/comments`,
  },
  // Users
  users: {
    profile: '/api/users/profile',
    update: '/api/users/profile',
    changePassword: '/api/users/change-password',
    uploadAvatar: '/api/users/avatar',
  },
  // Organizations
  organizations: {
    list: '/api/organizations',
    detail: (id: string) => `/api/organizations/${id}`,
    create: '/api/organizations',
    update: (id: string) => `/api/organizations/${id}`,
    delete: (id: string) => `/api/organizations/${id}`,
  },
  // Home
  home: {
    statistics: '/api/home/<USER>',
    featuredEvents: '/api/home/<USER>',
    featuredCampaigns: '/api/home/<USER>',
    latestPosts: '/api/home/<USER>',
  },
  // Admin
  admin: {
    users: {
      list: '/api/admin/users',
      detail: (id: string) => `/api/admin/users/${id}`,
      updateStatus: (id: string) => `/api/admin/users/${id}/status`,
      updateRole: (id: string) => `/api/admin/users/${id}/role`,
      delete: (id: string) => `/api/admin/users/${id}`,
    },
    posts: {
      list: '/api/admin/posts',
      delete: (id: string) => `/api/admin/posts/${id}`,
    },
    campaigns: {
      list: '/api/admin/campaigns',
    },
    donations: {
      list: '/api/admin/donations',
    },
    dashboard: {
      stats: '/api/admin/dashboard/stats',
    },
  },
}

export default api