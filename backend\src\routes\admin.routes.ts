import { Router } from 'express';
import * as adminController from '../controllers/admin.controller';
import { verifyToken, requireAdmin, authMiddleware } from '../middlewares/auth.middleware';
import { query, param, body } from 'express-validator';
import { validateRequest } from '../middlewares/validation.middleware';
import {
  getAllReviews,
  respondToReview,
  deleteReview,
  getReviewStats,
  getReviewsByEvent,
  toggleReviewVisibility
} from '../controllers/reviewAdminController';

const router = Router();

// Validation middleware
const getPostsValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page phải là số nguyên dương'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit phải từ 1-100'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Từ khóa tìm kiếm tối đa 100 ký tự'),
  query('status')
    .optional()
    .isIn(['all', 'reported', 'recent'])
    .withMessage('Status phải là all, reported hoặc recent')
];

const deletePostValidation = [
  param('id')
    .isMongoId()
    .withMessage('ID bài viết không hợp lệ')
];

/**
 * @route GET /admin/posts
 * @desc Lấy danh sách tất cả bài viết cho admin
 * @access Private (Admin only)
 * @query page - Trang hiện tại (default: 1)
 * @query limit - Số bài viết mỗi trang (default: 20, max: 100)
 * @query search - Từ khóa tìm kiếm
 * @query status - Lọc theo trạng thái (all/reported/recent)
 */
router.get('/posts',
  verifyToken,
  requireAdmin,
  getPostsValidation,
  validateRequest,
  adminController.getAllPosts
);

/**
 * @route DELETE /admin/posts/:id
 * @desc Xóa bài viết (chỉ admin)
 * @access Private (Admin only)
 */
router.delete('/posts/:id',
  verifyToken,
  requireAdmin,
  deletePostValidation,
  validateRequest,
  adminController.deletePost
);

/**
 * @route DELETE /admin/posts-test/:id
 * @desc Xóa bài viết (test endpoint không cần auth)
 * @access Public (for testing)
 */
router.delete('/posts-test/:id',
  deletePostValidation,
  validateRequest,
  adminController.deletePost
);

/**
 * @route DELETE /admin/posts/:postId/comments/:commentId
 * @desc Xóa comment (chỉ admin)
 * @access Private (Admin only)
 */
router.delete('/posts/:postId/comments/:commentId',
  verifyToken,
  requireAdmin,
  param('postId').isMongoId().withMessage('ID bài viết không hợp lệ'),
  param('commentId').isMongoId().withMessage('ID comment không hợp lệ'),
  validateRequest,
  adminController.deleteComment
);

/**
 * @route POST /admin/posts/:id/reports/resolve
 * @desc Xử lý báo cáo bài viết
 * @access Private (Admin only)
 */
router.post('/posts/:id/reports/resolve',
  verifyToken,
  requireAdmin,
  param('id').isMongoId().withMessage('ID bài viết không hợp lệ'),
  body('action').isIn(['dismiss', 'remove']).withMessage('Action phải là dismiss hoặc remove'),
  validateRequest,
  adminController.resolveReport
);

// Donations management
router.get('/donations', verifyToken, requireAdmin, adminController.getAllDonations);

// Campaigns management
router.get('/campaigns', verifyToken, requireAdmin, adminController.getAllCampaigns);

// Users management
router.get('/users', verifyToken, requireAdmin, adminController.getAllUsers);
router.get('/users/:id', verifyToken, requireAdmin, adminController.getUserById);
router.put('/users/:id/status', verifyToken, requireAdmin, adminController.updateUserStatus);
router.put('/users/:id/role', verifyToken, requireAdmin, adminController.updateUserRole);
router.delete('/users/:id', verifyToken, requireAdmin, adminController.deleteUser);

// Online status tracking
router.get('/online-status', verifyToken, requireAdmin, adminController.getOnlineStatus);

// Test users endpoint with new auth middleware (for development only)
router.get('/users-auth-test', authMiddleware as any, adminController.getAllUsers);

// Dashboard stats
router.get('/dashboard/stats', verifyToken, requireAdmin, adminController.getDashboardStats);

// Test dashboard stats (no auth for development)
router.get('/dashboard/stats-test', adminController.getDashboardStats);

// Test posts endpoint (no auth for development)
router.get('/posts-test', adminController.getPostsTest);

// Test delete post endpoint (no auth for development)
router.delete('/posts-test/:id', adminController.deletePostTest);

// Simple test delete endpoint without validation
router.delete('/posts-simple/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('🗑️ [Simple Test] Deleting post:', id);

    // Import Post model
    const { Post } = require('../models/Post');

    // Find and delete the post
    const deletedPost = await Post.findByIdAndDelete(id);

    if (!deletedPost) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy bài viết'
      });
    }

    console.log('✅ [Simple Test] Post deleted successfully');

    res.json({
      success: true,
      message: 'Đã xóa bài viết thành công (simple test)',
      data: {
        deletedPost: {
          _id: deletedPost._id,
          content: deletedPost.content?.substring(0, 50) + '...'
        }
      }
    });
  } catch (error: any) {
    console.error('❌ [Simple Test] Error deleting post:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi xóa bài viết',
      error: error.message
    });
  }
});

/**
 * @route GET /admin/comment-reports
 * @desc Lấy danh sách báo cáo comment
 * @access Private (Admin only)
 * @query page - Trang hiện tại (default: 1)
 * @query limit - Số báo cáo mỗi trang (default: 20)
 * @query status - Lọc theo trạng thái (pending/resolved/dismissed/all)
 */
router.get('/comment-reports',
  verifyToken,
  requireAdmin,
  query('page').optional().isInt({ min: 1 }).withMessage('Page phải là số nguyên dương'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit phải từ 1-100'),
  query('status').optional().isIn(['pending', 'resolved', 'dismissed', 'all']).withMessage('Status không hợp lệ'),
  validateRequest,
  adminController.getCommentReports
);

/**
 * @route POST /admin/comment-reports/:reportId/resolve
 * @desc Xử lý báo cáo comment
 * @access Private (Admin only)
 */
router.post('/comment-reports/:reportId/resolve',
  verifyToken,
  requireAdmin,
  param('reportId').isMongoId().withMessage('ID báo cáo không hợp lệ'),
  body('action').isIn(['dismiss', 'remove_comment']).withMessage('Action phải là dismiss hoặc remove_comment'),
  validateRequest,
  adminController.resolveCommentReport
);

/**
 * @route GET /admin/comment-reports/:reportId/test
 * @desc Test endpoint để debug comment report resolution
 * @access Private (Admin only)
 */
router.get('/comment-reports/:reportId/test',
  verifyToken,
  requireAdmin,
  param('reportId').isMongoId().withMessage('ID báo cáo không hợp lệ'),
  validateRequest,
  adminController.testCommentReportResolution
);

/**
 * @route GET /admin/posts/:postId/comments
 * @desc Lấy danh sách comment của bài viết với thông tin báo cáo
 * @access Private (Admin only)
 */
router.get('/posts/:postId/comments',
  verifyToken,
  requireAdmin,
  param('postId').isMongoId().withMessage('ID bài viết không hợp lệ'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page phải là số nguyên dương'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit phải từ 1-100'),
  validateRequest,
  adminController.getPostComments
);

// Review management routes
/**
 * @route GET /admin/reviews
 * @desc Lấy tất cả đánh giá (admin)
 * @access Private (Admin only)
 */
router.get('/reviews',
  verifyToken,
  requireAdmin,
  query('page').optional().isInt({ min: 1 }).withMessage('Page phải là số nguyên dương'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit phải từ 1-100'),
  query('search').optional().trim().isLength({ max: 100 }).withMessage('Từ khóa tìm kiếm tối đa 100 ký tự'),
  query('rating').optional().isIn(['1', '2', '3', '4', '5', 'all']).withMessage('Rating không hợp lệ'),
  query('eventId').optional().isMongoId().withMessage('Event ID không hợp lệ'),
  validateRequest,
  getAllReviews
);

/**
 * @route POST /admin/reviews/:id/respond
 * @desc Phản hồi đánh giá (admin)
 * @access Private (Admin only)
 */
router.post('/reviews/:id/respond',
  verifyToken,
  requireAdmin,
  param('id').isMongoId().withMessage('Review ID không hợp lệ'),
  body('response').trim().isLength({ min: 1, max: 1000 }).withMessage('Phản hồi phải từ 1-1000 ký tự'),
  validateRequest,
  respondToReview
);

/**
 * @route DELETE /admin/reviews/:id
 * @desc Xóa đánh giá (admin)
 * @access Private (Admin only)
 */
router.delete('/reviews/:id',
  verifyToken,
  requireAdmin,
  param('id').isMongoId().withMessage('Review ID không hợp lệ'),
  validateRequest,
  deleteReview
);

/**
 * @route GET /admin/reviews/stats
 * @desc Lấy thống kê đánh giá (admin)
 * @access Private (Admin only)
 */
router.get('/reviews/stats',
  verifyToken,
  requireAdmin,
  getReviewStats
);

/**
 * @route GET /admin/events/:eventId/reviews
 * @desc Lấy đánh giá theo sự kiện (admin)
 * @access Private (Admin only)
 */
router.get('/events/:eventId/reviews',
  verifyToken,
  requireAdmin,
  param('eventId').isMongoId().withMessage('Event ID không hợp lệ'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page phải là số nguyên dương'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit phải từ 1-100'),
  validateRequest,
  getReviewsByEvent
);

export default router;