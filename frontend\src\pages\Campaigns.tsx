import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { io } from 'socket.io-client';
import { motion, AnimatePresence } from 'framer-motion';
import { Heart, Target, TrendingUp, Clock, CheckCircle, XCircle, Search, Filter, Tag } from 'lucide-react';
import api from '../services/api';
import './CampaignStyles.css';

// Constants
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

// Types
interface Campaign {
  _id: string;
  title: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  startDate: string;
  endDate: string;
  status: 'active' | 'completed' | 'cancelled';
  images: string[];
  category?: string;
}

const Campaigns: React.FC = () => {
  const navigate = useNavigate();

  // State declarations
  const [allCampaigns, setAllCampaigns] = useState<Campaign[]>([]);
  const [activeCampaigns, setActiveCampaigns] = useState<Campaign[]>([]);
  const [completedCampaigns, setCompletedCampaigns] = useState<Campaign[]>([]);
  const [filteredCampaigns, setFilteredCampaigns] = useState<Campaign[]>([]);
  const [currentView, setCurrentView] = useState<'active' | 'completed'>('active');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [categories, setCategories] = useState<string[]>([]);

  // Fetch campaigns function
  const fetchCampaigns = useCallback(async () => {
    try {
      setLoading(true);
      console.log('💰 [Real API] Loading campaigns data...');

      // Fetch real campaigns data from API
      try {
        const response = await api.get('/api/campaigns');

        if (response.data.success) {
          const apiCampaigns = response.data.data || [];
          console.log('✅ [Real API] Campaigns loaded from server:', Array.isArray(apiCampaigns) ? apiCampaigns.length : 0);

          // Ensure apiCampaigns is an array
          if (!Array.isArray(apiCampaigns)) {
            console.warn('⚠️ API returned non-array data:', apiCampaigns);
            throw new Error('Dữ liệu chiến dịch không hợp lệ');
          }

          setAllCampaigns(apiCampaigns);
          setActiveCampaigns(apiCampaigns.filter((c: Campaign) => c.status === 'active'));
          setCompletedCampaigns(apiCampaigns.filter((c: Campaign) => c.status === 'completed'));

          // Extract unique categories
          const uniqueCategories = [...new Set(apiCampaigns
            .map((c: Campaign) => c.category)
            .filter(Boolean)
          )] as string[];
          setCategories(uniqueCategories);

          console.log('✅ [Real API] Campaigns loaded successfully:', apiCampaigns.length);
        } else {
          throw new Error(response.data.message || 'Không thể tải danh sách chiến dịch');
        }
      } catch (apiError: any) {
        console.warn('⚠️ API Error, using empty fallback:', apiError);

        // Fallback to empty data
        setAllCampaigns([]);
        setActiveCampaigns([]);
        setCompletedCampaigns([]);

        const errorMessage = apiError?.response?.data?.message || 'Không thể kết nối đến server';
        console.error(`❌ Campaigns API Error: ${errorMessage}`);
        setError('Không thể tải danh sách chiến dịch từ server');
      }

    } catch (error) {
      console.error('❌ Error loading campaigns:', error);
      setError('Không thể tải danh sách chiến dịch');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch campaigns on mount
  useEffect(() => {
    fetchCampaigns();
  }, [fetchCampaigns]);

  // Filter campaigns based on search and category
  useEffect(() => {
    const currentCampaigns = currentView === 'active' ? activeCampaigns : completedCampaigns;

    let filtered = currentCampaigns;

    // Filter by search term
    if (searchTerm.trim()) {
      filtered = filtered.filter(campaign =>
        campaign.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        campaign.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(campaign => campaign.category === selectedCategory);
    }

    setFilteredCampaigns(filtered);
  }, [activeCampaigns, completedCampaigns, currentView, searchTerm, selectedCategory]);

  // Lắng nghe realtime cập nhật campaign
  useEffect(() => {
    const socket = io(API_URL);
    socket.on('campaign_updated', () => {
      // Khi có sự kiện cập nhật, fetch lại danh sách
      fetchCampaigns();
    });
    return () => {
      socket.disconnect();
    };
  }, [fetchCampaigns]);

  const handleCardClick = (campaign: Campaign) => {
    navigate(`/campaigns/${campaign._id}`);
  };

  const calculateProgress = (current: number, target: number): number => {
    if (target <= 0) return 0;
    const progress = (current / target) * 100;
    return Math.min(progress, 100);
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-purple-100 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">Đang tải chiến dịch...</p>
        </motion.div>
      </div>
    );
  }

  if (error && !allCampaigns.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-purple-100 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center bg-white/80 backdrop-blur-md rounded-2xl p-8 shadow-xl"
        >
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <p className="text-gray-700 text-lg">{error}</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-purple-100 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full opacity-20 blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl mb-4 shadow-xl"
          >
            <Heart className="w-6 h-6 text-white" />
          </motion.div>
          <h1 className="campaign-heading text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-3">
            Chiến dịch quyên góp
          </h1>
          <p className="campaign-text text-gray-600 text-base md:text-lg max-w-2xl mx-auto">
            Cùng nhau lan tỏa yêu thương và tạo nên những thay đổi tích cực cho cộng đồng
          </p>
        </motion.div>

        {/* Search and Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-8"
        >
          <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/20">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search Input */}
              <div className="flex-1">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Tìm kiếm chiến dịch..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 bg-white/50 backdrop-blur-sm"
                  />
                </div>
              </div>

              {/* Category Filter */}
              {categories.length > 0 && (
                <div className="lg:w-64">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Filter className="h-5 w-5 text-gray-400" />
                    </div>
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="block w-full pl-10 pr-8 py-3 border border-gray-300 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 bg-white/50 backdrop-blur-sm appearance-none"
                    >
                      <option value="">Tất cả danh mục</option>
                      {categories.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Active Filters */}
            {(searchTerm || selectedCategory) && (
              <div className="mt-4 flex flex-wrap gap-2">
                {searchTerm && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="category-badge inline-flex items-center px-3 py-1.5 rounded-full text-sm bg-purple-100 text-purple-800"
                  >
                    <Search className="w-3 h-3 mr-1.5" />
                    <span>"{searchTerm}"</span>
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-2 hover:text-purple-600"
                    >
                      ×
                    </button>
                  </motion.div>
                )}
                {selectedCategory && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="category-badge inline-flex items-center px-3 py-1.5 rounded-full text-sm bg-pink-100 text-pink-800"
                  >
                    <Tag className="w-3 h-3 mr-1.5" />
                    <span>{selectedCategory}</span>
                    <button
                      onClick={() => setSelectedCategory('')}
                      className="ml-2 hover:text-pink-600"
                    >
                      ×
                    </button>
                  </motion.div>
                )}
              </div>
            )}
          </div>
        </motion.div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="flex justify-center mb-12"
        >
          <div className="bg-white/80 backdrop-blur-md rounded-2xl p-2 shadow-xl border border-white/20">
            <div className="flex space-x-2">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  currentView === 'active'
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
                }`}
                onClick={() => setCurrentView('active')}
              >
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4" />
                  <span>Đang gây quỹ ({activeCampaigns.length})</span>
                </div>
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  currentView === 'completed'
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
                }`}
                onClick={() => setCurrentView('completed')}
              >
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4" />
                  <span>Đã kết thúc ({completedCampaigns.length})</span>
                </div>
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Campaign Grid */}
        {/* Results Count */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mb-6"
        >
          <p className="text-gray-600 text-center">
            Hiển thị <span className="font-semibold text-purple-600">{filteredCampaigns.length}</span> chiến dịch
            {searchTerm && ` cho "${searchTerm}"`}
            {selectedCategory && ` trong danh mục "${selectedCategory}"`}
          </p>
        </motion.div>

        {/* Campaign Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <AnimatePresence>
            {filteredCampaigns.map((campaign: Campaign, index) => (
              <motion.div
                key={campaign._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/20 overflow-hidden cursor-pointer group"
                onClick={() => handleCardClick(campaign)}
              >
                {/* Image */}
                <div className="relative h-48 overflow-hidden">
                  {campaign.images && campaign.images.length > 0 ? (
                    <motion.img
                      whileHover={{ scale: 1.1 }}
                      transition={{ duration: 0.3 }}
                      src={`${API_URL}/${campaign.images[0]}`}
                      alt={campaign.title || 'Campaign Image'}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-purple-200 to-pink-200 flex items-center justify-center">
                      <Heart className="w-12 h-12 text-purple-400" />
                    </div>
                  )}

                  {/* Category Badge */}
                  {campaign.category && (
                    <div className="category-badge absolute top-4 left-4 px-3 py-1.5 rounded-full text-xs bg-purple-600 text-white shadow-lg">
                      <div className="flex items-center space-x-1.5">
                        <Tag className="w-3 h-3" />
                        <span>{campaign.category}</span>
                      </div>
                    </div>
                  )}

                  {/* Status Badge */}
                  <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-semibold text-white shadow-lg ${
                    campaign.status === 'active'
                      ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                      : campaign.status === 'completed'
                      ? 'bg-gradient-to-r from-blue-500 to-indigo-500'
                      : 'bg-gradient-to-r from-red-500 to-pink-500'
                  }`}>
                    {campaign.status === 'active' && 'Đang gây quỹ'}
                    {campaign.status === 'completed' && 'Đã hoàn thành'}
                    {campaign.status === 'cancelled' && 'Đã hủy'}
                  </div>

                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Title */}
                  <h3 className="campaign-heading text-xl font-bold text-gray-800 mb-3 line-clamp-2 group-hover:text-purple-600 transition-colors duration-300">
                    {campaign.title}
                  </h3>

                  {/* Progress Section */}
                  <div className="space-y-3">
                    {/* Progress Bar */}
                    <div className="relative">
                      <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${calculateProgress(campaign.currentAmount, campaign.targetAmount)}%` }}
                          transition={{ duration: 1, delay: index * 0.1 + 0.5 }}
                          className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                        />
                      </div>
                      <div className="absolute -top-1 right-0 transform translate-x-1/2">
                        <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full border-2 border-white shadow-lg"></div>
                      </div>
                    </div>

                    {/* Amount Info */}
                    <div className="campaign-text flex justify-between items-center text-sm">
                      <div className="flex items-center space-x-1">
                        <Target className="w-4 h-4 text-purple-500" />
                        <span className="font-semibold text-purple-600">
                          {formatCurrency(campaign.currentAmount)}
                        </span>
                      </div>
                      <span className="text-gray-500">
                        / {formatCurrency(campaign.targetAmount)}
                      </span>
                    </div>

                    {/* Progress Percentage */}
                    <div className="campaign-text flex items-center justify-between">
                      <div className="flex items-center space-x-1">
                        <TrendingUp className="w-4 h-4 text-green-500" />
                        <span className="text-sm font-medium text-green-600">
                          {calculateProgress(campaign.currentAmount, campaign.targetAmount).toFixed(1)}% hoàn thành
                        </span>
                      </div>

                      {campaign.status === 'active' && (
                        <div className="flex items-center space-x-1 text-gray-500">
                          <Clock className="w-4 h-4" />
                          <span className="text-xs">
                            {format(new Date(campaign.endDate), 'dd/MM/yyyy')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Empty State */}
        {filteredCampaigns.length === 0 && !loading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center py-16"
          >
            <div className="bg-white/80 backdrop-blur-md rounded-2xl p-12 shadow-xl border border-white/20 max-w-md mx-auto">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="w-20 h-20 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-6"
              >
                {searchTerm || selectedCategory ? (
                  <Search className="w-10 h-10 text-purple-400" />
                ) : (
                  <Heart className="w-10 h-10 text-purple-400" />
                )}
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-700 mb-3">
                {searchTerm || selectedCategory
                  ? 'Không tìm thấy chiến dịch phù hợp'
                  : currentView === 'active'
                    ? 'Chưa có chiến dịch đang gây quỹ'
                    : 'Chưa có chiến dịch đã kết thúc'
                }
              </h3>
              <p className="text-gray-500">
                {searchTerm || selectedCategory
                  ? 'Hãy thử tìm kiếm với từ khóa khác hoặc thay đổi bộ lọc'
                  : currentView === 'active'
                    ? 'Hãy quay lại sau để khám phá những chiến dịch mới'
                    : 'Các chiến dịch đã hoàn thành sẽ xuất hiện ở đây'
                }
              </p>
              {(searchTerm || selectedCategory) && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('');
                  }}
                  className="mt-4 px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-300"
                >
                  Xóa bộ lọc
                </motion.button>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Campaigns;