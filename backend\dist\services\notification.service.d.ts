import { Types } from 'mongoose';
interface CreateNotificationParams {
    userId: Types.ObjectId;
    type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
    title: string;
    message: string;
    data?: {
        campaignId?: Types.ObjectId;
        donationId?: Types.ObjectId;
        eventId?: Types.ObjectId;
        postId?: Types.ObjectId;
        commentId?: Types.ObjectId;
        reportId?: Types.ObjectId;
        amount?: number;
        status?: string;
        reason?: string;
        actionType?: 'created' | 'updated' | 'deleted' | 'cancelled' | 'completed' | 'reminder';
        relatedUserId?: Types.ObjectId;
        relatedUserName?: string;
        badgeType?: string;
        badgeLevel?: number;
    };
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    sendEmail?: boolean;
}
export declare const createNotification: (params: CreateNotificationParams) => Promise<any>;
export declare const getUnreadNotifications: (userId: Types.ObjectId) => Promise<any[]>;
export declare const markNotificationAsRead: (notificationId: string, userId: Types.ObjectId) => Promise<any>;
export declare const createReportNotificationForAdmins: (reportId: Types.ObjectId, postId: Types.ObjectId, reporterName: string, reason: string) => Promise<any[]>;
export declare const markAllNotificationsAsRead: (userId: Types.ObjectId) => Promise<import("mongoose").UpdateWriteOpResult>;
export declare const createCommentReportNotificationForAdmins: (reportId: Types.ObjectId, commentId: Types.ObjectId, postId: Types.ObjectId, reporterName: string, reason: string) => Promise<any[]>;
export declare const deleteNotification: (notificationId: string, userId: Types.ObjectId) => Promise<any>;
export declare const notifyNewComment: (postOwnerId: Types.ObjectId, commenterName: string, postId: Types.ObjectId) => Promise<any>;
export declare const notifyCampaignDeleted: (donorIds: Types.ObjectId[], campaignTitle: string, campaignId: Types.ObjectId, reason?: string) => Promise<any[]>;
export declare const notifyEventDeleted: (participantIds: Types.ObjectId[], eventTitle: string, eventId: Types.ObjectId, reason?: string) => Promise<any[]>;
export declare const notifyPostDeleted: (userId: Types.ObjectId, postTitle: string, reason: string) => Promise<any>;
export declare const notifyCommentDeleted: (userId: Types.ObjectId, commentContent: string, reason: string) => Promise<any>;
export declare const notifyNewCampaign: (userIds: Types.ObjectId[], campaignTitle: string, campaignId: Types.ObjectId) => Promise<any[]>;
export declare const notifyNewEvent: (userIds: Types.ObjectId[], eventTitle: string, eventId: Types.ObjectId) => Promise<any[]>;
export declare const notifyAdminMessage: (userIds: Types.ObjectId[], title: string, message: string) => Promise<any[]>;
export declare const notifyDonationSuccess: (donorEmail: string, donorName: string, amount: number, campaignTitle: string, campaignId: Types.ObjectId, transactionId: string, isAnonymous?: boolean, userId?: Types.ObjectId) => Promise<void>;
export declare const notifyCampaignMilestone: (donorIds: Types.ObjectId[], campaignTitle: string, campaignId: Types.ObjectId, milestone: number, currentAmount: number, targetAmount: number) => Promise<any[]>;
export declare const notifyCampaignDeadline: (userIds: Types.ObjectId[], campaignTitle: string, campaignId: Types.ObjectId, daysLeft: number) => Promise<any[]>;
export declare const notifyEventReminder: (participantIds: Types.ObjectId[], eventTitle: string, eventId: Types.ObjectId, eventDate: Date, reminderType: "7days" | "1day" | "1hour") => Promise<any[]>;
export declare const notifySecurityAlert: (userId: Types.ObjectId, alertType: "login" | "password_change" | "email_change", details: string) => Promise<any>;
export {};
//# sourceMappingURL=notification.service.d.ts.map