import React, { useState, useRef } from 'react';
import { Image, X } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface PostFormProps {
  onPost: (data: { content: string; media: File[] }) => Promise<void>;
}

const PostForm: React.FC<PostFormProps> = ({ onPost }) => {
  const { user } = useAuth();
  const [content, setContent] = useState('');
  const [media, setMedia] = useState<File[]>([]);
  const [preview, setPreview] = useState<string[]>([]);
  const [isPosting, setIsPosting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleMediaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setMedia(prev => [...prev, ...files]);

    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeMedia = (index: number) => {
    setMedia(prev => prev.filter((_, i) => i !== index));
    setPreview(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim() && media.length === 0) return;
    if (isPosting) return; // Prevent double submission

    setIsPosting(true);
    try {
      await onPost({ content, media });
      // Clear form only after successful post
      setContent('');
      setMedia([]);
      setPreview([]);
    } catch (error) {
      console.error('Error in PostForm:', error);
    } finally {
      setIsPosting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      {/* User Avatar and Input */}
      <div className="flex items-start space-x-3 mb-4">
        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden">
          {user?.avatar ? (
            <img src={user.avatar} alt={user.name} className="w-full h-full object-cover" />
          ) : (
            <span className="text-sm font-medium text-gray-600">
              {user?.name?.charAt(0).toUpperCase()}
            </span>
          )}
        </div>
        <div className="flex-1">
          <textarea
            id="post-content"
            name="content"
            className="w-full resize-none border-0 text-lg placeholder-gray-500 focus:ring-0 focus:outline-none"
            rows={3}
            placeholder="Bạn đang nghĩ gì?"
            value={content}
            onChange={e => setContent(e.target.value)}
          />
        </div>
      </div>

      {/* Media Preview */}
      {preview.length > 0 && (
        <div className="mb-4 border border-gray-200 rounded-lg p-4 relative">
          <div className={`grid gap-2 ${preview.length === 1 ? 'grid-cols-1' : 'grid-cols-2'}`}>
            {preview.map((url, index) => (
              <div key={index} className="relative">
                <img
                  src={url}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-32 object-cover rounded-lg"
                />
                <button
                  onClick={() => removeMedia(index)}
                  className="absolute top-1 right-1 p-1 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70"
                >
                  <X className="h-3 w-3 text-white" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center space-x-2 px-4 py-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Image className="h-5 w-5 text-green-500" />
            <span className="text-gray-700 font-medium">Ảnh/video</span>
          </button>
        </div>

        <button
          onClick={handleSubmit}
          disabled={isPosting || (!content.trim() && media.length === 0)}
          className={`px-6 py-2 rounded-lg font-medium transition-colors ${
            isPosting || (!content.trim() && media.length === 0)
              ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isPosting ? 'Đang đăng...' : 'Đăng'}
        </button>
      </div>

      {/* Hidden File Input */}
      <input
        id="post-media-upload"
        name="media"
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,video/*"
        onChange={handleMediaChange}
        className="hidden"
      />
    </div>
  );
};

export default PostForm;