import React, { useState, useEffect } from 'react';
import { X, User, Phone, Mail, Calendar, MapPin, Briefcase, Heart, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface EventRegistrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  eventId: string;
  eventTitle: string;
  onRegistrationSuccess: () => void;
}

interface ParticipantInfo {
  fullName: string;
  phone: string;
  email: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other' | '';
  address: string;
  occupation: string;
  organization: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  dietaryRestrictions: string;
  medicalConditions: string;
  experience: string;
  motivation: string;
}

const EventRegistrationModal: React.FC<EventRegistrationModalProps> = ({
  isOpen,
  onClose,
  eventId,
  eventTitle,
  onRegistrationSuccess
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [notes, setNotes] = useState('');
  
  const [participantInfo, setParticipantInfo] = useState<ParticipantInfo>({
    fullName: '',
    phone: '',
    email: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    occupation: '',
    organization: '',
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    },
    dietaryRestrictions: '',
    medicalConditions: '',
    experience: '',
    motivation: ''
  });

  useEffect(() => {
    if (isOpen && user) {
      // Pre-fill with user data
      setParticipantInfo(prev => ({
        ...prev,
        fullName: user.name || '',
        email: user.email || ''
      }));
    }
  }, [isOpen, user]);

  const handleInputChange = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setParticipantInfo(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof ParticipantInfo],
          [child]: value
        }
      }));
    } else {
      setParticipantInfo(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const validateStep1 = () => {
    const required = ['fullName', 'phone', 'email'];
    for (const field of required) {
      if (!participantInfo[field as keyof ParticipantInfo] || !participantInfo[field as keyof ParticipantInfo].toString().trim()) {
        toast.error(`Vui lòng nhập ${field === 'fullName' ? 'họ tên' : field === 'phone' ? 'số điện thoại' : 'email'}`);
        return false;
      }
    }

    // Validate email
    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
    if (!emailRegex.test(participantInfo.email.trim())) {
      toast.error('Email không hợp lệ');
      return false;
    }

    // Validate phone - allow Vietnamese phone numbers
    const phoneRegex = /^[0-9+\-\s()]+$/;
    const cleanPhone = participantInfo.phone.replace(/\s/g, '');
    if (!phoneRegex.test(participantInfo.phone) || cleanPhone.length < 10) {
      toast.error('Số điện thoại không hợp lệ (ít nhất 10 số)');
      return false;
    }

    // Validate name length
    if (participantInfo.fullName.trim().length < 2) {
      toast.error('Họ tên phải có ít nhất 2 ký tự');
      return false;
    }

    return true;
  };

  const validateStep2 = () => {
    if (!participantInfo.motivation.trim()) {
      toast.error('Vui lòng chia sẻ lý do tham gia');
      return false;
    }
    return true;
  };

  const handleNext = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (!validateStep1() || !validateStep2()) return;

    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      // Clean and prepare participant info
      const cleanedParticipantInfo = {
        ...participantInfo,
        fullName: participantInfo.fullName.trim(),
        phone: participantInfo.phone.trim(),
        email: participantInfo.email.trim().toLowerCase(),
        dateOfBirth: participantInfo.dateOfBirth && participantInfo.dateOfBirth.trim()
          ? new Date(participantInfo.dateOfBirth)
          : undefined,
        gender: participantInfo.gender || undefined,
        address: participantInfo.address?.trim() || undefined,
        occupation: participantInfo.occupation?.trim() || undefined,
        organization: participantInfo.organization?.trim() || undefined,
        emergencyContact: participantInfo.emergencyContact?.name?.trim() ? {
          name: participantInfo.emergencyContact.name.trim(),
          phone: participantInfo.emergencyContact.phone?.trim() || '',
          relationship: participantInfo.emergencyContact.relationship?.trim() || ''
        } : undefined,
        dietaryRestrictions: participantInfo.dietaryRestrictions?.trim() || undefined,
        medicalConditions: participantInfo.medicalConditions?.trim() || undefined,
        experience: participantInfo.experience?.trim() || undefined,
        motivation: participantInfo.motivation?.trim() || ''
      };

      const registrationData = {
        notes: notes?.trim() || '',
        participantInfo: cleanedParticipantInfo
      };

      console.log('Sending registration data:', registrationData);

      const response = await axios.post(
        `${API_URL}/api/events/${eventId}/register`,
        registrationData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        toast.success('Đăng ký sự kiện thành công!');
        onRegistrationSuccess();
        onClose();
        setCurrentStep(1);
        // Reset form
        setParticipantInfo({
          fullName: user?.name || '',
          phone: '',
          email: user?.email || '',
          dateOfBirth: '',
          gender: '',
          address: '',
          occupation: '',
          organization: '',
          emergencyContact: {
            name: '',
            phone: '',
            relationship: ''
          },
          dietaryRestrictions: '',
          medicalConditions: '',
          experience: '',
          motivation: ''
        });
        setNotes('');
      }
    } catch (error: any) {
      console.error('Error registering for event:', error);

      // More detailed error handling
      if (error.response?.status === 400) {
        toast.error(error.response.data.message || 'Dữ liệu đăng ký không hợp lệ');
      } else if (error.response?.status === 401) {
        toast.error('Vui lòng đăng nhập lại');
      } else if (error.response?.status === 409) {
        toast.error('Bạn đã đăng ký sự kiện này rồi');
      } else if (error.response?.status === 500) {
        toast.error('Lỗi server. Vui lòng thử lại sau');
      } else {
        toast.error(error.response?.data?.message || 'Có lỗi xảy ra khi đăng ký');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Đăng ký tham gia sự kiện</h3>
            <p className="text-sm text-gray-600">{eventTitle}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-center">
            {[1, 2].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                }`}>
                  {step}
                </div>
                <span className={`ml-2 text-sm ${step <= currentStep ? 'text-blue-600' : 'text-gray-500'}`}>
                  {step === 1 ? 'Thông tin cơ bản' : 'Lý do tham gia'}
                </span>
                {step < 2 && <div className={`w-16 h-0.5 mx-4 ${step < currentStep ? 'bg-blue-600' : 'bg-gray-200'}`} />}
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <div className="p-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Thông tin cơ bản</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Họ và tên *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      id="participant-fullname"
                      name="fullName"
                      type="text"
                      value={participantInfo.fullName}
                      onChange={(e) => handleInputChange('fullName', e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Nhập họ và tên"
                      required
                      autoComplete="name"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Số điện thoại *
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      id="participant-phone"
                      name="phone"
                      type="tel"
                      value={participantInfo.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Nhập số điện thoại"
                      required
                      autoComplete="tel"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      id="participant-email"
                      name="email"
                      type="email"
                      value={participantInfo.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Nhập email"
                      required
                      autoComplete="email"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ngày sinh
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      id="participant-dob"
                      name="dateOfBirth"
                      type="date"
                      value={participantInfo.dateOfBirth}
                      onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      autoComplete="bday"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Giới tính
                  </label>
                  <select
                    id="participant-gender"
                    name="gender"
                    value={participantInfo.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    autoComplete="sex"
                  >
                    <option value="">Chọn giới tính</option>
                    <option value="male">Nam</option>
                    <option value="female">Nữ</option>
                    <option value="other">Khác</option>
                  </select>
                </div>


              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Địa chỉ
                </label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
                  <textarea
                    id="participant-address"
                    name="address"
                    value={participantInfo.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    rows={2}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Nhập địa chỉ"
                    autoComplete="street-address"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Motivation */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Lý do tham gia</h4>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tại sao bạn muốn tham gia sự kiện này? *
                </label>
                <div className="relative">
                  <Heart className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
                  <textarea
                    id="participant-motivation"
                    name="motivation"
                    value={participantInfo.motivation}
                    onChange={(e) => handleInputChange('motivation', e.target.value)}
                    rows={5}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Chia sẻ lý do và động lực tham gia sự kiện này. Điều gì khiến bạn quan tâm đến hoạt động thiện nguyện này?"
                    required
                  />
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Thông tin này giúp ban tổ chức hiểu rõ hơn về động lực của bạn và chuẩn bị tốt hơn cho sự kiện.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Ghi chú thêm (tùy chọn)
                </label>
                <textarea
                  id="participant-notes"
                  name="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Có điều gì bạn muốn chia sẻ thêm với ban tổ chức không?"
                />
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-blue-600 mr-3 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">Lưu ý quan trọng:</p>
                    <ul className="space-y-1">
                      <li>• Vui lòng có mặt đúng giờ theo lịch trình</li>
                      <li>• Mang theo giấy tờ tùy thân để xác minh</li>
                      <li>• Tuân thủ các quy định của ban tổ chức</li>
                      <li>• Liên hệ ngay nếu có thay đổi kế hoạch</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}


        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            Bước {currentStep} / 2
          </div>

          <div className="flex space-x-3">
            {currentStep > 1 && (
              <button
                onClick={handleBack}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Quay lại
              </button>
            )}

            {currentStep < 2 ? (
              <button
                onClick={handleNext}
                className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Tiếp tục
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={loading}
                className="px-6 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : null}
                {loading ? 'Đang đăng ký...' : 'Xác nhận đăng ký'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventRegistrationModal;
