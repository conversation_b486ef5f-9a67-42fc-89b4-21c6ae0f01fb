// Simple test to check if badge service compiles correctly
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing badge service compilation...');

// Check if badge service file exists
const badgeServicePath = path.join(__dirname, 'src/services/badge.service.ts');
if (fs.existsSync(badgeServicePath)) {
  console.log('✅ Badge service file exists');
} else {
  console.log('❌ Badge service file not found');
  process.exit(1);
}

// Check if notification service file exists
const notificationServicePath = path.join(__dirname, 'src/services/notification.service.ts');
if (fs.existsSync(notificationServicePath)) {
  console.log('✅ Notification service file exists');
} else {
  console.log('❌ Notification service file not found');
  process.exit(1);
}

// Check if models exist
const models = [
  'src/models/UserBadge.ts',
  'src/models/Donation.ts',
  'src/models/EventRegistration.ts',
  'src/models/Post.ts'
];

models.forEach(model => {
  const modelPath = path.join(__dirname, model);
  if (fs.existsSync(modelPath)) {
    console.log(`✅ ${model} exists`);
  } else {
    console.log(`❌ ${model} not found`);
  }
});

console.log('🎉 All files exist! Badge service should compile correctly.');
console.log('');
console.log('📋 Summary of fixes applied:');
console.log('1. ✅ Fixed Donation model import: ../models/donation.model → ../models/Donation');
console.log('2. ✅ Added badgeType and badgeLevel to notification data type');
console.log('3. ✅ Fixed EventRegistration import to use default export');
console.log('');
console.log('🚀 Next steps:');
console.log('1. Restart the server: npm run dev');
console.log('2. Test badge endpoints:');
console.log('   - GET /api/badges/config');
console.log('   - GET /api/badges/test');
console.log('   - GET /api/badges/user/{userId}/primary');
console.log('');
console.log('💡 The 404 errors should be resolved now!');
