import React, { useState, useEffect } from 'react';
import axios from 'axios';
import UserBadge from './UserBadge';
import { Award, Star, Trophy } from 'lucide-react';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface Badge {
  _id: string;
  badgeType: string;
  badgeLevel: number;
  earnedAt: string;
  totalDonations?: number;
  totalEvents?: number;
  config: {
    name: string;
    icon: string;
    color: string;
    levels: {
      [key: number]: {
        name: string;
        [key: string]: any;
      };
    };
  };
  levelName: string;
}

interface BadgeShowcaseProps {
  userId?: string; // If provided, show badges for this user (public view)
  title?: string;
  showStats?: boolean;
  className?: string;
}

const BadgeShowcase: React.FC<BadgeShowcaseProps> = ({ 
  userId, 
  title = "Huy hiệu & Thành tích",
  showStats = true,
  className = ''
}) => {
  const [badges, setBadges] = useState<Badge[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBadges();
  }, [userId]);

  const fetchBadges = async () => {
    try {
      setLoading(true);
      setError(null);

      let url: string;
      const headers: any = {};

      if (userId) {
        // Public view - get badges for specific user
        url = `${API_URL}/api/badges/user/${userId}`;
      } else {
        // Private view - get current user's badges
        url = `${API_URL}/api/badges/my-badges`;
        const token = localStorage.getItem('token');
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        }
      }

      const response = await axios.get(url, { headers });

      if (response.data.success) {
        setBadges(response.data.data || []);
      } else {
        setError('Không thể tải huy hiệu');
      }
    } catch (err) {
      console.error('Error fetching badges:', err);
      setError('Lỗi khi tải huy hiệu');
    } finally {
      setLoading(false);
    }
  };

  const getStatsFromBadges = () => {
    let totalDonations = 0;
    let totalEvents = 0;
    let totalAmount = 0;

    badges.forEach(badge => {
      if (badge.totalDonations) totalDonations += badge.totalDonations;
      if (badge.totalEvents) totalEvents += badge.totalEvents;
    });

    return { totalDonations, totalEvents, totalAmount };
  };

  const stats = getStatsFromBadges();

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h2 className="text-xl font-bold text-gray-900 mb-4">{title}</h2>
        <div className="animate-pulse">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-gray-200 rounded-lg h-20"></div>
            ))}
          </div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-gray-200 rounded h-8"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h2 className="text-xl font-bold text-gray-900 mb-4">{title}</h2>
        <div className="text-center py-8">
          <Award className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (badges.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h2 className="text-xl font-bold text-gray-900 mb-4">{title}</h2>
        <div className="text-center py-8">
          <Award className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500 mb-2">Chưa có huy hiệu nào</p>
          <p className="text-sm text-gray-400">
            Hãy tham gia ủng hộ các chiến dịch và sự kiện để nhận huy hiệu!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
        <Trophy className="h-5 w-5 text-yellow-500" />
        {title}
      </h2>

      {/* Stats Summary */}
      {showStats && (stats.totalDonations > 0 || stats.totalEvents > 0) && (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
          {stats.totalDonations > 0 && (
            <div className="bg-purple-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.totalDonations}</div>
              <div className="text-sm text-purple-500">Lần ủng hộ</div>
            </div>
          )}
          {stats.totalEvents > 0 && (
            <div className="bg-green-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{stats.totalEvents}</div>
              <div className="text-sm text-green-500">Sự kiện tham gia</div>
            </div>
          )}
          <div className="bg-yellow-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{badges.length}</div>
            <div className="text-sm text-yellow-500">Huy hiệu</div>
          </div>
        </div>
      )}

      {/* Badges Grid */}
      <div className="space-y-4">
        <h3 className="font-semibold text-gray-700 flex items-center gap-2">
          <Star className="h-4 w-4 text-yellow-500" />
          Huy hiệu đã đạt được
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {badges.map((badge) => (
            <div
              key={badge._id}
              className="flex items-center gap-3 p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
              style={{ backgroundColor: `${badge.config.color}05` }}
            >
              <div className="flex-shrink-0">
                <UserBadge badge={badge} size="lg" showTooltip={false} />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 truncate">
                  {badge.config.name}
                </h4>
                <p className="text-sm text-gray-600 truncate">
                  Cấp độ: {badge.levelName}
                </p>
                <p className="text-xs text-gray-500">
                  Đạt được: {new Date(badge.earnedAt).toLocaleDateString('vi-VN')}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Encouragement Message */}
      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
        <p className="text-sm text-gray-700 text-center">
          <span className="font-medium">🌟 Tiếp tục phát huy!</span> Hãy tham gia thêm các hoạt động để nâng cấp huy hiệu và nhận thêm huy hiệu mới.
        </p>
      </div>
    </div>
  );
};

export default BadgeShowcase;
