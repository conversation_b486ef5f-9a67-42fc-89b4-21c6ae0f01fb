"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const donationController_1 = require("../controllers/donationController");
const express_1 = __importDefault(require("express"));
const Donation_1 = require("../models/Donation");
const Campaign_1 = require("../models/Campaign");
const router = express_1.default.Router();
// Create new donation
router.post('/', donationController_1.createDonation);
// Handle PayOS return URL
router.get('/payos_return', donationController_1.handlePayOSReturn);
// Handle PayOS webhook (for real payments)
router.post('/payos_webhook', donationController_1.handlePayOSReturn);
// Handle PayOS cancel URL
router.get('/payos_cancel', (req, res) => {
    res.redirect('/campaigns');
});
// Handle Momo return URL
router.get('/momo-return', donationController_1.handleMomoReturn);
// Handle Momo IPN (server-to-server)
router.post('/momo_ipn', donationController_1.handleMomoIPN);
// Get donation status by transactionId
router.get('/:transactionId/status', donationController_1.getDonationStatus);
// Get donation by transactionId (for frontend compatibility)
router.get('/:transactionId', donationController_1.getDonationStatus);
// Update donation status (for manual testing/fixing)
router.put('/:transactionId/status', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { transactionId } = req.params;
        const { status } = req.body;
        if (!['pending', 'success', 'failed', 'cancelled'].includes(status)) {
            return res.status(400).json({ message: 'Invalid status' });
        }
        const donation = yield require('../models/Donation').Donation.findOne({ transactionId });
        if (!donation) {
            return res.status(404).json({ message: 'Donation not found' });
        }
        const oldStatus = donation.status;
        donation.status = status;
        donation.paymentTime = new Date();
        yield donation.save();
        // If changing from pending to success, update campaign
        if (oldStatus === 'pending' && status === 'success') {
            const Campaign = require('../models/Campaign').Campaign;
            const campaign = yield Campaign.findById(donation.campaignId);
            if (campaign) {
                const newProgress = Math.min(100, ((campaign.currentAmount + donation.amount) / campaign.targetAmount) * 100);
                // Add donor to list
                const newDonor = {
                    name: donation.isAnonymous ? 'Ẩn danh' : donation.name,
                    amount: donation.amount,
                    date: donation.createdAt,
                    paymentMethod: donation.paymentMethod,
                    email: donation.email,
                    transactionId: donation.transactionId
                };
                // Update campaign using updateOne to avoid validation issues
                yield Campaign.updateOne({ _id: donation.campaignId }, {
                    $inc: {
                        currentAmount: donation.amount,
                        totalDonations: 1,
                        totalDonors: 1
                    },
                    $push: {
                        donors: newDonor
                    },
                    $set: {
                        progress: newProgress
                    }
                }, {
                    runValidators: false // Skip validation for partial updates
                });
            }
        }
        res.json({
            success: true,
            message: 'Donation status updated successfully',
            donation: {
                transactionId: donation.transactionId,
                status: donation.status,
                amount: donation.amount,
                name: donation.name
            }
        });
    }
    catch (error) {
        console.error('Error updating donation status:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
}));
// Get donations by campaign ID
router.get('/campaign/:campaignId', donationController_1.getDonationsByCampaignId);
// Get all donations (for admin)
router.get('/', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const donations = yield require('../models/Donation').Donation.find()
            .populate('campaignId', 'title')
            .sort({ createdAt: -1 })
            .limit(100);
        const transformedDonations = donations.map((donation) => ({
            _id: donation._id,
            name: donation.name,
            email: donation.email,
            amount: donation.amount,
            message: donation.message || '',
            paymentMethod: donation.paymentMethod.toLowerCase(),
            status: donation.status,
            transactionId: donation.transactionId,
            createdAt: donation.createdAt,
            campaign: {
                _id: donation.campaignId._id,
                title: donation.campaignId.title
            },
            isAnonymous: donation.isAnonymous || false
        }));
        res.json({
            success: true,
            donations: transformedDonations
        });
    }
    catch (error) {
        console.error('Error fetching donations:', error);
        res.status(500).json({ success: false, message: error.message });
    }
}));
// Auto-complete donation (for test environment)
router.post('/complete/:transactionId', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { transactionId } = req.params;
        // Update donation status
        const Donation = require('../models/Donation').Donation;
        const Campaign = require('../models/Campaign').Campaign;
        const donation = yield Donation.findOne({ transactionId });
        if (!donation) {
            return res.status(404).json({ message: 'Donation not found' });
        }
        if (donation.status !== 'pending') {
            return res.json({
                success: true,
                message: 'Donation already processed',
                status: donation.status
            });
        }
        // Update to success
        donation.status = 'success';
        donation.paymentTime = new Date();
        yield donation.save();
        // Instead of manually updating campaign stats, use fix-campaign to recalculate
        // This prevents duplicate additions and ensures accuracy
        // Get all successful donations for this campaign
        const donations = yield Donation.find({
            campaignId: donation.campaignId,
            status: 'success'
        });
        // Calculate correct totals
        const totalAmount = donations.reduce((sum, d) => sum + d.amount, 0);
        const totalCount = donations.length;
        // Get campaign to calculate progress
        const campaign = yield Campaign.findById(donation.campaignId);
        if (campaign) {
            const progress = Math.min(100, (totalAmount / campaign.targetAmount) * 100);
            // Update campaign with correct stats (not incremental)
            yield Campaign.updateOne({ _id: donation.campaignId }, {
                $set: {
                    currentAmount: totalAmount,
                    totalDonations: totalCount,
                    totalDonors: totalCount,
                    progress: progress
                }
            }, { runValidators: false });
        }
        res.json({
            success: true,
            message: 'Donation completed successfully',
            donation: {
                transactionId: donation.transactionId,
                status: donation.status,
                amount: donation.amount,
                name: donation.name
            }
        });
    }
    catch (error) {
        console.error('Error completing donation:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
}));
// Fix campaign stats (admin endpoint)
router.post('/fix-campaign/:campaignId', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { campaignId } = req.params;
        // Get all successful donations for this campaign
        const Donation = require('../models/Donation').Donation;
        const donations = yield Donation.find({
            campaignId: campaignId,
            status: 'success'
        });
        // Calculate correct totals
        const totalAmount = donations.reduce((sum, donation) => sum + donation.amount, 0);
        const totalCount = donations.length;
        // Get campaign to calculate progress
        const Campaign = require('../models/Campaign').Campaign;
        const campaign = yield Campaign.findById(campaignId);
        if (!campaign) {
            return res.status(404).json({ message: 'Campaign not found' });
        }
        const progress = Math.min(100, (totalAmount / campaign.targetAmount) * 100);
        // Update campaign stats
        yield Campaign.updateOne({ _id: campaignId }, {
            $set: {
                currentAmount: totalAmount,
                totalDonations: totalCount,
                totalDonors: totalCount,
                progress: progress
            }
        }, { runValidators: false });
        res.json({
            success: true,
            message: 'Campaign stats fixed successfully',
            stats: {
                currentAmount: totalAmount,
                totalDonations: totalCount,
                totalDonors: totalCount,
                progress: progress
            }
        });
    }
    catch (error) {
        console.error('Error fixing campaign stats:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
}));
// Fix all campaign stats
router.post('/fix-all-stats', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🔧 Starting to fix all campaign stats...');
        const campaigns = yield Campaign_1.Campaign.find({});
        let fixedCount = 0;
        for (const campaign of campaigns) {
            // Get all successful donations for this campaign
            const donations = yield Donation_1.Donation.find({
                campaignId: campaign._id,
                status: 'success'
            });
            const totalAmount = donations.reduce((sum, d) => sum + d.amount, 0);
            const totalCount = donations.length;
            const progress = Math.min(100, (totalAmount / campaign.targetAmount) * 100);
            // Check if stats need fixing
            const needsUpdate = campaign.currentAmount !== totalAmount ||
                campaign.totalDonations !== totalCount ||
                campaign.totalDonors !== totalCount ||
                Math.abs(campaign.progress - progress) > 0.1;
            if (needsUpdate) {
                yield Campaign_1.Campaign.updateOne({ _id: campaign._id }, {
                    $set: {
                        currentAmount: totalAmount,
                        totalDonations: totalCount,
                        totalDonors: totalCount,
                        progress: progress
                    }
                }, { runValidators: false });
                console.log(`✅ Fixed stats for campaign: ${campaign.title}`);
                console.log(`   Amount: ${campaign.currentAmount} → ${totalAmount}`);
                console.log(`   Donations: ${campaign.totalDonations} → ${totalCount}`);
                console.log(`   Progress: ${campaign.progress}% → ${progress}%`);
                fixedCount++;
            }
        }
        console.log(`🎉 Fixed stats for ${fixedCount} campaigns`);
        res.json({
            success: true,
            message: `Fixed stats for ${fixedCount} campaigns`,
            fixedCount
        });
    }
    catch (error) {
        console.error('Error fixing all campaign stats:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
}));
exports.default = router;
