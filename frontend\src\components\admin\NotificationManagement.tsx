import React, { useState, useEffect } from 'react';
import { Send, Users, User, Bell, BarChart3, AlertCircle, CheckCircle, Clock, Mail, Filter, Search, Eye, Trash2, MessageSquare, Calendar, TrendingUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import CreateSampleNotifications from './CreateSampleNotifications';

const API_URL = import.meta.env.VITE_API_URL || '';

interface NotificationStats {
  total: number;
  unread: number;
  byType: Array<{
    _id: string;
    count: number;
  }>;
  byPriority: Array<{
    _id: string;
    count: number;
  }>;
  recentActivity: Array<{
    date: string;
    count: number;
  }>;
}

interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
}

interface Notification {
  _id: string;
  userId: string;
  type: 'donation' | 'campaign' | 'event' | 'post' | 'comment' | 'system' | 'report' | 'admin';
  title: string;
  message: string;
  isRead: boolean;
  emailSent: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  user?: {
    name: string;
    email: string;
  };
}

const NotificationManagement: React.FC = () => {
  // Form states
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');
  const [sendToAll, setSendToAll] = useState(true);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  // Data states
  const [users, setUsers] = useState<User[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats | null>(null);

  // UI states
  const [activeTab, setActiveTab] = useState<'create' | 'manage' | 'analytics'>('create');
  const [loading, setLoading] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [loadingStats, setLoadingStats] = useState(false);
  const [loadingNotifications, setLoadingNotifications] = useState(false);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Selected notifications for bulk actions
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  useEffect(() => {
    fetchUsers();
    fetchStats();
    fetchNotifications();
  }, []);

  useEffect(() => {
    if (activeTab === 'manage') {
      fetchNotifications();
    }
  }, [activeTab]);

  const fetchUsers = async () => {
    try {
      setLoadingUsers(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/admin/users`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('Users API response:', response.data);

      if (response.data.success && response.data.data?.users && Array.isArray(response.data.data.users)) {
        setUsers(response.data.data.users.filter((user: User) => user.role === 'user'));
      } else if (response.data.users && Array.isArray(response.data.users)) {
        // Handle case where users array is directly in response
        setUsers(response.data.users.filter((user: User) => user.role === 'user'));
      } else if (response.data.data?.users && Array.isArray(response.data.data.users)) {
        // Handle case where users array is in data.users
        setUsers(response.data.data.users.filter((user: User) => user.role === 'user'));
      } else {
        console.error('Invalid users data structure:', response.data);
        setUsers([]);
        toast.error('Dữ liệu người dùng không hợp lệ');
      }
    } catch (error: any) {
      console.error('Error fetching users:', error);
      setUsers([]);
      toast.error(error.response?.data?.message || 'Không thể tải danh sách người dùng');
    } finally {
      setLoadingUsers(false);
    }
  };

  const fetchNotifications = async () => {
    try {
      setLoadingNotifications(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/notifications/admin/all`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setNotifications(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast.error('Không thể tải danh sách thông báo');
    } finally {
      setLoadingNotifications(false);
    }
  };

  const fetchStats = async () => {
    try {
      setLoadingStats(true);
      const token = localStorage.getItem('token');

      console.log('Fetching notification stats from:', `${API_URL}/api/notifications/admin/stats`);

      const response = await axios.get(`${API_URL}/api/notifications/admin/stats`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('Stats API response:', response.data);

      if (response.data.success) {
        setStats(response.data.data);
      } else {
        console.error('Stats API returned success: false');
        toast.error(response.data.message || 'Không thể tải thống kê thông báo');
      }
    } catch (error: any) {
      console.error('Error fetching notification stats:', error);

      if (error.response?.status === 404) {
        toast.error('API endpoint không tồn tại. Vui lòng kiểm tra server.');
      } else if (error.response?.status === 403) {
        toast.error('Bạn không có quyền truy cập thống kê thông báo.');
      } else {
        toast.error(error.response?.data?.message || 'Không thể tải thống kê thông báo');
      }
    } finally {
      setLoadingStats(false);
    }
  };

  const handleSendNotification = async () => {
    if (!title.trim() || !message.trim()) {
      toast.error('Vui lòng nhập tiêu đề và nội dung thông báo');
      return;
    }

    if (!sendToAll && selectedUsers.length === 0) {
      toast.error('Vui lòng chọn ít nhất một người dùng');
      return;
    }

    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const payload = {
        title: title.trim(),
        message: message.trim(),
        priority,
        sendToAll,
        userIds: sendToAll ? undefined : selectedUsers
      };

      const response = await axios.post(`${API_URL}/api/notifications/admin/create`, payload, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        toast.success(`Đã gửi thông báo đến ${response.data.count} người dùng`);
        setTitle('');
        setMessage('');
        setPriority('medium');
        setSelectedUsers([]);
        fetchStats(); // Refresh stats
        fetchNotifications(); // Refresh notifications list
      } else {
        toast.error(response.data.message || 'Có lỗi xảy ra');
      }
    } catch (error: any) {
      console.error('Error sending notification:', error);
      toast.error(error.response?.data?.message || 'Không thể gửi thông báo');
    } finally {
      setLoading(false);
    }
  };

  const handleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const getTypeDisplayName = (type: string) => {
    const typeMap: Record<string, string> = {
      'donation': 'Quyên góp',
      'campaign': 'Chiến dịch',
      'event': 'Sự kiện',
      'post': 'Bài viết',
      'comment': 'Bình luận',
      'system': 'Hệ thống',
      'admin': 'Quản trị',
      'report': 'Báo cáo'
    };
    return typeMap[type] || type;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityDisplayName = (priority: string) => {
    const priorityMap: Record<string, string> = {
      'urgent': 'Khẩn cấp',
      'high': 'Cao',
      'medium': 'Trung bình',
      'low': 'Thấp'
    };
    return priorityMap[priority] || priority;
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'donation': return '💰';
      case 'campaign': return '🎯';
      case 'event': return '📅';
      case 'post': return '📝';
      case 'comment': return '💬';
      case 'system': return '⚙️';
      case 'admin': return '👨‍💼';
      case 'report': return '⚠️';
      default: return '📢';
    }
  };

  // Filter notifications based on search and filters
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = searchTerm === '' ||
      notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.user?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.user?.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = typeFilter === 'all' || notification.type === typeFilter;
    const matchesPriority = priorityFilter === 'all' || notification.priority === priorityFilter;
    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'read' && notification.isRead) ||
      (statusFilter === 'unread' && !notification.isRead);

    return matchesSearch && matchesType && matchesPriority && matchesStatus;
  });

  // Handle notification actions
  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const token = localStorage.getItem('token');
      await axios.patch(`${API_URL}/api/notifications/${notificationId}/read`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Update local state
      setNotifications(prev =>
        prev.map(n => n._id === notificationId ? { ...n, isRead: true } : n)
      );
      toast.success('Đã đánh dấu thông báo là đã đọc');
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Không thể đánh dấu thông báo');
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    if (!confirm('Bạn có chắc chắn muốn xóa thông báo này?')) return;

    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/notifications/admin/${notificationId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Update local state
      setNotifications(prev => prev.filter(n => n._id !== notificationId));
      toast.success('Đã xóa thông báo');
      fetchStats(); // Refresh stats
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast.error('Không thể xóa thông báo');
    }
  };

  const handleBulkMarkAsRead = async () => {
    if (selectedNotifications.length === 0) return;

    try {
      const token = localStorage.getItem('token');
      await Promise.all(
        selectedNotifications.map(id =>
          axios.patch(`${API_URL}/api/notifications/${id}/read`, {}, {
            headers: { Authorization: `Bearer ${token}` }
          })
        )
      );

      // Update local state
      setNotifications(prev =>
        prev.map(n =>
          selectedNotifications.includes(n._id) ? { ...n, isRead: true } : n
        )
      );
      setSelectedNotifications([]);
      toast.success(`Đã đánh dấu ${selectedNotifications.length} thông báo là đã đọc`);
    } catch (error) {
      console.error('Error bulk marking as read:', error);
      toast.error('Không thể đánh dấu thông báo');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedNotifications.length === 0) return;
    if (!confirm(`Bạn có chắc chắn muốn xóa ${selectedNotifications.length} thông báo?`)) return;

    try {
      const token = localStorage.getItem('token');
      await Promise.all(
        selectedNotifications.map(id =>
          axios.delete(`${API_URL}/api/notifications/admin/${id}`, {
            headers: { Authorization: `Bearer ${token}` }
          })
        )
      );

      // Update local state
      setNotifications(prev =>
        prev.filter(n => !selectedNotifications.includes(n._id))
      );
      setSelectedNotifications([]);
      toast.success(`Đã xóa ${selectedNotifications.length} thông báo`);
      fetchStats(); // Refresh stats
    } catch (error) {
      console.error('Error bulk deleting:', error);
      toast.error('Không thể xóa thông báo');
    }
  };

  const handleSelectNotification = (notificationId: string) => {
    setSelectedNotifications(prev =>
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n._id));
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Bell className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Quản lý thông báo</h1>
              <p className="text-gray-600">Tạo và quản lý thông báo hệ thống</p>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {[
              { id: 'create', label: 'Tạo mới', icon: Send },
              { id: 'manage', label: 'Quản lý', icon: MessageSquare },
              { id: 'analytics', label: 'Thống kê', icon: BarChart3 }
            ].map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'analytics' && (
          <motion.div
            key="analytics"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Tổng thông báo</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {loadingStats ? '...' : stats?.total || 0}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Bell className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Chưa đọc</p>
                    <p className="text-2xl font-bold text-orange-600">
                      {loadingStats ? '...' : stats?.unread || 0}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <AlertCircle className="w-6 h-6 text-orange-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Người dùng</p>
                    <p className="text-2xl font-bold text-green-600">
                      {loadingUsers ? '...' : users.length}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Hệ thống</p>
                    <p className="text-2xl font-bold text-purple-600">48</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Notification Types Chart */}
            {stats?.byType && stats.byType.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Thống kê theo loại thông báo
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {stats.byType.map((type) => (
                    <div key={type._id} className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl mb-2">{getTypeIcon(type._id)}</div>
                      <p className="text-sm text-gray-600">{getTypeDisplayName(type._id)}</p>
                      <p className="text-xl font-bold text-gray-900">{type.count}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        )}

        {activeTab === 'create' && (
          <motion.div
            key="create"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Sample Notifications */}
            <CreateSampleNotifications />

            {/* Create Notification Form */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                <Send className="w-5 h-5 mr-2" />
                Tạo thông báo mới
              </h3>

              <div className="space-y-6">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tiêu đề thông báo
                  </label>
                  <input
                    id="notification-title"
                    name="title"
                    type="text"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Nhập tiêu đề thông báo..."
                    maxLength={100}
                  />
                  <p className="text-xs text-gray-500 mt-1">{title.length}/100 ký tự</p>
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nội dung thông báo
                  </label>
                  <textarea
                    id="notification-message"
                    name="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Nhập nội dung thông báo..."
                    maxLength={500}
                  />
                  <p className="text-xs text-gray-500 mt-1">{message.length}/500 ký tự</p>
                </div>

                {/* Priority */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mức độ ưu tiên
                  </label>
                  <select
                    id="notification-priority"
                    name="priority"
                    value={priority}
                    onChange={(e) => setPriority(e.target.value as any)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="low">Thấp</option>
                    <option value="medium">Trung bình</option>
                    <option value="high">Cao</option>
                    <option value="urgent">Khẩn cấp</option>
                  </select>
                </div>

                {/* Recipients */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Người nhận
                  </label>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        id="send-to-all"
                        name="recipients"
                        type="radio"
                        checked={sendToAll}
                        onChange={() => setSendToAll(true)}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 flex items-center">
                        <Users className="w-4 h-4 mr-1" />
                        Gửi đến tất cả người dùng
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        id="send-to-specific"
                        name="recipients"
                        type="radio"
                        checked={!sendToAll}
                        onChange={() => setSendToAll(false)}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 flex items-center">
                        <User className="w-4 h-4 mr-1" />
                        Chọn người dùng cụ thể
                      </span>
                    </label>
                  </div>
                </div>

                {/* User Selection */}
                {!sendToAll && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Chọn người dùng ({selectedUsers.length} đã chọn)
                    </label>
                    <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-lg p-3 space-y-2">
                      {loadingUsers ? (
                        <div className="text-center py-4">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                        </div>
                      ) : users.length === 0 ? (
                        <p className="text-gray-500 text-center py-4">Không có người dùng nào</p>
                      ) : (
                        users.map((user) => (
                          <label key={user._id} className="flex items-center p-2 hover:bg-gray-50 rounded">
                            <input
                              id={`user-${user._id}`}
                              name={`user-${user._id}`}
                              type="checkbox"
                              checked={selectedUsers.includes(user._id)}
                              onChange={() => handleUserSelection(user._id)}
                              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <div className="ml-3">
                              <p className="text-sm font-medium text-gray-900">{user.name}</p>
                              <p className="text-xs text-gray-500">{user.email}</p>
                            </div>
                          </label>
                        ))
                      )}
                    </div>
                  </div>
                )}

                {/* Send Button */}
                <div className="flex justify-end">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleSendNotification}
                    disabled={loading}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Đang gửi...</span>
                      </>
                    ) : (
                      <>
                        <Mail className="w-4 h-4" />
                        <span>Gửi thông báo</span>
                      </>
                    )}
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'manage' && (
          <motion.div
            key="manage"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Filters */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Filter className="w-5 h-5 mr-2" />
                Bộ lọc thông báo
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Tìm kiếm</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      id="notifications-search"
                      name="search"
                      type="text"
                      placeholder="Tìm kiếm thông báo..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      autoComplete="off"
                    />
                  </div>
                </div>

                {/* Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Loại</label>
                  <select
                    id="notifications-type-filter"
                    name="typeFilter"
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">Tất cả</option>
                    <option value="admin">Quản trị</option>
                    <option value="system">Hệ thống</option>
                    <option value="campaign">Chiến dịch</option>
                    <option value="event">Sự kiện</option>
                    <option value="donation">Quyên góp</option>
                    <option value="post">Bài viết</option>
                    <option value="comment">Bình luận</option>
                    <option value="report">Báo cáo</option>
                  </select>
                </div>

                {/* Priority Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Ưu tiên</label>
                  <select
                    id="notifications-priority-filter"
                    name="priorityFilter"
                    value={priorityFilter}
                    onChange={(e) => setPriorityFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">Tất cả</option>
                    <option value="urgent">Khẩn cấp</option>
                    <option value="high">Cao</option>
                    <option value="medium">Trung bình</option>
                    <option value="low">Thấp</option>
                  </select>
                </div>

                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
                  <select
                    id="notifications-status-filter"
                    name="statusFilter"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">Tất cả</option>
                    <option value="unread">Chưa đọc</option>
                    <option value="read">Đã đọc</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Notifications List */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                      <MessageSquare className="w-5 h-5 mr-2" />
                      Danh sách thông báo ({filteredNotifications.length})
                    </h3>

                    {filteredNotifications.length > 0 && (
                      <div className="flex items-center space-x-2">
                        <input
                          id="select-all-notifications"
                          name="selectAll"
                          type="checkbox"
                          checked={selectedNotifications.length === filteredNotifications.length && filteredNotifications.length > 0}
                          onChange={handleSelectAll}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="select-all-notifications" className="text-sm text-gray-600">
                          Chọn tất cả
                        </label>
                      </div>
                    )}
                  </div>

                  {selectedNotifications.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">
                        Đã chọn {selectedNotifications.length} thông báo
                      </span>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={handleBulkMarkAsRead}
                        className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors flex items-center space-x-1"
                      >
                        <CheckCircle className="w-4 h-4" />
                        <span>Đánh dấu đã đọc</span>
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={handleBulkDelete}
                        className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors flex items-center space-x-1"
                      >
                        <Trash2 className="w-4 h-4" />
                        <span>Xóa</span>
                      </motion.button>
                    </div>
                  )}
                </div>
              </div>

              <div className="divide-y divide-gray-200">
                {loadingNotifications ? (
                  <div className="p-8 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                    <p className="text-gray-500 mt-2">Đang tải thông báo...</p>
                  </div>
                ) : filteredNotifications.length === 0 ? (
                  <div className="p-8 text-center">
                    <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Không có thông báo nào</p>
                  </div>
                ) : (
                  filteredNotifications.map((notification) => (
                    <div key={notification._id} className={`p-6 transition-colors ${
                      selectedNotifications.includes(notification._id) ? 'bg-blue-50' : 'hover:bg-gray-50'
                    }`}>
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0 flex items-center space-x-3">
                          <input
                            id={`notification-${notification._id}`}
                            name={`notification-${notification._id}`}
                            type="checkbox"
                            checked={selectedNotifications.includes(notification._id)}
                            onChange={() => handleSelectNotification(notification._id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <div className="text-2xl">{getTypeIcon(notification.type)}</div>
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {notification.title}
                            </h4>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(notification.priority)}`}>
                              {getPriorityDisplayName(notification.priority)}
                            </span>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              notification.isRead
                                ? 'bg-green-100 text-green-800'
                                : 'bg-orange-100 text-orange-800'
                            }`}>
                              {notification.isRead ? 'Đã đọc' : 'Chưa đọc'}
                            </span>
                          </div>

                          <p className="text-sm text-gray-600 mb-2">{notification.message}</p>

                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span className="flex items-center">
                              <Calendar className="w-3 h-3 mr-1" />
                              {format(new Date(notification.createdAt), 'dd/MM/yyyy HH:mm', { locale: vi })}
                            </span>
                            <span className="flex items-center">
                              <User className="w-3 h-3 mr-1" />
                              {notification.user?.name || 'Hệ thống'}
                            </span>
                            <span className="flex items-center">
                              <Mail className="w-3 h-3 mr-1" />
                              {notification.emailSent ? 'Đã gửi email' : 'Chưa gửi email'}
                            </span>
                          </div>
                        </div>

                        <div className="flex-shrink-0 flex items-center space-x-2">
                          {!notification.isRead && (
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => handleMarkAsRead(notification._id)}
                              className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                              title="Đánh dấu đã đọc"
                            >
                              <CheckCircle className="w-4 h-4" />
                            </motion.button>
                          )}
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => handleDeleteNotification(notification._id)}
                            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                            title="Xóa thông báo"
                          >
                            <Trash2 className="w-4 h-4" />
                          </motion.button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default NotificationManagement;
