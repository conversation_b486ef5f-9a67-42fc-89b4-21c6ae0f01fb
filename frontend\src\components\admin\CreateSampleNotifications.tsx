import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Plus, Loader2 } from 'lucide-react';
import axios from 'axios';
import { toast } from 'react-hot-toast';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

const CreateSampleNotifications: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const sampleNotifications = [
    {
      title: 'Chào mừng bạn đến với KeyDyWeb!',
      message: 'Cảm ơn bạn đã tham gia cộng đồng thiện nguyện của chúng tôi. Hãy khám phá các chiến dịch và sự kiện đang diễn ra.',
      priority: 'medium' as const,
      type: 'system'
    },
    {
      title: 'Chiến dịch mới: Hỗ trợ trẻ em vùng cao',
      message: '<PERSON><PERSON><PERSON> chiến dịch quyên góp mới đã được tạo để hỗ trợ trẻ em vùng cao. H<PERSON>y tham gia đóng góp để mang lại hy vọng cho các em.',
      priority: 'high' as const,
      type: 'campaign'
    },
    {
      title: 'Sự kiện tình nguyện cuối tuần',
      message: 'Tham gia hoạt động tình nguyện dọn dẹp môi trường vào cuối tuần này. Đăng ký ngay để không bỏ lỡ cơ hội ý nghĩa.',
      priority: 'medium' as const,
      type: 'event'
    },
    {
      title: 'Cảm ơn đóng góp của bạn!',
      message: 'Chúng tôi đã nhận được khoản đóng góp 500,000 VNĐ từ bạn cho chiến dịch "Xây dựng trường học". Cảm ơn tấm lòng hảo tâm của bạn!',
      priority: 'low' as const,
      type: 'donation'
    },
    {
      title: 'Bài viết mới được đăng',
      message: 'Có một bài viết mới về hoạt động thiện nguyện được chia sẻ trong cộng đồng. Hãy xem và tương tác nhé!',
      priority: 'low' as const,
      type: 'post'
    },
    {
      title: 'Thông báo bảo trì hệ thống',
      message: 'Hệ thống sẽ được bảo trì vào 2:00 AM ngày mai. Thời gian dự kiến: 2 tiếng. Xin lỗi vì sự bất tiện này.',
      priority: 'urgent' as const,
      type: 'system'
    },
    {
      title: 'Báo cáo vi phạm mới',
      message: 'Có một báo cáo vi phạm mới cần được xem xét. Vui lòng kiểm tra và xử lý kịp thời.',
      priority: 'high' as const,
      type: 'report'
    },
    {
      title: 'Bình luận mới trên bài viết',
      message: 'Có người dùng vừa bình luận trên bài viết "Hoạt động thiện nguyện tháng 12". Hãy xem và phản hồi.',
      priority: 'low' as const,
      type: 'comment'
    }
  ];

  const createSampleNotifications = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      // Create notifications one by one with small delay
      for (const notification of sampleNotifications) {
        await axios.post(`${API_URL}/api/notifications/admin/create`, {
          title: notification.title,
          message: notification.message,
          priority: notification.priority,
          sendToAll: true
        }, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      toast.success(`Đã tạo ${sampleNotifications.length} thông báo mẫu thành công!`);
    } catch (error) {
      console.error('Error creating sample notifications:', error);
      toast.error('Không thể tạo thông báo mẫu');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Tạo thông báo mẫu</h3>
          <p className="text-sm text-gray-600">Tạo {sampleNotifications.length} thông báo mẫu để test hệ thống</p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={createSampleNotifications}
          disabled={loading}
          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-600 text-white rounded-lg hover:from-purple-600 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Plus className="w-4 h-4" />
          )}
          <span>{loading ? 'Đang tạo...' : 'Tạo thông báo mẫu'}</span>
        </motion.button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {sampleNotifications.map((notification, index) => (
          <div key={index} className="p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-xs font-medium text-gray-600">{notification.type}</span>
              <span className={`text-xs px-2 py-1 rounded-full ${
                notification.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                notification.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                notification.priority === 'medium' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {notification.priority}
              </span>
            </div>
            <h4 className="text-sm font-medium text-gray-900 mb-1">{notification.title}</h4>
            <p className="text-xs text-gray-600 line-clamp-2">{notification.message}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CreateSampleNotifications;
