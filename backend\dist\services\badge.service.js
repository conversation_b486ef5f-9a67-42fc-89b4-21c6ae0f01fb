"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkAllBadges = exports.getUserPrimaryBadge = exports.getUserBadges = exports.checkVolunteerBadge = exports.checkSupporterBadge = exports.calculatePostStats = exports.calculateEventStats = exports.calculateDonationStats = void 0;
const UserBadge_1 = require("../models/UserBadge");
const donation_model_1 = require("../models/donation.model");
const EventRegistration_1 = __importDefault(require("../models/EventRegistration"));
const Post_1 = require("../models/Post");
const notification_service_1 = require("./notification.service");
// Calculate user's donation statistics
const calculateDonationStats = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const stats = yield donation_model_1.Donation.aggregate([
        {
            $match: {
                userId: userId,
                status: 'success'
            }
        },
        {
            $group: {
                _id: null,
                totalAmount: { $sum: '$amount' },
                totalDonations: { $sum: 1 },
                lastDonation: { $max: '$createdAt' },
                lastAmount: { $last: '$amount' }
            }
        }
    ]);
    return stats[0] || {
        totalAmount: 0,
        totalDonations: 0,
        lastDonation: null,
        lastAmount: 0
    };
});
exports.calculateDonationStats = calculateDonationStats;
// Calculate user's event participation
const calculateEventStats = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const stats = yield EventRegistration_1.default.aggregate([
        {
            $match: {
                userId: userId,
                status: 'registered'
            }
        },
        {
            $group: {
                _id: null,
                totalEvents: { $sum: 1 },
                lastEvent: { $max: '$registrationDate' }
            }
        }
    ]);
    return stats[0] || {
        totalEvents: 0,
        lastEvent: null
    };
});
exports.calculateEventStats = calculateEventStats;
// Calculate user's post statistics
const calculatePostStats = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const stats = yield Post_1.Post.aggregate([
        {
            $match: {
                user: userId
            }
        },
        {
            $group: {
                _id: null,
                totalPosts: { $sum: 1 },
                totalLikes: { $sum: { $size: '$reactions' } },
                totalComments: { $sum: { $size: '$comments' } }
            }
        }
    ]);
    return stats[0] || {
        totalPosts: 0,
        totalLikes: 0,
        totalComments: 0
    };
});
exports.calculatePostStats = calculatePostStats;
// Check and award supporter badge
const checkSupporterBadge = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const donationStats = yield (0, exports.calculateDonationStats)(userId);
    const { totalAmount, totalDonations } = donationStats;
    // Find current supporter badge
    let currentBadge = yield UserBadge_1.UserBadge.findOne({
        userId,
        badgeType: 'supporter',
        isActive: true
    });
    const supporterLevels = UserBadge_1.BADGE_CONFIG.supporter.levels;
    let newLevel = 0;
    // Determine the highest level earned
    for (let level = 5; level >= 1; level--) {
        const requirement = supporterLevels[level];
        if (totalDonations >= requirement.minDonations && totalAmount >= requirement.minAmount) {
            newLevel = level;
            break;
        }
    }
    if (newLevel === 0) {
        return { earned: false };
    }
    const previousLevel = (currentBadge === null || currentBadge === void 0 ? void 0 : currentBadge.badgeLevel) || 0;
    const levelUp = newLevel > previousLevel;
    if (!currentBadge) {
        // Create new badge
        currentBadge = new UserBadge_1.UserBadge({
            userId,
            badgeType: 'supporter',
            badgeLevel: newLevel,
            totalDonations,
            metadata: {
                lastDonationAmount: donationStats.lastAmount,
                lastDonationDate: donationStats.lastDonation
            }
        });
        yield currentBadge.save();
        // Send notification for new badge
        yield (0, notification_service_1.createNotification)({
            userId,
            type: 'system',
            title: '🎉 Chúc mừng! Bạn đã nhận được huy hiệu mới!',
            message: `Bạn đã trở thành ${UserBadge_1.BADGE_CONFIG.supporter.icon} ${UserBadge_1.BADGE_CONFIG.supporter.name} - ${supporterLevels[newLevel].name}! Cảm ơn những đóng góp tuyệt vời của bạn.`,
            data: {
                badgeType: 'supporter',
                badgeLevel: newLevel,
                actionType: 'created'
            },
            priority: 'medium',
            sendEmail: false
        });
        return { earned: true, badge: currentBadge, levelUp: true, previousLevel: 0 };
    }
    else if (levelUp) {
        // Update existing badge
        currentBadge.badgeLevel = newLevel;
        currentBadge.totalDonations = totalDonations;
        currentBadge.metadata = Object.assign(Object.assign({}, currentBadge.metadata), { lastDonationAmount: donationStats.lastAmount, lastDonationDate: donationStats.lastDonation });
        yield currentBadge.save();
        // Send notification for level up
        yield (0, notification_service_1.createNotification)({
            userId,
            type: 'system',
            title: '⬆️ Huy hiệu của bạn đã được nâng cấp!',
            message: `${UserBadge_1.BADGE_CONFIG.supporter.icon} ${UserBadge_1.BADGE_CONFIG.supporter.name} của bạn đã lên cấp ${supporterLevels[newLevel].name}! Tiếp tục phát huy nhé!`,
            data: {
                badgeType: 'supporter',
                badgeLevel: newLevel,
                actionType: 'updated'
            },
            priority: 'medium',
            sendEmail: false
        });
        return { earned: true, badge: currentBadge, levelUp: true, previousLevel };
    }
    return { earned: true, badge: currentBadge, levelUp: false };
});
exports.checkSupporterBadge = checkSupporterBadge;
// Check and award volunteer badge
const checkVolunteerBadge = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const eventStats = yield (0, exports.calculateEventStats)(userId);
    const { totalEvents } = eventStats;
    let currentBadge = yield UserBadge_1.UserBadge.findOne({
        userId,
        badgeType: 'volunteer',
        isActive: true
    });
    const volunteerLevels = UserBadge_1.BADGE_CONFIG.volunteer.levels;
    let newLevel = 0;
    for (let level = 5; level >= 1; level--) {
        const requirement = volunteerLevels[level];
        if (totalEvents >= requirement.minEvents) {
            newLevel = level;
            break;
        }
    }
    if (newLevel === 0) {
        return { earned: false };
    }
    const previousLevel = (currentBadge === null || currentBadge === void 0 ? void 0 : currentBadge.badgeLevel) || 0;
    const levelUp = newLevel > previousLevel;
    if (!currentBadge) {
        currentBadge = new UserBadge_1.UserBadge({
            userId,
            badgeType: 'volunteer',
            badgeLevel: newLevel,
            totalEvents
        });
        yield currentBadge.save();
        yield (0, notification_service_1.createNotification)({
            userId,
            type: 'system',
            title: '🎉 Chúc mừng! Bạn đã nhận được huy hiệu mới!',
            message: `Bạn đã trở thành ${UserBadge_1.BADGE_CONFIG.volunteer.icon} ${UserBadge_1.BADGE_CONFIG.volunteer.name} - ${volunteerLevels[newLevel].name}! Cảm ơn sự tham gia tích cực của bạn.`,
            data: {
                badgeType: 'volunteer',
                badgeLevel: newLevel,
                actionType: 'created'
            },
            priority: 'medium',
            sendEmail: false
        });
        return { earned: true, badge: currentBadge, levelUp: true, previousLevel: 0 };
    }
    else if (levelUp) {
        currentBadge.badgeLevel = newLevel;
        currentBadge.totalEvents = totalEvents;
        yield currentBadge.save();
        yield (0, notification_service_1.createNotification)({
            userId,
            type: 'system',
            title: '⬆️ Huy hiệu của bạn đã được nâng cấp!',
            message: `${UserBadge_1.BADGE_CONFIG.volunteer.icon} ${UserBadge_1.BADGE_CONFIG.volunteer.name} của bạn đã lên cấp ${volunteerLevels[newLevel].name}! Tiếp tục tham gia nhé!`,
            data: {
                badgeType: 'volunteer',
                badgeLevel: newLevel,
                actionType: 'updated'
            },
            priority: 'medium',
            sendEmail: false
        });
        return { earned: true, badge: currentBadge, levelUp: true, previousLevel };
    }
    return { earned: true, badge: currentBadge, levelUp: false };
});
exports.checkVolunteerBadge = checkVolunteerBadge;
// Get user's active badges
const getUserBadges = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const badges = yield UserBadge_1.UserBadge.find({
        userId,
        isActive: true
    }).sort({ badgeLevel: -1, earnedAt: -1 });
    return badges.map(badge => (Object.assign(Object.assign({}, badge.toObject()), { config: UserBadge_1.BADGE_CONFIG[badge.badgeType], levelName: UserBadge_1.BADGE_CONFIG[badge.badgeType].levels[badge.badgeLevel].name })));
});
exports.getUserBadges = getUserBadges;
// Get user's primary badge (highest level)
const getUserPrimaryBadge = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const badge = yield UserBadge_1.UserBadge.findOne({
        userId,
        isActive: true
    }).sort({ badgeLevel: -1, earnedAt: -1 });
    if (!badge)
        return null;
    return Object.assign(Object.assign({}, badge.toObject()), { config: UserBadge_1.BADGE_CONFIG[badge.badgeType], levelName: UserBadge_1.BADGE_CONFIG[badge.badgeType].levels[badge.badgeLevel].name });
});
exports.getUserPrimaryBadge = getUserPrimaryBadge;
// Check all badges for a user (called after donations, events, etc.)
const checkAllBadges = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const results = yield Promise.allSettled([
        (0, exports.checkSupporterBadge)(userId),
        (0, exports.checkVolunteerBadge)(userId)
    ]);
    const earnedBadges = results
        .filter(result => result.status === 'fulfilled' && result.value.earned)
        .map(result => result.value);
    return earnedBadges;
});
exports.checkAllBadges = checkAllBadges;
