import { Request, Response } from 'express';
import { getUserBadges, getUserPrimaryBadge, checkAllBadges } from '../services/badge.service';
import { UserBadge, BADGE_CONFIG } from '../models/UserBadge';
import mongoose from 'mongoose';

// Get user's badges
export const getUserBadgesController = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?._id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const badges = await getUserBadges(userId);

    return res.json({
      success: true,
      data: badges
    });
  } catch (error) {
    console.error('Error getting user badges:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get user's primary badge
export const getUserPrimaryBadgeController = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?._id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const badge = await getUserPrimaryBadge(userId);

    return res.json({
      success: true,
      data: badge
    });
  } catch (error) {
    console.error('Error getting user primary badge:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get another user's badges (public)
export const getPublicUserBadges = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID'
      });
    }

    const badges = await getUserBadges(new mongoose.Types.ObjectId(userId));

    return res.json({
      success: true,
      data: badges
    });
  } catch (error) {
    console.error('Error getting public user badges:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get another user's primary badge (public)
export const getPublicUserPrimaryBadge = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID'
      });
    }

    const badge = await getUserPrimaryBadge(new mongoose.Types.ObjectId(userId));

    return res.json({
      success: true,
      data: badge
    });
  } catch (error) {
    console.error('Error getting public user primary badge:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Manually check all badges for current user
export const checkUserBadges = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?._id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const earnedBadges = await checkAllBadges(userId);

    return res.json({
      success: true,
      message: 'Badges checked successfully',
      data: earnedBadges
    });
  } catch (error) {
    console.error('Error checking user badges:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get badge configuration (public)
export const getBadgeConfig = async (req: Request, res: Response) => {
  try {
    return res.json({
      success: true,
      data: BADGE_CONFIG
    });
  } catch (error) {
    console.error('Error getting badge config:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Test endpoint to debug badge system
export const testBadgeSystem = async (req: Request, res: Response) => {
  try {
    console.log('🧪 [Badge Test] Testing badge system...');

    // Test badge config
    console.log('📋 [Badge Test] Badge config:', Object.keys(BADGE_CONFIG));

    // Test UserBadge model
    const badgeCount = await UserBadge.countDocuments();
    console.log('🏆 [Badge Test] Total badges in database:', badgeCount);

    // Test sample badge creation (if no badges exist)
    if (badgeCount === 0) {
      console.log('🆕 [Badge Test] No badges found, creating sample badge...');

      // Create a sample badge for testing
      const sampleBadge = new UserBadge({
        userId: new mongoose.Types.ObjectId(),
        badgeType: 'supporter',
        badgeLevel: 1,
        totalDonations: 1
      });

      await sampleBadge.save();
      console.log('✅ [Badge Test] Sample badge created');
    }

    // Test badge service functions
    const { getUserBadges, getUserPrimaryBadge } = await import('../services/badge.service');
    console.log('📦 [Badge Test] Badge service functions imported successfully');

    return res.json({
      success: true,
      message: 'Badge system test completed',
      data: {
        badgeConfigKeys: Object.keys(BADGE_CONFIG),
        totalBadgesInDB: badgeCount,
        serviceImported: true,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    console.error('❌ [Badge Test] Error testing badge system:', error);
    return res.status(500).json({
      success: false,
      message: error.message,
      stack: error.stack
    });
  }
};

// Admin: Get all badges statistics
export const getBadgeStats = async (req: Request, res: Response) => {
  try {
    const user = (req as any).user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin only.'
      });
    }

    const stats = await UserBadge.aggregate([
      {
        $group: {
          _id: {
            badgeType: '$badgeType',
            badgeLevel: '$badgeLevel'
          },
          count: { $sum: 1 },
          totalDonations: { $sum: '$totalDonations' },
          totalEvents: { $sum: '$totalEvents' }
        }
      },
      {
        $sort: {
          '_id.badgeType': 1,
          '_id.badgeLevel': 1
        }
      }
    ]);

    const totalBadges = await UserBadge.countDocuments({ isActive: true });
    const uniqueUsers = await UserBadge.distinct('userId', { isActive: true });

    return res.json({
      success: true,
      data: {
        totalBadges,
        uniqueUsers: uniqueUsers.length,
        badgeDistribution: stats
      }
    });
  } catch (error) {
    console.error('Error getting badge stats:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Admin: Get users with specific badge
export const getUsersWithBadge = async (req: Request, res: Response) => {
  try {
    const user = (req as any).user;
    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin only.'
      });
    }

    const { badgeType, badgeLevel } = req.query;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const filter: any = { isActive: true };
    if (badgeType) filter.badgeType = badgeType;
    if (badgeLevel) filter.badgeLevel = parseInt(badgeLevel as string);

    const [badges, total] = await Promise.all([
      UserBadge.find(filter)
        .populate('userId', 'name email')
        .sort({ badgeLevel: -1, earnedAt: -1 })
        .skip(skip)
        .limit(limit),
      UserBadge.countDocuments(filter)
    ]);

    return res.json({
      success: true,
      data: badges,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting users with badge:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
