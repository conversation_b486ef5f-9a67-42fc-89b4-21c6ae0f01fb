import { Request, Response } from 'express';
export declare const getAllPosts: (req: Request, res: Response) => Promise<void>;
export declare const deletePost: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getAllDonations: (req: Request, res: Response) => Promise<void>;
export declare const getAllCampaigns: (req: Request, res: Response) => Promise<void>;
export declare const deleteComment: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const resolveReport: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getCommentReports: (req: Request, res: Response) => Promise<void>;
export declare const resolveCommentReport: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const testCommentReportResolution: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getPostComments: (req: Request, res: Response) => Promise<void>;
export declare const getDashboardStats: (req: Request, res: Response) => Promise<void>;
export declare const getPostsTest: (req: Request, res: Response) => Promise<void>;
export declare const deletePostTest: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getAllUsers: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getUserById: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateUserStatus: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateUserRole: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteUser: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getOnlineStatus: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=admin.controller.d.ts.map