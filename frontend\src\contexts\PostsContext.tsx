import React, { createContext, useContext, useState, useEffect, useRef, ReactNode } from 'react';
import { toast } from 'sonner';
import api from '../services/api';

interface Post {
  _id: string;
  user: {
    _id: string;
    name: string;
    avatar?: string;
  };
  content: string;
  media?: string[];
  createdAt: string;
  reactions?: any[];
  comments?: any[];
  shares?: number;
  userReaction?: string;
}

interface PostsContextType {
  posts: Post[];
  addPost: (post: Post) => void;
  updatePost: (postId: string, updates: Partial<Post>) => void;
  deletePost: (postId: string) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  fetchPosts: (page?: number, limit?: number) => Promise<void>;
  createPost: (content: string, media?: string[]) => Promise<void>;
}

const PostsContext = createContext<PostsContextType | undefined>(undefined);

export const usePostsContext = () => {
  const context = useContext(PostsContext);
  if (!context) {
    throw new Error('usePostsContext must be used within a PostsProvider');
  }
  return context;
};

interface PostsProviderProps {
  children: ReactNode;
}

export const PostsProvider: React.FC<PostsProviderProps> = ({ children }) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);



  // Rate limiting for API calls
  const lastFetchTime = useRef<number>(0);
  const FETCH_COOLDOWN = 3000; // 3 seconds cooldown between API calls

  // Fetch posts from API
  const fetchPosts = async (page = 1, limit = 10) => {

    // Rate limiting check
    const now = Date.now();
    if (now - lastFetchTime.current < FETCH_COOLDOWN) {
      console.log('🚫 API call blocked due to rate limiting');
      return;
    }
    lastFetchTime.current = now;

    try {
      setLoading(true);
      console.log('🔄 Fetching posts from API...');

      // Check if user is authenticated
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('❌ No authentication token found');
        // Fallback to localStorage if no token
        const storedPosts = localStorage.getItem('communityPosts');
        if (storedPosts) {
          try {
            const parsedPosts = JSON.parse(storedPosts);
            console.log('📚 Using stored posts (no auth):', parsedPosts.length);
            setPosts(parsedPosts);
          } catch (parseError) {
            console.error('Error parsing stored posts:', parseError);
            setPosts([]);
          }
        } else {
          setPosts([]);
        }
        setLoading(false);

        // Show a message that user needs to login
        console.log('⚠️ User needs to login to see posts');
        return;
      }

      // Always use user endpoint since it should return all public posts
      // This ensures both admin and regular users see the same posts
      const endpoint = `/api/posts?page=${page}&limit=${limit}`;

      console.log(`🔄 Using user endpoint for all users: ${endpoint}`);
      const response = await api.get(endpoint);

      if (response.data.success) {
        // All users use the same response structure
        const apiPosts = response.data.data.posts;
        console.log('📚 Loaded posts from API:', apiPosts.length);
        console.log('📚 Posts details:', apiPosts.map((p: any) => ({
          id: p._id,
          author: p.user?.name,
          content: p.content?.substring(0, 30),
          userReaction: p.userReaction
        })));
        console.log('📚 First post userReaction:', apiPosts[0]?.userReaction);
        console.log('📚 First post reactions:', apiPosts[0]?.reactions);

        // ALWAYS use API data as the source of truth
        setPosts(apiPosts);

        // Save to localStorage for offline access only
        localStorage.setItem('communityPosts', JSON.stringify(apiPosts));

        console.log('✅ Posts updated from server - all users should see all posts');
      } else {
        throw new Error(response.data.message || 'Failed to fetch posts');
      }
    } catch (error: any) {
      console.error('Error fetching posts:', error);

      // Only show error toast for non-timeout errors
      if (error.code !== 'ECONNABORTED') {
        // Fallback to localStorage if API fails
        const storedPosts = localStorage.getItem('communityPosts');
        if (storedPosts) {
          try {
            const parsedPosts = JSON.parse(storedPosts);
            console.log('📚 Fallback to stored posts:', parsedPosts.length);
            setPosts(parsedPosts);
          } catch (parseError) {
            console.error('Error parsing stored posts:', parseError);
            setPosts([]);
          }
        } else {
          console.log('📚 No stored posts available, starting with empty list');
          setPosts([]);
        }

        // Only show toast for non-auth errors
        if (error.response?.status !== 401) {
          toast.error('Không thể tải bài viết từ server. Hiển thị dữ liệu offline.');
        }
      } else {
        console.log('⏱️ Request timeout, will retry automatically');
        setPosts([]); // Set empty for timeout
      }
    } finally {
      setLoading(false);
    }
  };

  // Create new post via API
  const createPost = async (content: string, media: string[] = []) => {
    try {
      console.log('🚀 [Real API] Creating post...');

      // Check if user is authenticated
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Vui lòng đăng nhập để đăng bài viết');
      }

      // Call real API to create post
      const response = await api.post('/api/posts', {
        content,
        media: media || []
      });

      if (response.data.success) {
        const newPost = response.data.data;
        console.log('✅ [Real API] Post created successfully:', newPost);

        // Add to local state immediately for better UX
        setPosts(prevPosts => [newPost, ...prevPosts]);

        // Update localStorage
        const updatedPosts = [newPost, ...posts];
        localStorage.setItem('communityPosts', JSON.stringify(updatedPosts));

        // Broadcast to other tabs/windows that a new post was created
        window.dispatchEvent(new CustomEvent('postsUpdated', {
          detail: { type: 'refresh', message: 'New post created' }
        }));

        // Also broadcast via localStorage for cross-tab communication
        localStorage.setItem('lastPostUpdate', Date.now().toString());

        toast.success('Bài viết đã được đăng thành công!');
      } else {
        throw new Error(response.data.message || 'Không thể tạo bài viết');
      }

    } catch (error: any) {
      console.error('❌ [Real API] Error creating post:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Không thể đăng bài viết';
      toast.error(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Initialize posts and setup real-time sync
  useEffect(() => {
    // Only fetch posts if user has token
    const token = localStorage.getItem('token');
    if (token) {
      // Load posts from API on mount
      fetchPosts();
    } else {
      console.log('⚠️ No token found, skipping initial posts fetch');
      setLoading(false);
    }

    // Listen for token changes (when user logs in/out)
    const handleTokenChange = (e: StorageEvent) => {
      if (e.key === 'token') {
        if (e.newValue) {
          // User logged in, fetch posts
          console.log('🔑 Token added, fetching posts...');
          fetchPosts();
        } else {
          // User logged out, clear posts
          console.log('🔑 Token removed, clearing posts...');
          setPosts([]);
        }
      }
    };

    // Listen for storage changes from other tabs/windows
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'communityPosts') {
        console.log('🔄 Storage change detected:', e.newValue ? 'Updated' : 'Cleared');
        if (e.newValue) {
          try {
            const updatedPosts = JSON.parse(e.newValue);
            console.log('🔄 Syncing posts from other tab:', updatedPosts.length);
            setPosts(updatedPosts);
          } catch (error) {
            console.error('Error parsing updated posts:', error);
          }
        } else {
          // If posts were cleared in another tab
          console.log('🔄 Posts cleared in another tab');
          setPosts([]);
        }
      } else if (e.key === 'lastPostUpdate') {
        // When another tab updates posts, refresh from server immediately
        console.log('🔄 Post update detected from another tab, refreshing...');
        setTimeout(() => fetchPosts(), 100);
      }
    };

    // Listen for focus events to refresh posts when switching between tabs/accounts (disabled to prevent infinite loop)
    // const handleFocus = () => {
    //   console.log('🔄 Window focused, refreshing posts');
    //   const savedPosts = localStorage.getItem('communityPosts');
    //   if (savedPosts) {
    //     try {
    //       const parsedPosts = JSON.parse(savedPosts);
    //       console.log('🔄 Posts refreshed on focus:', parsedPosts.length);
    //       setPosts(parsedPosts);
    //     } catch (error) {
    //       console.error('Error parsing posts on focus:', error);
    //     }
    //   }
    // };

    // Listen for visibility change (when switching tabs) - disabled to prevent infinite loop
    // const handleVisibilityChange = () => {
    //   if (!document.hidden) {
    //     console.log('🔄 Tab became visible, refreshing posts');
    //     handleFocus();
    //   }
    // };

    // Listen for custom posts update events
    const handlePostsUpdated = (event: any) => {
      console.log('🔄 Custom posts update event:', event.detail);
      const { posts: updatedPosts, action, type } = event.detail;

      if (type === 'refresh') {
        // Refresh posts from server when notified - immediate refresh for reactions/comments
        console.log('🔄 Refreshing posts due to external update...');
        setTimeout(() => fetchPosts(), 50);
      } else if (updatedPosts) {
        setPosts(updatedPosts);
      }
    };

    window.addEventListener('storage', handleTokenChange);
    window.addEventListener('storage', handleStorageChange);
    // window.addEventListener('focus', handleFocus); // Disabled to prevent infinite loop
    window.addEventListener('postsUpdated', handlePostsUpdated);
    // document.addEventListener('visibilitychange', handleVisibilityChange); // Disabled to prevent infinite loop

    // Light polling mechanism to sync posts (every 30 seconds when tab is active)
    const pollInterval = setInterval(() => {
      if (!document.hidden) {
        const now = Date.now();
        // Only fetch if enough time has passed since last fetch (respect rate limiting)
        if (now - lastFetchTime.current >= FETCH_COOLDOWN) {
          console.log('🔄 Light polling: refreshing posts...');
          fetchPosts();
        }
      }
    }, 30000); // 30 seconds - longer interval to avoid interrupting user interactions

    return () => {
      window.removeEventListener('storage', handleTokenChange);
      window.removeEventListener('storage', handleStorageChange);
      // window.removeEventListener('focus', handleFocus); // Already disabled
      window.removeEventListener('postsUpdated', handlePostsUpdated);
      // document.removeEventListener('visibilitychange', handleVisibilityChange); // Already disabled
      clearInterval(pollInterval); // Clear the polling interval
    };
  }, []); // Empty dependency array to run only once

  // Save posts to localStorage whenever posts change (always save, even if empty)
  useEffect(() => {
    // Only save if posts array is not empty or if we explicitly want to clear it
    if (posts.length > 0) {
      localStorage.setItem('communityPosts', JSON.stringify(posts));
      console.log('💾 Auto-saved posts to localStorage:', posts.length);
    }
  }, [posts]);

  const addPost = (newPost: Post) => {
    console.log('➕ Adding new post:', newPost);
    console.log('📊 Current posts before add:', posts.length);

    // Get current posts from localStorage to ensure we have the latest
    const currentStoredPosts = localStorage.getItem('communityPosts');
    let latestPosts: Post[] = [];

    if (currentStoredPosts) {
      try {
        latestPosts = JSON.parse(currentStoredPosts);
      } catch (error) {
        console.error('Error parsing current posts:', error);
        latestPosts = posts;
      }
    } else {
      latestPosts = posts;
    }

    // Add new post to the latest posts
    const updatedPosts = [newPost, ...latestPosts];
    console.log('💾 Saving to localStorage, total posts:', updatedPosts.length);

    // Save to localStorage first
    localStorage.setItem('communityPosts', JSON.stringify(updatedPosts));

    // Then update state
    setPosts(updatedPosts);

    // Trigger a custom event to notify other tabs immediately
    window.dispatchEvent(new CustomEvent('postsUpdated', {
      detail: { posts: updatedPosts, action: 'add', newPost }
    }));

    toast.success('Bài viết đã được chia sẻ với cộng đồng!');
  };

  const updatePost = (postId: string, updates: Partial<Post>) => {
    // Get latest posts from localStorage
    const currentStoredPosts = localStorage.getItem('communityPosts');
    let latestPosts: Post[] = posts;

    if (currentStoredPosts) {
      try {
        latestPosts = JSON.parse(currentStoredPosts);
      } catch (error) {
        console.error('Error parsing current posts for update:', error);
      }
    }

    const updatedPosts = latestPosts.map(post =>
      post._id === postId ? { ...post, ...updates } : post
    );

    // Save to localStorage first
    localStorage.setItem('communityPosts', JSON.stringify(updatedPosts));

    // Update state
    setPosts(updatedPosts);

    // Trigger custom event
    window.dispatchEvent(new CustomEvent('postsUpdated', {
      detail: { posts: updatedPosts, action: 'update', postId, updates }
    }));
  };

  const deletePost = (postId: string) => {
    // Get latest posts from localStorage
    const currentStoredPosts = localStorage.getItem('communityPosts');
    let latestPosts: Post[] = posts;

    if (currentStoredPosts) {
      try {
        latestPosts = JSON.parse(currentStoredPosts);
      } catch (error) {
        console.error('Error parsing current posts for delete:', error);
      }
    }

    const updatedPosts = latestPosts.filter(post => post._id !== postId);

    // Save to localStorage first
    localStorage.setItem('communityPosts', JSON.stringify(updatedPosts));

    // Update state
    setPosts(updatedPosts);

    // Trigger custom event
    window.dispatchEvent(new CustomEvent('postsUpdated', {
      detail: { posts: updatedPosts, action: 'delete', postId }
    }));

    toast.success('Đã xóa bài viết!');
  };

  const value: PostsContextType = {
    posts,
    addPost,
    updatePost,
    deletePost,
    loading,
    setLoading,
    fetchPosts,
    createPost
  };

  return (
    <PostsContext.Provider value={value}>
      {children}
    </PostsContext.Provider>
  );
};

export default PostsContext;
