import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import {
  Search,
  Eye,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar,
  MessageSquare,
  User,
  ChevronDown
} from 'lucide-react';

const ManageCommentReports: React.FC = () => {
  const [reports, setReports] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('pending'); // pending, resolved, dismissed, all
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [showReportDetail, setShowReportDetail] = useState(false);

  // Load comment reports from API
  const fetchCommentReports = async () => {
    console.log('🔧 [ManageCommentReports] Loading comment reports from API');
    setLoading(true);

    try {
      const response = await fetch(`http://localhost:5001/api/admin/comment-reports?status=${filterStatus}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (data.success) {
        console.log('✅ [ManageCommentReports] Loaded comment reports:', data.data.reports.length);
        setReports(data.data.reports);
      } else {
        console.error('❌ [ManageCommentReports] Failed to load reports:', data.message);
        setReports([]);
      }
    } catch (error) {
      console.error('❌ [ManageCommentReports] Error loading reports:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách báo cáo');
      setReports([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCommentReports();
  }, [filterStatus]);

  // Filter reports based on search term
  const filteredReports = reports.filter(report => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      report.reason?.toLowerCase().includes(searchLower) ||
      report.user?.name?.toLowerCase().includes(searchLower) ||
      report.comment?.content?.toLowerCase().includes(searchLower)
    );
  });

  // Resolve comment report
  const handleResolveReport = async (reportId: string, action: 'dismiss' | 'remove_comment') => {
    try {
      console.log('🔄 [Frontend] Resolving report:', { reportId, action });

      const response = await fetch(`http://localhost:5001/api/admin/comment-reports/${reportId}/resolve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ action })
      });

      console.log('📡 [Frontend] Response status:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ [Frontend] Success result:', result);

        if (action === 'remove_comment') {
          toast.success('Đã xóa comment và xử lý báo cáo');
        } else {
          toast.success('Đã bỏ qua báo cáo');
        }

        // Refresh reports
        fetchCommentReports();
        setShowReportDetail(false);
      } else {
        // Try to get error message from response
        let errorMessage = 'Failed to resolve report';
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
          console.error('❌ [Frontend] Server error:', errorData);
        } catch (parseError) {
          console.error('❌ [Frontend] Failed to parse error response:', parseError);
        }

        throw new Error(errorMessage);
      }
    } catch (error: any) {
      console.error('❌ [Frontend] Error resolving report:', error);
      toast.error(error.message || 'Có lỗi xảy ra khi xử lý báo cáo');
    }
  };

  // View report details
  const handleViewReport = (report: any) => {
    setSelectedReport(report);
    setShowReportDetail(true);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'dismissed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Chờ xử lý';
      case 'resolved':
        return 'Đã xử lý';
      case 'dismissed':
        return 'Đã bỏ qua';
      default:
        return status;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Quản lý báo cáo comment</h1>
          <p className="mt-2 text-gray-600">Quản lý và xử lý các báo cáo comment từ người dùng</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Chờ xử lý</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {reports.filter(r => r.status === 'pending').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Đã xử lý</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {reports.filter(r => r.status === 'resolved').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircle className="h-8 w-8 text-gray-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Đã bỏ qua</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {reports.filter(r => r.status === 'dismissed').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MessageSquare className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Tổng báo cáo</p>
                <p className="text-2xl font-semibold text-gray-900">{reports.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Tìm kiếm báo cáo..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={fetchCommentReports}
                  disabled={loading}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  {loading ? 'Đang tải...' : 'Làm mới'}
                </button>

                <div className="relative">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="pending">Chờ xử lý</option>
                    <option value="resolved">Đã xử lý</option>
                    <option value="dismissed">Đã bỏ qua</option>
                    <option value="all">Tất cả</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Reports Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Người báo cáo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Comment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Lý do
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trạng thái
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ngày báo cáo
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-12 text-center">
                      <div className="flex justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      </div>
                      <p className="mt-2 text-gray-500">Đang tải...</p>
                    </td>
                  </tr>
                ) : filteredReports.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-12 text-center">
                      <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-gray-500">Không có báo cáo nào</p>
                    </td>
                  </tr>
                ) : (
                  filteredReports.map((report) => (
                    <tr key={report._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <User className="h-5 w-5 text-gray-600" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {report.user?.name || 'Ẩn danh'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {report.user?.email || 'N/A'}
                            </div>
                          </div>
                        </div>
                      </td>

                      <td className="px-6 py-4">
                        <div className="max-w-xs">
                          <p className="text-sm text-gray-900 line-clamp-2">
                            {report.comment?.content || 'Comment đã bị xóa'}
                          </p>
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{report.reason}</div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                          {getStatusText(report.status)}
                        </span>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(report.createdAt)}
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleViewReport(report)}
                            className="text-blue-600 hover:text-blue-900 p-1 rounded"
                            title="Xem chi tiết"
                          >
                            <Eye className="h-4 w-4" />
                          </button>

                          {report.status === 'pending' && (
                            <>
                              <button
                                onClick={() => handleResolveReport(report._id, 'dismiss')}
                                className="text-green-600 hover:text-green-900 p-1 rounded"
                                title="Bỏ qua báo cáo"
                              >
                                <CheckCircle className="h-4 w-4" />
                              </button>

                              <button
                                onClick={() => handleResolveReport(report._id, 'remove_comment')}
                                className="text-red-600 hover:text-red-900 p-1 rounded"
                                title="Xóa comment"
                              >
                                <XCircle className="h-4 w-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Report Detail Modal */}
        {showReportDetail && selectedReport && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Chi tiết báo cáo comment</h3>
                  <button
                    onClick={() => setShowReportDetail(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Người báo cáo</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedReport.user?.name || 'Ẩn danh'}</p>
                    <p className="text-xs text-gray-500">{selectedReport.user?.email || 'N/A'}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Lý do báo cáo</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedReport.reason}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Nội dung comment</label>
                    <div className="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                      <p className="text-sm text-gray-900">{selectedReport.comment?.content || 'Comment đã bị xóa'}</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Bài viết liên quan</label>
                    <div className="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                      <p className="text-sm text-gray-900 line-clamp-3">
                        {selectedReport.post?.content || 'Bài viết đã bị xóa'}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Trạng thái</label>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedReport.status)}`}>
                        {getStatusText(selectedReport.status)}
                      </span>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Ngày báo cáo</label>
                      <p className="mt-1 text-sm text-gray-900">{formatDate(selectedReport.createdAt)}</p>
                    </div>
                  </div>

                  {selectedReport.resolvedAt && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Ngày xử lý</label>
                        <p className="mt-1 text-sm text-gray-900">{formatDate(selectedReport.resolvedAt)}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Người xử lý</label>
                        <p className="mt-1 text-sm text-gray-900">{selectedReport.resolvedBy?.name || 'N/A'}</p>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  {selectedReport.status === 'pending' && (
                    <div className="border-t pt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-3">Xử lý báo cáo</label>
                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleResolveReport(selectedReport._id, 'dismiss')}
                          className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                          Bỏ qua báo cáo
                        </button>
                        <button
                          onClick={() => handleResolveReport(selectedReport._id, 'remove_comment')}
                          className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                        >
                          Xóa comment
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManageCommentReports;
