import mongoose from 'mongoose';
import { UserBadge, BADGE_CONFIG, IUserBadge } from '../models/UserBadge';
import { Donation } from '../models/donation.model';
import EventRegistration from '../models/EventRegistration';
import { Post } from '../models/Post';
import { createNotification } from './notification.service';

export interface BadgeEarnedResult {
  earned: boolean;
  badge?: IUserBadge;
  levelUp?: boolean;
  previousLevel?: number;
}

// Calculate user's donation statistics
export const calculateDonationStats = async (userId: mongoose.Types.ObjectId) => {
  const stats = await Donation.aggregate([
    {
      $match: {
        userId: userId,
        status: 'success'
      }
    },
    {
      $group: {
        _id: null,
        totalAmount: { $sum: '$amount' },
        totalDonations: { $sum: 1 },
        lastDonation: { $max: '$createdAt' },
        lastAmount: { $last: '$amount' }
      }
    }
  ]);

  return stats[0] || {
    totalAmount: 0,
    totalDonations: 0,
    lastDonation: null,
    lastAmount: 0
  };
};

// Calculate user's event participation
export const calculateEventStats = async (userId: mongoose.Types.ObjectId) => {
  const stats = await EventRegistration.aggregate([
    {
      $match: {
        userId: userId,
        status: 'registered'
      }
    },
    {
      $group: {
        _id: null,
        totalEvents: { $sum: 1 },
        lastEvent: { $max: '$registrationDate' }
      }
    }
  ]);

  return stats[0] || {
    totalEvents: 0,
    lastEvent: null
  };
};

// Calculate user's post statistics
export const calculatePostStats = async (userId: mongoose.Types.ObjectId) => {
  const stats = await Post.aggregate([
    {
      $match: {
        user: userId
      }
    },
    {
      $group: {
        _id: null,
        totalPosts: { $sum: 1 },
        totalLikes: { $sum: { $size: '$reactions' } },
        totalComments: { $sum: { $size: '$comments' } }
      }
    }
  ]);

  return stats[0] || {
    totalPosts: 0,
    totalLikes: 0,
    totalComments: 0
  };
};

// Check and award supporter badge
export const checkSupporterBadge = async (userId: mongoose.Types.ObjectId): Promise<BadgeEarnedResult> => {
  const donationStats = await calculateDonationStats(userId);
  const { totalAmount, totalDonations } = donationStats;

  // Find current supporter badge
  let currentBadge = await UserBadge.findOne({
    userId,
    badgeType: 'supporter',
    isActive: true
  });

  const supporterLevels = BADGE_CONFIG.supporter.levels;
  let newLevel = 0;

  // Determine the highest level earned
  for (let level = 5; level >= 1; level--) {
    const requirement = supporterLevels[level as keyof typeof supporterLevels];
    if (totalDonations >= requirement.minDonations && totalAmount >= requirement.minAmount) {
      newLevel = level;
      break;
    }
  }

  if (newLevel === 0) {
    return { earned: false };
  }

  const previousLevel = currentBadge?.badgeLevel || 0;
  const levelUp = newLevel > previousLevel;

  if (!currentBadge) {
    // Create new badge
    currentBadge = new UserBadge({
      userId,
      badgeType: 'supporter',
      badgeLevel: newLevel,
      totalDonations,
      metadata: {
        lastDonationAmount: donationStats.lastAmount,
        lastDonationDate: donationStats.lastDonation
      }
    });
    await currentBadge.save();

    // Send notification for new badge
    await createNotification({
      userId,
      type: 'system',
      title: '🎉 Chúc mừng! Bạn đã nhận được huy hiệu mới!',
      message: `Bạn đã trở thành ${BADGE_CONFIG.supporter.icon} ${BADGE_CONFIG.supporter.name} - ${supporterLevels[newLevel as keyof typeof supporterLevels].name}! Cảm ơn những đóng góp tuyệt vời của bạn.`,
      data: {
        badgeType: 'supporter',
        badgeLevel: newLevel,
        actionType: 'created'
      },
      priority: 'medium',
      sendEmail: false
    });

    return { earned: true, badge: currentBadge, levelUp: true, previousLevel: 0 };
  } else if (levelUp) {
    // Update existing badge
    currentBadge.badgeLevel = newLevel;
    currentBadge.totalDonations = totalDonations;
    currentBadge.metadata = {
      ...currentBadge.metadata,
      lastDonationAmount: donationStats.lastAmount,
      lastDonationDate: donationStats.lastDonation
    };
    await currentBadge.save();

    // Send notification for level up
    await createNotification({
      userId,
      type: 'system',
      title: '⬆️ Huy hiệu của bạn đã được nâng cấp!',
      message: `${BADGE_CONFIG.supporter.icon} ${BADGE_CONFIG.supporter.name} của bạn đã lên cấp ${supporterLevels[newLevel as keyof typeof supporterLevels].name}! Tiếp tục phát huy nhé!`,
      data: {
        badgeType: 'supporter',
        badgeLevel: newLevel,
        actionType: 'updated'
      },
      priority: 'medium',
      sendEmail: false
    });

    return { earned: true, badge: currentBadge, levelUp: true, previousLevel };
  }

  return { earned: true, badge: currentBadge, levelUp: false };
};

// Check and award volunteer badge
export const checkVolunteerBadge = async (userId: mongoose.Types.ObjectId): Promise<BadgeEarnedResult> => {
  const eventStats = await calculateEventStats(userId);
  const { totalEvents } = eventStats;

  let currentBadge = await UserBadge.findOne({
    userId,
    badgeType: 'volunteer',
    isActive: true
  });

  const volunteerLevels = BADGE_CONFIG.volunteer.levels;
  let newLevel = 0;

  for (let level = 5; level >= 1; level--) {
    const requirement = volunteerLevels[level as keyof typeof volunteerLevels];
    if (totalEvents >= requirement.minEvents) {
      newLevel = level;
      break;
    }
  }

  if (newLevel === 0) {
    return { earned: false };
  }

  const previousLevel = currentBadge?.badgeLevel || 0;
  const levelUp = newLevel > previousLevel;

  if (!currentBadge) {
    currentBadge = new UserBadge({
      userId,
      badgeType: 'volunteer',
      badgeLevel: newLevel,
      totalEvents
    });
    await currentBadge.save();

    await createNotification({
      userId,
      type: 'system',
      title: '🎉 Chúc mừng! Bạn đã nhận được huy hiệu mới!',
      message: `Bạn đã trở thành ${BADGE_CONFIG.volunteer.icon} ${BADGE_CONFIG.volunteer.name} - ${volunteerLevels[newLevel as keyof typeof volunteerLevels].name}! Cảm ơn sự tham gia tích cực của bạn.`,
      data: {
        badgeType: 'volunteer',
        badgeLevel: newLevel,
        actionType: 'created'
      },
      priority: 'medium',
      sendEmail: false
    });

    return { earned: true, badge: currentBadge, levelUp: true, previousLevel: 0 };
  } else if (levelUp) {
    currentBadge.badgeLevel = newLevel;
    currentBadge.totalEvents = totalEvents;
    await currentBadge.save();

    await createNotification({
      userId,
      type: 'system',
      title: '⬆️ Huy hiệu của bạn đã được nâng cấp!',
      message: `${BADGE_CONFIG.volunteer.icon} ${BADGE_CONFIG.volunteer.name} của bạn đã lên cấp ${volunteerLevels[newLevel as keyof typeof volunteerLevels].name}! Tiếp tục tham gia nhé!`,
      data: {
        badgeType: 'volunteer',
        badgeLevel: newLevel,
        actionType: 'updated'
      },
      priority: 'medium',
      sendEmail: false
    });

    return { earned: true, badge: currentBadge, levelUp: true, previousLevel };
  }

  return { earned: true, badge: currentBadge, levelUp: false };
};

// Get user's active badges
export const getUserBadges = async (userId: mongoose.Types.ObjectId) => {
  const badges = await UserBadge.find({
    userId,
    isActive: true
  }).sort({ badgeLevel: -1, earnedAt: -1 });

  return badges.map(badge => ({
    ...badge.toObject(),
    config: BADGE_CONFIG[badge.badgeType],
    levelName: BADGE_CONFIG[badge.badgeType].levels[badge.badgeLevel as keyof typeof BADGE_CONFIG[typeof badge.badgeType]['levels']].name
  }));
};

// Get user's primary badge (highest level)
export const getUserPrimaryBadge = async (userId: mongoose.Types.ObjectId) => {
  const badge = await UserBadge.findOne({
    userId,
    isActive: true
  }).sort({ badgeLevel: -1, earnedAt: -1 });

  if (!badge) return null;

  return {
    ...badge.toObject(),
    config: BADGE_CONFIG[badge.badgeType],
    levelName: BADGE_CONFIG[badge.badgeType].levels[badge.badgeLevel as keyof typeof BADGE_CONFIG[typeof badge.badgeType]['levels']].name
  };
};

// Check all badges for a user (called after donations, events, etc.)
export const checkAllBadges = async (userId: mongoose.Types.ObjectId) => {
  const results = await Promise.allSettled([
    checkSupporterBadge(userId),
    checkVolunteerBadge(userId)
  ]);

  const earnedBadges = results
    .filter(result => result.status === 'fulfilled' && result.value.earned)
    .map(result => (result as PromiseFulfilledResult<BadgeEarnedResult>).value);

  return earnedBadges;
};
