import { Request, Response } from 'express';
export declare const getUserBadgesController: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getUserPrimaryBadgeController: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getPublicUserBadges: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getPublicUserPrimaryBadge: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const checkUserBadges: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getBadgeConfig: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const testBadgeSystem: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getBadgeStats: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
export declare const getUsersWithBadge: (req: Request, res: Response) => Promise<Response<any, Record<string, any>>>;
//# sourceMappingURL=badgeController.d.ts.map