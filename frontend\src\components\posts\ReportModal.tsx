import React, { useState } from 'react';

interface ReportModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (reason: string) => void;
}

const reasons = [
  'Nội dung không phù hợp',
  'Spam',
  '<PERSON><PERSON><PERSON> từ kích động',
  'Thông tin sai sự thật',
  'Khác',
];

const ReportModal: React.FC<ReportModalProps> = ({ open, onClose, onSubmit }) => {
  const [reason, setReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);



  const handleSubmit = async () => {
    const finalReason = reason === 'Khác' ? customReason : reason;
    if (!finalReason.trim()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(finalReason);
      setReason('');
      setCustomReason('');
      onClose();
    } catch (error) {
      console.error('Error submitting report:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setReason('');
    setCustomReason('');
    onClose();
  };

  if (!open) {
    return null;
  }



  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      style={{
        zIndex: 99999,
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      onClick={handleClose}
    >
      <div
        className="bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-xl transform transition-all duration-200 scale-100"
        style={{
          backgroundColor: 'white',
          zIndex: 100000
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-gray-900">Báo cáo bài viết</h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Chọn lý do báo cáo:
          </label>
          <select
            className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-red-500 focus:border-transparent"
            value={reason}
            onChange={e => setReason(e.target.value)}
          >
            <option value="">-- Chọn lý do --</option>
            {reasons.map(r => <option key={r} value={r}>{r}</option>)}
          </select>
        </div>

        {reason === 'Khác' && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mô tả chi tiết:
            </label>
            <textarea
              id="report-custom-reason"
              name="customReason"
              className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-red-500 focus:border-transparent"
              rows={3}
              placeholder="Vui lòng mô tả lý do báo cáo..."
              value={customReason}
              onChange={e => setCustomReason(e.target.value)}
            />
          </div>
        )}

        <div className="flex justify-end gap-3">
          <button
            onClick={handleClose}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            disabled={isSubmitting}
          >
            Hủy
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            disabled={!reason || (reason === 'Khác' && !customReason.trim()) || isSubmitting}
          >
            {isSubmitting && (
              <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {isSubmitting ? 'Đang gửi...' : 'Gửi báo cáo'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReportModal;