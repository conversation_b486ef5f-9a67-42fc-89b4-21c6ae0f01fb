"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.reportComment = exports.deletePost = exports.deleteComment = exports.replyToComment = exports.reactToComment = exports.commentOnPost = exports.reactToPost = exports.getPostById = exports.createPost = exports.getPosts = void 0;
const Post_1 = require("../models/Post");
const Comment_1 = require("../models/Comment");
const mongoose_1 = __importDefault(require("mongoose"));
// Rate limiting map to track requests per IP
const requestCounts = new Map();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_REQUESTS_PER_WINDOW = 100; // Max 100 requests per minute (increased for development)
// GET /posts - Lấy danh sách bài viết công khai (newsfeed)
const getPosts = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d;
    try {
        // Rate limiting check
        const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
        const now = Date.now();
        if (!requestCounts.has(clientIP)) {
            requestCounts.set(clientIP, { count: 1, lastReset: now });
        }
        else {
            const clientData = requestCounts.get(clientIP);
            // Reset count if window has passed
            if (now - clientData.lastReset > RATE_LIMIT_WINDOW) {
                clientData.count = 1;
                clientData.lastReset = now;
            }
            else {
                clientData.count++;
                // Block if exceeded rate limit
                if (clientData.count > MAX_REQUESTS_PER_WINDOW) {
                    console.log(`🚫 Rate limit exceeded for IP: ${clientIP} (${clientData.count} requests)`);
                    return res.status(429).json({
                        success: false,
                        message: 'Too many requests. Please try again later.',
                        retryAfter: Math.ceil((RATE_LIMIT_WINDOW - (now - clientData.lastReset)) / 1000)
                    });
                }
            }
        }
        console.log(`📊 Request from ${clientIP}: ${(_a = requestCounts.get(clientIP)) === null || _a === void 0 ? void 0 : _a.count}/${MAX_REQUESTS_PER_WINDOW}`);
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        // Query filters
        const search = req.query.search;
        const author = req.query.author;
        const hasMedia = req.query.hasMedia === 'true';
        // Check if this is a user-specific request (from /posts/user/:userId route)
        const userIdFromParams = req.params.userId;
        // Build query
        const query = { visibility: 'public' };
        if (search) {
            query.content = { $regex: search, $options: 'i' };
        }
        if (author) {
            query.user = author;
        }
        // If requesting specific user's posts, filter by that user
        if (userIdFromParams) {
            query.user = userIdFromParams;
            // For user's own posts, also include private posts if it's their own profile
            if (userIdFromParams === ((_c = (_b = req.user) === null || _b === void 0 ? void 0 : _b._id) === null || _c === void 0 ? void 0 : _c.toString())) {
                delete query.visibility; // Show all posts for own profile
            }
        }
        if (hasMedia) {
            query.media = { $exists: true, $not: { $size: 0 } };
        }
        const posts = yield Post_1.Post.find(query)
            .populate('user', 'name avatar email')
            .populate('reactions.user', 'name avatar')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean();
        // Manually populate ALL comments for each post (including replies)
        for (const post of posts) {
            const allComments = yield Comment_1.Comment.find({ post: post._id })
                .populate('user', 'name avatar')
                .populate('reactions.user', 'name avatar')
                .lean();
            post.comments = allComments;
            console.log(`🔍 Post ${post._id}: Loaded ${allComments.length} total comments from DB`);
        }
        // Process comments and replies properly
        const userId = (_d = req.user) === null || _d === void 0 ? void 0 : _d._id;
        const postsWithProcessedComments = posts.map((post) => {
            var _a;
            // Find current user's reaction for post
            const userReaction = (_a = post.reactions) === null || _a === void 0 ? void 0 : _a.find((reaction) => { var _a; return ((_a = reaction.user._id) === null || _a === void 0 ? void 0 : _a.toString()) === (userId === null || userId === void 0 ? void 0 : userId.toString()); });
            // Separate top-level comments and replies
            const allComments = post.comments || [];
            console.log(`🔍 Post ${post._id} - Total comments from DB:`, allComments.length);
            const topLevelComments = allComments.filter((comment) => {
                const isTopLevel = !comment.parentComment;
                console.log(`  📝 Comment ${comment._id}: parentComment=${comment.parentComment}, isTopLevel=${isTopLevel}`);
                return isTopLevel;
            });
            const replies = allComments.filter((comment) => {
                const isReply = !!comment.parentComment;
                if (isReply) {
                    console.log(`  🔄 Reply ${comment._id}: parentComment=${comment.parentComment}`);
                }
                return isReply;
            });
            console.log(`📊 Post ${post._id}: ${topLevelComments.length} top-level, ${replies.length} replies`);
            // Group replies by parent comment
            const repliesByParent = replies.reduce((acc, reply) => {
                const parentId = reply.parentComment.toString();
                console.log(`🔗 Grouping reply ${reply._id} under parent ${parentId}`);
                if (!acc[parentId])
                    acc[parentId] = [];
                acc[parentId].push(reply);
                return acc;
            }, {});
            console.log(`📋 Replies grouped:`, Object.keys(repliesByParent).map(parentId => ({
                parentId,
                repliesCount: repliesByParent[parentId].length
            })));
            // Add replies to their parent comments and userReaction
            const processedComments = topLevelComments.map((comment) => {
                var _a;
                const commentUserReaction = (_a = comment.reactions) === null || _a === void 0 ? void 0 : _a.find((reaction) => { var _a; return ((_a = reaction.user._id) === null || _a === void 0 ? void 0 : _a.toString()) === (userId === null || userId === void 0 ? void 0 : userId.toString()); });
                const commentId = comment._id.toString();
                const commentReplies = repliesByParent[commentId] || [];
                console.log(`🔗 Comment ${commentId} looking for replies in:`, Object.keys(repliesByParent));
                console.log(`🔗 Comment ${commentId} found ${commentReplies.length} replies`);
                const processedReplies = commentReplies.map((reply) => {
                    var _a, _b;
                    console.log(`  ✅ Processing reply ${reply._id}: "${(_a = reply.content) === null || _a === void 0 ? void 0 : _a.substring(0, 30)}..."`);
                    const replyUserReaction = (_b = reply.reactions) === null || _b === void 0 ? void 0 : _b.find((reaction) => { var _a; return ((_a = reaction.user._id) === null || _a === void 0 ? void 0 : _a.toString()) === (userId === null || userId === void 0 ? void 0 : userId.toString()); });
                    return Object.assign(Object.assign({}, reply), { userReaction: (replyUserReaction === null || replyUserReaction === void 0 ? void 0 : replyUserReaction.type) || null });
                });
                const result = Object.assign(Object.assign({}, comment), { userReaction: (commentUserReaction === null || commentUserReaction === void 0 ? void 0 : commentUserReaction.type) || null, replies: processedReplies });
                console.log(`📝 Final comment ${commentId} has ${result.replies.length} replies attached`);
                return result;
            });
            return Object.assign(Object.assign({}, post), { userReaction: (userReaction === null || userReaction === void 0 ? void 0 : userReaction.type) || null, comments: processedComments });
        });
        const total = yield Post_1.Post.countDocuments(query);
        const totalPages = Math.ceil(total / limit);
        console.log(`📊 Posts endpoint: Found ${posts.length} posts (total: ${total}) for query:`, query);
        console.log(`📊 Posts from users:`, posts.map(p => { var _a, _b; return ({ id: p._id, author: (_a = p.user) === null || _a === void 0 ? void 0 : _a.name, content: (_b = p.content) === null || _b === void 0 ? void 0 : _b.substring(0, 30) }); }));
        // Debug: Check all comments in database for the first post with comments
        const postWithComments = posts.find((p) => p.comments && p.comments.length > 0);
        if (postWithComments) {
            console.log(`🔍 Checking all comments for post ${postWithComments._id}:`);
            const allCommentsForPost = yield Comment_1.Comment.find({ post: postWithComments._id })
                .populate('user', 'name')
                .lean();
            console.log(`📋 Found ${allCommentsForPost.length} total comments in DB:`);
            allCommentsForPost.forEach((comment, index) => {
                var _a, _b;
                console.log(`  ${index + 1}. ${comment._id}: "${(_a = comment.content) === null || _a === void 0 ? void 0 : _a.substring(0, 30)}..." by ${(_b = comment.user) === null || _b === void 0 ? void 0 : _b.name}`);
                console.log(`     parentComment: ${comment.parentComment || 'null (top-level)'}`);
            });
        }
        // Debug processed comments and replies
        postsWithProcessedComments.forEach((post, index) => {
            if (post.comments && post.comments.length > 0) {
                console.log(`📝 Post ${index + 1} has ${post.comments.length} top-level comments:`);
                post.comments.forEach((comment, commentIndex) => {
                    var _a, _b;
                    console.log(`  💬 Comment ${commentIndex + 1}: "${(_a = comment.content) === null || _a === void 0 ? void 0 : _a.substring(0, 30)}..." by ${(_b = comment.user) === null || _b === void 0 ? void 0 : _b.name}`);
                    if (comment.replies && comment.replies.length > 0) {
                        console.log(`    🔄 Has ${comment.replies.length} replies:`);
                        comment.replies.forEach((reply, replyIndex) => {
                            var _a, _b;
                            console.log(`      ↳ Reply ${replyIndex + 1}: "${(_a = reply.content) === null || _a === void 0 ? void 0 : _a.substring(0, 30)}..." by ${(_b = reply.user) === null || _b === void 0 ? void 0 : _b.name}`);
                        });
                    }
                    else {
                        console.log(`    🔄 No replies`);
                    }
                });
            }
            else {
                console.log(`📝 Post ${index + 1} has no comments`);
            }
        });
        res.json({
            success: true,
            data: {
                posts: postsWithProcessedComments,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalPosts: total,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }
        });
    }
    catch (error) {
        console.error('Error fetching posts:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải bài viết',
            error: error.message
        });
    }
});
exports.getPosts = getPosts;
// POST /posts - Tạo bài viết mới
const createPost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d;
    try {
        const { content, media, visibility = 'public' } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!content || content.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Nội dung bài viết không được để trống'
            });
        }
        const newPost = new Post_1.Post({
            user: userId,
            content: content.trim(),
            media: media || [],
            visibility
        });
        console.log(`📝 Creating post with visibility: ${visibility} by user: ${((_b = req.user) === null || _b === void 0 ? void 0 : _b.name) || 'Unknown'} (role: ${((_c = req.user) === null || _c === void 0 ? void 0 : _c.role) || 'Unknown'})`);
        yield newPost.save();
        // Populate user info for response
        const populatedPost = yield Post_1.Post.findById(newPost._id)
            .populate('user', 'name avatar email')
            .lean();
        // Emit socket event for real-time updates
        if (req.io) {
            req.io.emit('new-post', {
                post: populatedPost,
                message: `${(_d = populatedPost === null || populatedPost === void 0 ? void 0 : populatedPost.user) === null || _d === void 0 ? void 0 : _d.name} đã đăng bài viết mới`
            });
        }
        res.status(201).json({
            success: true,
            message: 'Đăng bài thành công',
            data: { post: populatedPost }
        });
    }
    catch (error) {
        console.error('Error creating post:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi đăng bài viết',
            error: error.message
        });
    }
});
exports.createPost = createPost;
// GET /posts/:id - Lấy chi tiết bài viết
const getPostById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            return res.status(400).json({
                success: false,
                message: 'ID bài viết không hợp lệ'
            });
        }
        const post = yield Post_1.Post.findOne({ _id: id, visibility: 'public' })
            .populate('user', 'name avatar email')
            .populate({
            path: 'comments',
            populate: {
                path: 'user',
                select: 'name avatar'
            }
        })
            .lean();
        if (!post) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài viết'
            });
        }
        res.json({
            success: true,
            data: { post }
        });
    }
    catch (error) {
        console.error('Error fetching post:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tải bài viết',
            error: error.message
        });
    }
});
exports.getPostById = getPostById;
// POST /posts/:id/react - Thả cảm xúc
const reactToPost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id } = req.params;
        const { type } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            return res.status(400).json({
                success: false,
                message: 'ID bài viết không hợp lệ'
            });
        }
        const validReactions = ['like', 'love', 'haha', 'wow', 'sad', 'angry'];
        if (type && !validReactions.includes(type)) {
            return res.status(400).json({
                success: false,
                message: 'Loại cảm xúc không hợp lệ'
            });
        }
        const post = yield Post_1.Post.findOne({ _id: id, visibility: 'public' });
        if (!post) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài viết'
            });
        }
        // Remove existing reaction from this user
        post.reactions = post.reactions.filter((reaction) => reaction.user.toString() !== (userId === null || userId === void 0 ? void 0 : userId.toString()));
        // Add new reaction if type is provided
        if (type) {
            post.reactions.push({ user: userId, type });
        }
        yield post.save();
        // Emit socket event
        if (req.io) {
            req.io.emit('post-reaction', {
                postId: id,
                userId,
                type,
                totalReactions: post.reactions.length
            });
        }
        res.json({
            success: true,
            message: type ? 'Đã thả cảm xúc' : 'Đã bỏ cảm xúc',
            data: {
                reactions: post.reactions,
                totalReactions: post.reactions.length,
                userReaction: type || null
            }
        });
    }
    catch (error) {
        console.error('Error reacting to post:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi thả cảm xúc',
            error: error.message
        });
    }
});
exports.reactToPost = reactToPost;
// POST /posts/:id/comment - Bình luận bài viết
const commentOnPost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { id } = req.params;
        const { content } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            return res.status(400).json({
                success: false,
                message: 'ID bài viết không hợp lệ'
            });
        }
        if (!content || content.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Nội dung bình luận không được để trống'
            });
        }
        const post = yield Post_1.Post.findOne({ _id: id, visibility: 'public' });
        if (!post) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài viết'
            });
        }
        const newComment = new Comment_1.Comment({
            user: userId,
            post: id,
            content: content.trim()
        });
        yield newComment.save();
        post.comments.push(newComment._id);
        yield post.save();
        // Populate comment for response
        const populatedComment = yield Comment_1.Comment.findById(newComment._id)
            .populate('user', 'name avatar')
            .lean();
        // Emit socket event
        if (req.io) {
            req.io.emit('new-comment', {
                postId: id,
                comment: populatedComment
            });
        }
        // Tạo thông báo cho chủ bài viết (nếu không phải chính họ comment)
        if (post.user.toString() !== (userId === null || userId === void 0 ? void 0 : userId.toString())) {
            try {
                console.log('🔔 Creating comment notification for post owner:', {
                    postOwnerId: post.user,
                    commenterId: userId,
                    postId: id
                });
                const { createNotification } = yield Promise.resolve().then(() => __importStar(require('../services/notification.service')));
                const commenterUser = yield (yield Promise.resolve().then(() => __importStar(require('../models/user.model')))).User.findById(userId).select('name');
                const commenterName = (commenterUser === null || commenterUser === void 0 ? void 0 : commenterUser.name) || 'Ai đó';
                yield createNotification({
                    userId: post.user,
                    type: 'comment',
                    title: `${commenterName} đã bình luận về bài viết của bạn`,
                    message: `"${content.trim().substring(0, 100)}${content.trim().length > 100 ? '...' : ''}"`,
                    priority: 'medium',
                    data: {
                        postId: new mongoose_1.default.Types.ObjectId(id),
                        commentId: newComment._id,
                        relatedUserName: commenterName,
                        actionType: 'created'
                    },
                    sendEmail: true
                });
                console.log('✅ Comment notification created for post owner');
            }
            catch (notificationError) {
                console.error('❌ Error creating comment notification:', notificationError);
                // Don't fail the comment if notification fails
            }
        }
        else {
            console.log('🔕 Skipping notification - user commenting on own post');
        }
        res.status(201).json({
            success: true,
            message: 'Đã thêm bình luận',
            data: { comment: populatedComment }
        });
    }
    catch (error) {
        console.error('Error commenting on post:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi bình luận',
            error: error.message
        });
    }
});
exports.commentOnPost = commentOnPost;
// React to comment
const reactToComment = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { type } = req.body;
        const { commentId } = req.params;
        const userId = req.user._id;
        console.log('🎯 Comment reaction request:', { commentId, type, userId });
        if (!mongoose_1.default.Types.ObjectId.isValid(commentId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid comment ID'
            });
        }
        const comment = yield Comment_1.Comment.findById(commentId);
        if (!comment) {
            return res.status(404).json({
                success: false,
                message: 'Comment not found'
            });
        }
        // Remove existing reaction from this user
        comment.reactions = comment.reactions.filter((r) => r.user.toString() !== userId.toString());
        // Add new reaction if type is provided and not empty
        if (type && type.trim() !== '') {
            comment.reactions.push({
                user: userId,
                type: type.trim(),
                createdAt: new Date()
            });
        }
        comment.updatedAt = new Date();
        yield comment.save();
        // Populate reactions.user to get user details for response
        const populatedComment = yield Comment_1.Comment.findById(commentId)
            .populate('reactions.user', 'name avatar')
            .lean();
        // Calculate reaction summary
        const reactionSummary = ((_a = populatedComment === null || populatedComment === void 0 ? void 0 : populatedComment.reactions) === null || _a === void 0 ? void 0 : _a.reduce((acc, reaction) => {
            acc[reaction.type] = (acc[reaction.type] || 0) + 1;
            return acc;
        }, {})) || {};
        // Find current user's reaction
        const userReaction = (_b = populatedComment === null || populatedComment === void 0 ? void 0 : populatedComment.reactions) === null || _b === void 0 ? void 0 : _b.find((r) => r.user._id.toString() === userId.toString());
        console.log('✅ Comment reaction success:', {
            commentId,
            userReaction: (userReaction === null || userReaction === void 0 ? void 0 : userReaction.type) || null,
            totalReactions: (populatedComment === null || populatedComment === void 0 ? void 0 : populatedComment.reactions.length) || 0
        });
        res.json({
            success: true,
            data: {
                commentId,
                reactions: (populatedComment === null || populatedComment === void 0 ? void 0 : populatedComment.reactions) || [],
                reactionSummary,
                userReaction: (userReaction === null || userReaction === void 0 ? void 0 : userReaction.type) || null,
                totalReactions: (populatedComment === null || populatedComment === void 0 ? void 0 : populatedComment.reactions.length) || 0
            }
        });
    }
    catch (err) {
        console.error('❌ Error reacting to comment:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});
exports.reactToComment = reactToComment;
// Reply to comment
const replyToComment = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { content } = req.body;
        const { commentId } = req.params;
        const userId = req.user._id;
        console.log('🎯 Comment reply request:', { commentId, content, userId });
        if (!content || content.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Reply content cannot be empty'
            });
        }
        if (!mongoose_1.default.Types.ObjectId.isValid(commentId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid comment ID'
            });
        }
        const parentComment = yield Comment_1.Comment.findById(commentId);
        if (!parentComment) {
            return res.status(404).json({
                success: false,
                message: 'Parent comment not found'
            });
        }
        // Create reply comment (simple structure)
        const reply = yield Comment_1.Comment.create({
            user: userId,
            post: parentComment.post,
            content: content.trim(),
            parentComment: commentId, // This makes it a reply
            reactions: [],
            createdAt: new Date(),
            updatedAt: new Date()
        });
        console.log('✅ Reply created:', {
            parentCommentId: commentId,
            replyId: reply._id,
            content: content.trim()
        });
        // Populate reply with user data
        const populatedReply = yield Comment_1.Comment.findById(reply._id)
            .populate('user', 'name avatar')
            .lean();
        console.log('✅ Comment reply success:', {
            replyId: reply._id,
            parentCommentId: commentId,
            content: content.trim()
        });
        // Tạo thông báo cho người được reply (nếu không phải chính họ reply)
        if (parentComment.user.toString() !== userId.toString()) {
            try {
                const { createNotification } = yield Promise.resolve().then(() => __importStar(require('../services/notification.service')));
                const { User } = yield Promise.resolve().then(() => __importStar(require('../models/user.model')));
                const replierUser = yield User.findById(userId).select('name');
                const replierName = (replierUser === null || replierUser === void 0 ? void 0 : replierUser.name) || 'Ai đó';
                yield createNotification({
                    userId: parentComment.user,
                    type: 'comment',
                    title: `${replierName} đã phản hồi bình luận của bạn`,
                    message: `"${content.trim().substring(0, 100)}${content.trim().length > 100 ? '...' : ''}"`,
                    priority: 'medium',
                    data: {
                        postId: parentComment.post,
                        commentId: parentComment._id,
                        relatedUserName: replierName,
                        actionType: 'created'
                    },
                    sendEmail: true
                });
                console.log('✅ Reply notification created for comment owner');
            }
            catch (notificationError) {
                console.error('❌ Error creating reply notification:', notificationError);
                // Don't fail the reply if notification fails
            }
        }
        res.json({
            success: true,
            data: {
                reply: populatedReply,
                parentCommentId: commentId
            }
        });
    }
    catch (err) {
        console.error('❌ Error replying to comment:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});
exports.replyToComment = replyToComment;
// DELETE /posts/comments/:commentId - Xóa comment của chính mình
const deleteComment = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        const { commentId } = req.params;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!mongoose_1.default.Types.ObjectId.isValid(commentId)) {
            return res.status(400).json({
                success: false,
                message: 'ID comment không hợp lệ'
            });
        }
        const comment = yield Comment_1.Comment.findById(commentId);
        if (!comment) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy comment'
            });
        }
        // Kiểm tra quyền xóa (chỉ chủ sở hữu hoặc admin)
        if (comment.user.toString() !== userId.toString() && ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Bạn không có quyền xóa comment này'
            });
        }
        // Xóa comment và tất cả replies
        yield Comment_1.Comment.deleteMany({
            $or: [
                { _id: commentId },
                { parentComment: commentId }
            ]
        });
        // Cập nhật post để xóa comment khỏi danh sách
        yield Post_1.Post.updateOne({ comments: commentId }, { $pull: { comments: commentId } });
        console.log('✅ Comment deleted:', {
            commentId,
            userId,
            deletedBy: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.role) === 'admin' ? 'admin' : 'owner'
        });
        res.json({
            success: true,
            message: 'Đã xóa comment thành công'
        });
    }
    catch (err) {
        console.error('❌ Error deleting comment:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});
exports.deleteComment = deleteComment;
// DELETE /posts/:id - Xóa bài viết
const deletePost = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { id } = req.params;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        const userRole = (_b = req.user) === null || _b === void 0 ? void 0 : _b.role;
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            return res.status(400).json({
                success: false,
                message: 'ID bài viết không hợp lệ'
            });
        }
        const post = yield Post_1.Post.findById(id);
        if (!post) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài viết'
            });
        }
        // Kiểm tra quyền xóa (chỉ chủ sở hữu hoặc admin)
        if (post.user.toString() !== (userId === null || userId === void 0 ? void 0 : userId.toString()) && userRole !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Bạn không có quyền xóa bài viết này'
            });
        }
        // Xóa tất cả comments của post này
        yield Comment_1.Comment.deleteMany({ post: id });
        // Xóa bài viết
        yield Post_1.Post.findByIdAndDelete(id);
        console.log('✅ Post deleted:', {
            postId: id,
            userId,
            deletedBy: userRole === 'admin' ? 'admin' : 'owner'
        });
        // Emit socket event
        if (req.io) {
            req.io.emit('post-deleted', {
                postId: id,
                deletedBy: userRole === 'admin' ? 'admin' : 'owner'
            });
        }
        res.json({
            success: true,
            message: 'Đã xóa bài viết thành công'
        });
    }
    catch (error) {
        console.error('❌ Error deleting post:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi xóa bài viết',
            error: error.message
        });
    }
});
exports.deletePost = deletePost;
// POST /posts/comments/:commentId/report - Báo cáo comment
const reportComment = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { commentId } = req.params;
        const { reason } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!mongoose_1.default.Types.ObjectId.isValid(commentId)) {
            return res.status(400).json({
                success: false,
                message: 'ID comment không hợp lệ'
            });
        }
        if (!reason || reason.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Lý do báo cáo là bắt buộc'
            });
        }
        const comment = yield Comment_1.Comment.findById(commentId);
        if (!comment) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy comment'
            });
        }
        // Import CommentReport model
        const { CommentReport } = yield Promise.resolve().then(() => __importStar(require('../models/CommentReport')));
        // Kiểm tra xem user đã báo cáo comment này chưa
        const existingReport = yield CommentReport.findOne({
            user: userId,
            comment: commentId
        });
        if (existingReport) {
            return res.status(400).json({
                success: false,
                message: 'Bạn đã báo cáo comment này rồi'
            });
        }
        // Tạo báo cáo mới
        const report = new CommentReport({
            user: userId,
            comment: commentId,
            post: comment.post,
            reason: reason.trim()
        });
        yield report.save();
        console.log('✅ Comment report created:', {
            reportId: report._id,
            commentId,
            userId,
            reason: reason.trim()
        });
        // Tạo thông báo cho admin (tương tự như báo cáo post)
        try {
            const { createCommentReportNotificationForAdmins } = yield Promise.resolve().then(() => __importStar(require('../services/notification.service')));
            const { User } = yield Promise.resolve().then(() => __importStar(require('../models/user.model')));
            const reporterUser = yield User.findById(userId).select('name');
            const reporterName = (reporterUser === null || reporterUser === void 0 ? void 0 : reporterUser.name) || 'Người dùng';
            yield createCommentReportNotificationForAdmins(report._id, comment._id, comment.post, reporterName, reason.trim());
            console.log('✅ Admin notification created for comment report');
        }
        catch (notificationError) {
            console.error('❌ Error creating comment report notification:', notificationError);
            // Don't fail the report if notification fails
        }
        res.json({
            success: true,
            message: 'Đã gửi báo cáo thành công'
        });
    }
    catch (err) {
        console.error('❌ Error reporting comment:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});
exports.reportComment = reportComment;
