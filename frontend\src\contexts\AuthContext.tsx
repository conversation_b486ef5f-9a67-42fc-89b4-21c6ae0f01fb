import React, { createContext, useContext, useState, useEffect } from 'react';
import api from '../services/api';

interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  role?: string;
  profilePicture?: string;
  address?: string;
  notificationPreferences?: any;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => Promise<void>;
  checkAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setUser(null);
        setLoading(false);
        return;
      }

      console.log('🔍 [Real Auth] Checking authentication with token:', token.substring(0, 20) + '...');

      // Call real API to get current user
      const response = await api.get('/api/auth/me');

      if (response.data.success) {
        const userData = response.data.data;
        const user: User = {
          id: userData.id,
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
          avatar: userData.avatar,
          role: userData.role,
          address: userData.address,
          notificationPreferences: userData.preferences
        };

        console.log('✅ [Real Auth] Authentication successful:', user);
        setUser(user);
      } else {
        console.log('❌ [Real Auth] Authentication failed');
        setUser(null);
        localStorage.removeItem('token');
      }

    } catch (error: any) {
      console.error('❌ [Real Auth] Auth check error:', error);
      if (error.response?.status === 401) {
        console.log('🔄 [Real Auth] Token expired, removing from storage');
        localStorage.removeItem('token');
        setUser(null);
      } else {
        // For other errors (network issues), don't clear user state immediately
        console.log('⚠️ [Real Auth] Network or other error, keeping user state');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    console.log('🔍 [AuthContext] Initial token check:', token ? 'Found' : 'Not found');
    if (token) {
      console.log('🔍 [AuthContext] Token found, calling checkAuth...');
      checkAuth();
    } else {
      console.log('🔍 [AuthContext] No token found, setting loading to false');
      setLoading(false);
    }
  }, []);



  const login = async (email: string, password: string) => {
    try {
      setError(null);
      console.log('🔐 [Real Auth] Login attempt:', email);

      // Call real API for login
      const response = await api.post('/api/auth/login', {
        email,
        password
      });

      if (response.data.success) {
        const { token, user: userData } = response.data.data;

        // Create user object
        const user: User = {
          id: userData.id,
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
          avatar: userData.avatar,
          role: userData.role,
          address: userData.address,
          notificationPreferences: userData.preferences
        };

        // Store token and user
        localStorage.setItem('token', token);
        console.log('💾 [Real Auth] Token stored in localStorage:', token.substring(0, 20) + '...');
        setUser(user);
        console.log('✅ [Real Auth] Login successful:', user);
        console.log('✅ [Real Auth] User state updated, loading:', loading);

        // Force a re-check to ensure everything is synced
        setTimeout(() => {
          console.log('🔄 [Real Auth] Force checking auth after login...');
          checkAuth();
        }, 100);
      } else {
        throw new Error(response.data.message || 'Đăng nhập thất bại');
      }

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Đăng nhập thất bại';
      setError(errorMessage);
      console.error('❌ [Real Auth] Login error:', error);
      throw new Error(errorMessage);
    }
  };

  const register = async (userData: any) => {
    try {
      setError(null);
      console.log('📝 [Real Auth] Register attempt:', userData.email);

      // Call real registration API
      const response = await api.post('/api/auth/register', {
        name: userData.name,
        email: userData.email,
        password: userData.password,
        phone: userData.phone || '',
        address: userData.address || ''
      });

      if (response.data.success) {
        const { token, user: apiUser } = response.data.data;

        // Store token and user data
        localStorage.setItem('token', token);
        setUser(apiUser);
        console.log('✅ [Real Auth] Registration successful:', apiUser);
      } else {
        throw new Error(response.data.message || 'Đăng ký thất bại');
      }

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Đăng ký thất bại';
      setError(errorMessage);
      console.error('❌ [Real Auth] Registration error:', error);
      throw new Error(errorMessage);
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    sessionStorage.removeItem('token');
    setUser(null);
    console.log('👋 [Mock Auth] User logged out');
  };

  const updateUser = async (userData: Partial<User>) => {
    try {
      setError(null);
      console.log('🔄 [Real Auth] Updating user:', userData);

      if (!user) {
        throw new Error('Không tìm thấy thông tin người dùng');
      }

      // Call real update API
      const response = await api.put('/api/auth/profile', userData);

      if (response.data.success) {
        const updatedUser = response.data.data;
        setUser(updatedUser);
        console.log('✅ [Real Auth] User updated successfully:', updatedUser);
        // Don't throw error on success!
        return updatedUser;
      } else {
        throw new Error(response.data.message || 'Cập nhật thông tin thất bại');
      }

    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Cập nhật thông tin thất bại';
      setError(errorMessage);
      console.error('❌ [Real Auth] Update error:', error);
      throw new Error(errorMessage);
    }
  };

  const value = {
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateUser,
    checkAuth
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};