import React, { useState, useEffect } from 'react';
import { 
  MessageCircle, 
  Heart, 
  Users, 
  Gift,
  Calendar,
  TrendingUp,
  Award
} from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'post' | 'donation' | 'event' | 'badge';
  title: string;
  description: string;
  date: string;
  amount?: number;
  icon: React.ReactNode;
  color: string;
}

interface UserActivityTimelineProps {
  userId: string;
  userStats: {
    totalPosts: number;
    totalDonations: number;
    totalEvents: number;
    totalDonationAmount: number;
  };
}

const UserActivityTimeline: React.FC<UserActivityTimelineProps> = ({ userId, userStats }) => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserActivities();
  }, [userId]);

  const fetchUserActivities = async () => {
    try {
      setLoading(true);
      
      // For now, create mock activities based on stats
      // In a real app, you'd fetch actual activity data from API
      const mockActivities: ActivityItem[] = [];

      // Add post activities
      if (userStats.totalPosts > 0) {
        mockActivities.push({
          id: 'posts',
          type: 'post',
          title: `Đã đăng ${userStats.totalPosts} bài viết`,
          description: 'Chia sẻ câu chuyện và kết nối với cộng đồng',
          date: new Date().toISOString(),
          icon: <MessageCircle className="w-5 h-5" />,
          color: 'bg-blue-100 text-blue-600'
        });
      }

      // Add donation activities
      if (userStats.totalDonations > 0) {
        mockActivities.push({
          id: 'donations',
          type: 'donation',
          title: `Đã ủng hộ ${userStats.totalDonations} lần`,
          description: `Tổng cộng ${formatCurrency(userStats.totalDonationAmount)}`,
          date: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
          amount: userStats.totalDonationAmount,
          icon: <Heart className="w-5 h-5" />,
          color: 'bg-red-100 text-red-600'
        });
      }

      // Add event activities
      if (userStats.totalEvents > 0) {
        mockActivities.push({
          id: 'events',
          type: 'event',
          title: `Tham gia ${userStats.totalEvents} sự kiện`,
          description: 'Hoạt động tình nguyện tích cực',
          date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
          icon: <Users className="w-5 h-5" />,
          color: 'bg-green-100 text-green-600'
        });
      }

      // Add badge activities
      if (userStats.totalDonations > 0 || userStats.totalEvents > 0) {
        mockActivities.push({
          id: 'badges',
          type: 'badge',
          title: 'Nhận huy hiệu mới',
          description: userStats.totalDonations > 0 ? 'Huy hiệu Người ủng hộ' : 'Huy hiệu Tình nguyện viên',
          date: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
          icon: <Award className="w-5 h-5" />,
          color: 'bg-yellow-100 text-yellow-600'
        });
      }

      // Add welcome activity
      mockActivities.push({
        id: 'welcome',
        type: 'badge',
        title: 'Tham gia cộng đồng',
        description: 'Chào mừng bạn đến với nền tảng thiện nguyện',
        date: new Date(Date.now() - 604800000).toISOString(), // 1 week ago
        icon: <TrendingUp className="w-5 h-5" />,
        color: 'bg-purple-100 text-purple-600'
      });

      setActivities(mockActivities);
    } catch (error) {
      console.error('Error fetching user activities:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M₫`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(0)}K₫`;
    }
    return new Intl.NumberFormat('vi-VN').format(amount) + '₫';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Hôm qua';
    if (diffDays < 7) return `${diffDays} ngày trước`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} tuần trước`;
    return date.toLocaleDateString('vi-VN');
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Hoạt động gần đây</h2>
      
      {activities.length > 0 ? (
        <div className="space-y-6">
          {activities.map((activity, index) => (
            <div key={activity.id} className="relative">
              {/* Timeline line */}
              {index < activities.length - 1 && (
                <div className="absolute left-5 top-12 w-0.5 h-16 bg-gray-200"></div>
              )}
              
              {/* Activity item */}
              <div className="flex items-start space-x-4">
                {/* Icon */}
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${activity.color} flex-shrink-0`}>
                  {activity.icon}
                </div>
                
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">{activity.title}</h3>
                    <span className="text-sm text-gray-500">{formatDate(activity.date)}</span>
                  </div>
                  <p className="text-gray-600 mt-1">{activity.description}</p>
                  
                  {/* Additional info for donations */}
                  {activity.type === 'donation' && activity.amount && (
                    <div className="mt-2 inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                      <Gift className="w-4 h-4 mr-1" />
                      {formatCurrency(activity.amount)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">Chưa có hoạt động nào</p>
          <p className="text-gray-400">Hãy bắt đầu tham gia các hoạt động thiện nguyện!</p>
        </div>
      )}
    </div>
  );
};

export default UserActivityTimeline;
