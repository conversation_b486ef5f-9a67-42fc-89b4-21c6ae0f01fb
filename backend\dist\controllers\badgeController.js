"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUsersWithBadge = exports.getBadgeStats = exports.testBadgeSystem = exports.getBadgeConfig = exports.checkUserBadges = exports.getPublicUserPrimaryBadge = exports.getPublicUserBadges = exports.getUserPrimaryBadgeController = exports.getUserBadgesController = void 0;
const badge_service_1 = require("../services/badge.service");
const UserBadge_1 = require("../models/UserBadge");
const mongoose_1 = __importDefault(require("mongoose"));
// Get user's badges
const getUserBadgesController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }
        const badges = yield (0, badge_service_1.getUserBadges)(userId);
        return res.json({
            success: true,
            data: badges
        });
    }
    catch (error) {
        console.error('Error getting user badges:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getUserBadgesController = getUserBadgesController;
// Get user's primary badge
const getUserPrimaryBadgeController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }
        const badge = yield (0, badge_service_1.getUserPrimaryBadge)(userId);
        return res.json({
            success: true,
            data: badge
        });
    }
    catch (error) {
        console.error('Error getting user primary badge:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getUserPrimaryBadgeController = getUserPrimaryBadgeController;
// Get another user's badges (public)
const getPublicUserBadges = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid user ID'
            });
        }
        const badges = yield (0, badge_service_1.getUserBadges)(new mongoose_1.default.Types.ObjectId(userId));
        return res.json({
            success: true,
            data: badges
        });
    }
    catch (error) {
        console.error('Error getting public user badges:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getPublicUserBadges = getPublicUserBadges;
// Get another user's primary badge (public)
const getPublicUserPrimaryBadge = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid user ID'
            });
        }
        const badge = yield (0, badge_service_1.getUserPrimaryBadge)(new mongoose_1.default.Types.ObjectId(userId));
        return res.json({
            success: true,
            data: badge
        });
    }
    catch (error) {
        console.error('Error getting public user primary badge:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getPublicUserPrimaryBadge = getPublicUserPrimaryBadge;
// Manually check all badges for current user
const checkUserBadges = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized'
            });
        }
        const earnedBadges = yield (0, badge_service_1.checkAllBadges)(userId);
        return res.json({
            success: true,
            message: 'Badges checked successfully',
            data: earnedBadges
        });
    }
    catch (error) {
        console.error('Error checking user badges:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.checkUserBadges = checkUserBadges;
// Get badge configuration (public)
const getBadgeConfig = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return res.json({
            success: true,
            data: UserBadge_1.BADGE_CONFIG
        });
    }
    catch (error) {
        console.error('Error getting badge config:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getBadgeConfig = getBadgeConfig;
// Test endpoint to debug badge system
const testBadgeSystem = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('🧪 [Badge Test] Testing badge system...');
        // Test badge config
        console.log('📋 [Badge Test] Badge config:', Object.keys(UserBadge_1.BADGE_CONFIG));
        // Test UserBadge model
        const badgeCount = yield UserBadge_1.UserBadge.countDocuments();
        console.log('🏆 [Badge Test] Total badges in database:', badgeCount);
        // Test sample badge creation (if no badges exist)
        if (badgeCount === 0) {
            console.log('🆕 [Badge Test] No badges found, creating sample badge...');
            // Create a sample badge for testing
            const sampleBadge = new UserBadge_1.UserBadge({
                userId: new mongoose_1.default.Types.ObjectId(),
                badgeType: 'supporter',
                badgeLevel: 1,
                totalDonations: 1
            });
            yield sampleBadge.save();
            console.log('✅ [Badge Test] Sample badge created');
        }
        // Test badge service functions
        const { getUserBadges, getUserPrimaryBadge } = yield Promise.resolve().then(() => __importStar(require('../services/badge.service')));
        console.log('📦 [Badge Test] Badge service functions imported successfully');
        return res.json({
            success: true,
            message: 'Badge system test completed',
            data: {
                badgeConfigKeys: Object.keys(UserBadge_1.BADGE_CONFIG),
                totalBadgesInDB: badgeCount,
                serviceImported: true,
                timestamp: new Date().toISOString()
            }
        });
    }
    catch (error) {
        console.error('❌ [Badge Test] Error testing badge system:', error);
        return res.status(500).json({
            success: false,
            message: error.message,
            stack: error.stack
        });
    }
});
exports.testBadgeSystem = testBadgeSystem;
// Admin: Get all badges statistics
const getBadgeStats = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin only.'
            });
        }
        const stats = yield UserBadge_1.UserBadge.aggregate([
            {
                $group: {
                    _id: {
                        badgeType: '$badgeType',
                        badgeLevel: '$badgeLevel'
                    },
                    count: { $sum: 1 },
                    totalDonations: { $sum: '$totalDonations' },
                    totalEvents: { $sum: '$totalEvents' }
                }
            },
            {
                $sort: {
                    '_id.badgeType': 1,
                    '_id.badgeLevel': 1
                }
            }
        ]);
        const totalBadges = yield UserBadge_1.UserBadge.countDocuments({ isActive: true });
        const uniqueUsers = yield UserBadge_1.UserBadge.distinct('userId', { isActive: true });
        return res.json({
            success: true,
            data: {
                totalBadges,
                uniqueUsers: uniqueUsers.length,
                badgeDistribution: stats
            }
        });
    }
    catch (error) {
        console.error('Error getting badge stats:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getBadgeStats = getBadgeStats;
// Admin: Get users with specific badge
const getUsersWithBadge = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin only.'
            });
        }
        const { badgeType, badgeLevel } = req.query;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const skip = (page - 1) * limit;
        const filter = { isActive: true };
        if (badgeType)
            filter.badgeType = badgeType;
        if (badgeLevel)
            filter.badgeLevel = parseInt(badgeLevel);
        const [badges, total] = yield Promise.all([
            UserBadge_1.UserBadge.find(filter)
                .populate('userId', 'name email')
                .sort({ badgeLevel: -1, earnedAt: -1 })
                .skip(skip)
                .limit(limit),
            UserBadge_1.UserBadge.countDocuments(filter)
        ]);
        return res.json({
            success: true,
            data: badges,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error('Error getting users with badge:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getUsersWithBadge = getUsersWithBadge;
