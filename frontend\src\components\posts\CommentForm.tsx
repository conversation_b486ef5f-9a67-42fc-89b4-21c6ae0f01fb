import React, { useState } from 'react';

interface CommentFormProps {
  onComment: (content: string) => void;
}

const CommentForm: React.FC<CommentFormProps> = ({ onComment }) => {
  const [content, setContent] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim()) return;
    onComment(content);
    setContent('');
  };

  return (
    <form onSubmit={handleSubmit} className="flex gap-2 mt-2">
      <input
        id="comment-input"
        name="comment"
        type="text"
        className="flex-1 border rounded p-2"
        placeholder="Viết bình luận..."
        value={content}
        onChange={e => setContent(e.target.value)}
      />
      <button type="submit" className="px-4 py-2 bg-blue-500 text-white rounded">Gửi</button>
    </form>
  );
};

export default CommentForm; 