import React, { useState, useEffect } from 'react';
import axios from 'axios';
import UserBadge from './UserBadge';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface Badge {
  _id: string;
  badgeType: string;
  badgeLevel: number;
  earnedAt: string;
  config: {
    name: string;
    icon: string;
    color: string;
    levels: {
      [key: number]: {
        name: string;
        [key: string]: any;
      };
    };
  };
  levelName: string;
}

interface PrimaryBadgeProps {
  userId?: string; // If provided, show badge for this user (public view)
  size?: 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  className?: string;
}

const PrimaryBadge: React.FC<PrimaryBadgeProps> = ({ 
  userId, 
  size = 'sm',
  showTooltip = true,
  className = ''
}) => {
  const [badge, setBadge] = useState<Badge | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPrimaryBadge();
  }, [userId]);

  const fetchPrimaryBadge = async () => {
    try {
      setLoading(true);

      let url: string;
      const headers: any = {};

      if (userId) {
        // Public view - get primary badge for specific user
        url = `${API_URL}/api/badges/user/${userId}/primary`;
      } else {
        // Private view - get current user's primary badge
        url = `${API_URL}/api/badges/my-primary`;
        const token = localStorage.getItem('token');
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        }
      }

      const response = await axios.get(url, { headers });

      if (response.data.success && response.data.data) {
        setBadge(response.data.data);
      } else {
        setBadge(null);
      }
    } catch (err: any) {
      // Handle 404 gracefully - user simply doesn't have badges yet
      if (err.response?.status === 404) {
        console.log('No badges found for user - this is normal for new users');
        setBadge(null);
      } else {
        console.error('Error fetching primary badge:', err);
        setBadge(null);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`animate-pulse bg-gray-200 rounded-full h-5 w-12 ${className}`}></div>
    );
  }

  if (!badge) {
    return null;
  }

  return (
    <div className={className}>
      <UserBadge
        badge={badge}
        size={size}
        showTooltip={showTooltip}
      />
    </div>
  );
};

export default PrimaryBadge;
