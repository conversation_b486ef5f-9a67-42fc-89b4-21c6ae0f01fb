import React from 'react';

interface BadgeConfig {
  name: string;
  icon: string;
  color: string;
  levels: {
    [key: number]: {
      name: string;
      [key: string]: any;
    };
  };
}

interface UserBadgeProps {
  badge: {
    badgeType: string;
    badgeLevel: number;
    earnedAt: string;
    config: BadgeConfig;
    levelName: string;
  };
  size?: 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  className?: string;
}

const UserBadge: React.FC<UserBadgeProps> = ({ 
  badge, 
  size = 'md', 
  showTooltip = true,
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-sm px-2 py-1',
    lg: 'text-base px-3 py-1.5'
  };

  const iconSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const badgeElement = (
    <span
      className={`
        inline-flex items-center gap-1 rounded-full font-medium
        ${sizeClasses[size]}
        ${className}
      `}
      style={{ 
        backgroundColor: `${badge.config.color}20`,
        color: badge.config.color,
        border: `1px solid ${badge.config.color}40`
      }}
    >
      <span className={iconSizes[size]}>{badge.config.icon}</span>
      <span className="font-semibold">{badge.levelName}</span>
    </span>
  );

  if (!showTooltip) {
    return badgeElement;
  }

  return (
    <div className="relative group">
      {badgeElement}
      
      {/* Tooltip */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
        <div className="font-semibold">{badge.config.icon} {badge.config.name}</div>
        <div className="text-gray-300">{badge.levelName}</div>
        <div className="text-gray-400 text-xs">
          Đạt được: {new Date(badge.earnedAt).toLocaleDateString('vi-VN')}
        </div>
        
        {/* Arrow */}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
      </div>
    </div>
  );
};

export default UserBadge;
